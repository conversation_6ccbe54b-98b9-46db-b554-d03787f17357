<?xml version="1.0" encoding="utf-8"?>
<edmx:Edmx Version="3.0" xmlns:edmx="http://schemas.microsoft.com/ado/2009/11/edmx">
 <!-- EF Designer content (DO NOT EDIT MANUALLY BELOW HERE) -->
  <edmx:Designer xmlns="http://schemas.microsoft.com/ado/2009/11/edmx">
    <!-- Diagram content (shape and connector positions) -->
    <edmx:Diagrams>
      <Diagram DiagramId="2850a830dd71487b9f23fb99ea693745" Name="Diagram1">
        <EntityTypeShape EntityType="ServITModel.action_type" Width="1.5" PointX="3" PointY="82.375" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.Active_permissions" Width="1.5" PointX="8.25" PointY="55.375" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.asset" Width="1.5" PointX="11" PointY="1" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.assets_category" Width="1.5" PointX="8.75" PointY="1.375" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.assets_depreciations" Width="1.5" PointX="13.25" PointY="1.375" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.attendance_departure" Width="1.5" PointX="8.25" PointY="9.75" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.attendance_state" Width="1.5" PointX="6" PointY="1.125" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.bank" Width="1.5" PointX="3.75" PointY="75.375" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.bank_account" Width="1.5" PointX="6" PointY="76.875" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.bank_action" Width="1.5" PointX="8.25" PointY="58.875" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.bank_branch" Width="1.5" PointX="3.75" PointY="78.125" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.category" Width="1.5" PointX="3" PointY="59.875" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.company" Width="1.5" PointX="10.75" PointY="4.75" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.cost_center" Width="1.5" PointX="0.75" PointY="34.625" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.customer_vendor" Width="1.5" PointX="3" PointY="39.25" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.customer_initial_balance" Width="1.5" PointX="5.25" PointY="35.125" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.customer_kind" Width="1.5" PointX="0.75" PointY="39.625" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.Customers_Transactions" Width="1.5" PointX="10.75" PointY="9.75" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.delivery" Width="1.5" PointX="3" PointY="45.75" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.document_type" Width="1.5" PointX="3" PointY="84.75" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.employee" Width="1.5" PointX="3" PointY="7.875" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.employee_activity" Width="1.5" PointX="5.25" PointY="9.875" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.employee_borrowing_payments" Width="1.5" PointX="7.5" PointY="13.5" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.employee_borrowings" Width="1.5" PointX="5.25" PointY="13.125" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.employee_payroll_header" Width="1.5" PointX="5.25" PointY="16.875" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.employee_salary" Width="1.5" PointX="5.25" PointY="6.875" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.employee_vacations" Width="1.5" PointX="8.25" PointY="5.5" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.expens" Width="1.5" PointX="9" PointY="66.625" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.hall" Width="1.5" PointX="0.75" PointY="28.75" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.hall_tables" Width="1.5" PointX="3" PointY="28.5" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.Installment_paids" Width="1.5" PointX="8.25" PointY="73.625" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.invoice_detail" Width="1.5" PointX="13.5" PointY="53.625" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.invoice_header" Width="1.5" PointX="5.25" PointY="41.875" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.invoice_kind" Width="1.5" PointX="3" PointY="31.375" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.job" Width="1.5" PointX="0.75" PointY="11" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.kitchen_products" Width="1.5" PointX="0.75" PointY="0.75" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.kitchen" Width="1.5" PointX="2.75" PointY="0.75" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.maintenance" Width="1.5" PointX="16.25" PointY="56" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.maintenance_engineers" Width="1.5" PointX="14" PointY="63.125" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.maintenance_workshops" Width="1.5" PointX="14" PointY="66" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.management" Width="1.5" PointX="0.75" PointY="7.375" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.manufacture_order_Finished_detail" Width="1.5" PointX="10.5" PointY="44.375" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.manufacture_order_header" Width="1.5" PointX="3" PointY="34.5" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.manufacture_order_Raw_detail" Width="1.5" PointX="10.5" PointY="40.25" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.manufacture_order_type" Width="1.5" PointX="0.75" PointY="32.25" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.order_status" Width="1.5" PointX="3" PointY="63.75" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.order_type" Width="1.5" PointX="3" PointY="66.125" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.paper" Width="1.5" PointX="6" PointY="68.875" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.payment" Width="1.5" PointX="3" PointY="68.5" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.product" Width="1.5" PointX="5.25" PointY="52.75" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.product_collected" Width="1.5" PointX="7.5" PointY="62.75" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.product_cost_policy" Width="1.5" PointX="12.75" PointY="4.75" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.product_initial_balance_detail" Width="1.5" PointX="13.5" PointY="59" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.product_partitioning_header" Width="1.5" PointX="8.25" PointY="42.375" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.product_partitioning_inputs" Width="1.5" PointX="13.5" PointY="49.25" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.product_partitioning_outputs" Width="1.5" PointX="13.5" PointY="45.25" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.product_unit" Width="1.5" PointX="10.5" PointY="60.75" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.revenues_items" Width="1.5" PointX="9" PointY="69.5" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.salary_items" Width="1.5" PointX="3" PointY="3.5" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.salary_Items_type" Width="1.5" PointX="0.75" PointY="4.125" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.sale_price_change_alert" Width="1.5" PointX="12.75" PointY="7.75" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.sales_representative" Width="1.5" PointX="0.75" PointY="41.75" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.stock" Width="1.5" PointX="3" PointY="49.5" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.stock_permission_detail" Width="1.5" PointX="16.5" PointY="51.875" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.stock_permission_header" Width="1.5" PointX="8.25" PointY="47.375" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.stock_permission_kind" Width="1.5" PointX="6" PointY="38.625" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.treasury" Width="1.5" PointX="3" PointY="70.875" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.treasury_initial_balance" Width="1.5" PointX="5.25" PointY="85.5" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.treasury_permission" Width="1.5" PointX="11.25" PointY="53.75" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.treasury_permssion_kind" Width="1.5" PointX="9" PointY="52.5" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.unit" Width="1.5" PointX="8.25" PointY="39.125" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.user" Width="1.5" PointX="3" PointY="54.25" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.user_log" Width="1.5" PointX="5.25" PointY="61" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.users_permissions" Width="1.5" PointX="6" PointY="66.625" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.vacations_type" Width="1.5" PointX="6" PointY="4.125" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.work_periods" Width="1.5" PointX="0.75" PointY="14.125" IsExpanded="true" />
        <EntityTypeShape EntityType="ServITModel.employee_payroll_details" Width="1.5" PointX="7.5" PointY="17.375" IsExpanded="true" />
        <AssociationConnector Association="ServITModel.FK_user_log_action_type" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_Active_permissions_user" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_Active_permissions_users_permissions" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_assets_assets_category" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_assets_depreciations_assets" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_attendance_departure_attendance_state" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_attendance_departure_employee" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_bank_account_bank" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_bank_branch_bank" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_paper_bank" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_bank_account_bank_branch" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_bank_action_bank_account" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_Installment_paids_bank_account" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_bank_action_user" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_paper_bank_branch" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_product_category" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_invoice_header_cost_center" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_manufacture_order_header_cost_center" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_product_partitioning_header_cost_center" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_stock_cost_center" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_stock_permission_header_cost_center" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_user_cost_center" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_customer_vendor_customer_kind" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_customer_vendor_sales_representative" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_customer_initial_balance_customer_vendor" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_invoice_header_customer_vendor" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_maintenance_customer_vendor" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_paper_customer_vendor" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_treasury_permission_customer_vendor" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_invoice_header_delivery" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_user_log_document_type" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_employee_activity_employee" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_employee_borrowings_employee" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_employee_jobs" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_employee_management" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_employee_payroll_header_employee" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_employee_salary_employee" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_employee_vacations_employee" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_employee_work_periods" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_employee_borrowing_payments_employee_borrowings" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_employee_payroll_details_employee_payroll_header" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_employee_salary_salary_items" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_employee_vacations_vacations_type" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_treasury_permission_expenses" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_hall_tables_hall" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_invoice_header_hall_tables" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_Installment_paids_treasury" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_invoice_detail_invoice_header" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_invoice_detail_product" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_invoice_detail_stock" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_invoice_header_invoice_kind" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_invoice_header_order_status" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_invoice_header_order_type" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_invoice_header_payment" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_invoice_header_sales_representative" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_invoice_header_treasury" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_invoice_header_user" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_maintenance_maintenance_engineers" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_maintenance_maintenance_engineers1" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_maintenance_maintenance_workshops" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_maintenance_treasury" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_manufacture_order_detail_product" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_manufacture_order_Finished_detail_manufacture_order_header" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_manufacture_order_Finished_detail_unit" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_manufacture_order_header_manufacture_order_type" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_manufacture_order_Raw_detail_manufacture_order_header" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_manufacture_order_Raw_detail_product" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_manufacture_order_Raw_detail_unit" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_pro_unit_product" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_product_collected_product" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_product_collected_product1" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_product_initial_balance_detail_product" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_product_partitioning_inputs_product" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_product_partitioning_outputs_product" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_product_stock" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_product_unit_product" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_product_user" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_stock_permission_detail_product" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_product_initial_balance_detail_stock" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_product_partitioning_header_stock" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_product_partitioning_header_stock1" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_product_partitioning_inputs_product_partitioning_header" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_product_partitioning_outputs_product_partitioning_header" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_pro_unit_unit" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_product_unit_unit" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_treasury_permission_revenues_items" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_employee_payroll_details_salary_items" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_salary_items_salary_Items_type" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_stock_permission_header_stock" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_stock_permission_detail_stock_permission_header" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_stock_permission_header_stock_permission_kind" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_stock_permission_header_user" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_stock_permission_permission_kind" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_treasury_initial_balance_treasury" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_treasury_permission_treasury" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_treasury_permission_treasury_permssion_kind" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_treasury_permission_user" ManuallyRouted="false" />
        <AssociationConnector Association="ServITModel.FK_user_log_user" ManuallyRouted="false" />
      </Diagram>
    </edmx:Diagrams>
  </edmx:Designer>
</edmx:Edmx>