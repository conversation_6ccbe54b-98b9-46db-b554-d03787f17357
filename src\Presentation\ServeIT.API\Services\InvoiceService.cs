using AutoMapper;
using ServeIT.Database.UnitOfWork;
using ServeIT.Models.DTOs;
using ServeIT.Models.Common;
using ServeIT.Models.Entities;
using ServeIT.Models.Enums;

namespace ServeIT.API.Services;

public class InvoiceService : IInvoiceService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;

    public InvoiceService(IUnitOfWork unitOfWork, IMapper mapper)
    {
        _unitOfWork = unitOfWork;
        _mapper = mapper;
    }

    public async Task<ServiceResult<IEnumerable<InvoiceDto>>> GetAllInvoicesAsync()
    {
        try
        {
            var invoices = await _unitOfWork.Invoices.GetAllAsync();
            var invoiceDtos = _mapper.Map<IEnumerable<InvoiceDto>>(invoices);
            return ServiceResult<IEnumerable<InvoiceDto>>.Success(invoiceDtos);
        }
        catch (Exception ex)
        {
            return ServiceResult<IEnumerable<InvoiceDto>>.Failure($"Error retrieving invoices: {ex.Message}");
        }
    }

    public async Task<ServiceResult<InvoiceDto>> GetInvoiceByIdAsync(string id)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(id))
            {
                return ServiceResult<InvoiceDto>.Failure("Invoice ID is required");
            }

            var invoice = await _unitOfWork.Invoices.GetWithDetailsAsync(id);
            if (invoice == null)
            {
                return ServiceResult<InvoiceDto>.Failure("Invoice not found");
            }

            var invoiceDto = _mapper.Map<InvoiceDto>(invoice);
            return ServiceResult<InvoiceDto>.Success(invoiceDto);
        }
        catch (Exception ex)
        {
            return ServiceResult<InvoiceDto>.Failure($"Error retrieving invoice: {ex.Message}");
        }
    }

    public async Task<ServiceResult<PagedResult<InvoiceDto>>> GetInvoicesPagedAsync(PagingParameters parameters)
    {
        try
        {
            var pagedInvoices = await _unitOfWork.Invoices.GetPagedAsync(parameters);
            var invoiceDtos = _mapper.Map<IEnumerable<InvoiceDto>>(pagedInvoices.Items);

            var pagedResult = new PagedResult<InvoiceDto>(
                invoiceDtos,
                pagedInvoices.TotalCount,
                pagedInvoices.PageNumber,
                pagedInvoices.PageSize);

            return ServiceResult<PagedResult<InvoiceDto>>.Success(pagedResult);
        }
        catch (Exception ex)
        {
            return ServiceResult<PagedResult<InvoiceDto>>.Failure($"Error retrieving invoices: {ex.Message}");
        }
    }

    public async Task<ServiceResult<InvoiceDto>> CreateInvoiceAsync(InvoiceDto invoiceDto)
    {
        try
        {
            if (invoiceDto == null)
            {
                return ServiceResult<InvoiceDto>.Failure("Invoice data is required");
            }

            var invoice = _mapper.Map<Invoice>(invoiceDto);

            // Generate invoice number if not provided
            if (string.IsNullOrEmpty(invoice.Id))
            {
                invoice.Id = await _unitOfWork.Invoices.GetNextInvoiceNumberAsync(invoice.InvoiceKind);
            }

            invoice.CreatedAt = DateTime.UtcNow;
            invoice.IsActive = true;
            invoice.InvoiceDate = DateTime.UtcNow;

            // Calculate totals from invoice details
            if (invoice.InvoiceDetails?.Any() == true)
            {
                invoice.SubTotal = invoice.InvoiceDetails.Sum(d => d.Quantity * d.UnitPrice);
                invoice.TotalVAT = invoice.InvoiceDetails.Sum(d => d.VATAmount);
                invoice.TotalAmount = invoice.SubTotal + invoice.TotalVAT - invoice.DiscountAmount;
            }

            await _unitOfWork.Invoices.AddAsync(invoice);
            await _unitOfWork.SaveChangesAsync();

            var createdInvoiceDto = _mapper.Map<InvoiceDto>(invoice);
            return ServiceResult<InvoiceDto>.Success(createdInvoiceDto);
        }
        catch (Exception ex)
        {
            return ServiceResult<InvoiceDto>.Failure($"Error creating invoice: {ex.Message}");
        }
    }

    public async Task<ServiceResult<InvoiceDto>> UpdateInvoiceAsync(string id, InvoiceDto invoiceDto)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(id))
            {
                return ServiceResult<InvoiceDto>.Failure("Invoice ID is required");
            }

            if (invoiceDto == null)
            {
                return ServiceResult<InvoiceDto>.Failure("Invoice data is required");
            }

            var existingInvoice = await _unitOfWork.Invoices.GetWithDetailsAsync(id);
            if (existingInvoice == null)
            {
                return ServiceResult<InvoiceDto>.Failure("Invoice not found");
            }

            // Update properties
            _mapper.Map(invoiceDto, existingInvoice);
            existingInvoice.UpdatedAt = DateTime.UtcNow;

            // Recalculate totals
            if (existingInvoice.InvoiceDetails?.Any() == true)
            {
                existingInvoice.SubTotal = existingInvoice.InvoiceDetails.Sum(d => d.Quantity * d.UnitPrice);
                existingInvoice.TotalVAT = existingInvoice.InvoiceDetails.Sum(d => d.VATAmount);
                existingInvoice.TotalAmount = existingInvoice.SubTotal + existingInvoice.TotalVAT - existingInvoice.DiscountAmount;
            }

            _unitOfWork.Invoices.Update(existingInvoice);
            await _unitOfWork.SaveChangesAsync();

            var updatedInvoiceDto = _mapper.Map<InvoiceDto>(existingInvoice);
            return ServiceResult<InvoiceDto>.Success(updatedInvoiceDto);
        }
        catch (Exception ex)
        {
            return ServiceResult<InvoiceDto>.Failure($"Error updating invoice: {ex.Message}");
        }
    }

    public async Task<ServiceResult> DeleteInvoiceAsync(string id)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(id))
            {
                return ServiceResult.Failure("Invoice ID is required");
            }

            var invoice = await _unitOfWork.Invoices.GetByIdAsync(id);
            if (invoice == null)
            {
                return ServiceResult.Failure("Invoice not found");
            }

            // Soft delete
            invoice.IsActive = false;
            invoice.UpdatedAt = DateTime.UtcNow;

            _unitOfWork.Invoices.Update(invoice);
            await _unitOfWork.SaveChangesAsync();

            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            return ServiceResult.Failure($"Error deleting invoice: {ex.Message}");
        }
    }
}
