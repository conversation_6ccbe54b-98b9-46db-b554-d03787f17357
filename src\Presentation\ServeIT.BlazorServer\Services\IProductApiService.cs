using ServeIT.Models.DTOs;
using ServeIT.Models.Common;

namespace ServeIT.BlazorServer.Services;

public interface IProductApiService
{
    Task<ServiceResult<IEnumerable<ProductDto>>> GetAllProductsAsync();
    Task<ServiceResult<ProductDto>> GetProductByIdAsync(string id);
    Task<ServiceResult<PagedResult<ProductDto>>> GetProductsPagedAsync(PagingParameters parameters);
    Task<ServiceResult<ProductDto>> CreateProductAsync(ProductDto product);
    Task<ServiceResult<ProductDto>> UpdateProductAsync(string id, ProductDto product);
    Task<ServiceResult> DeleteProductAsync(string id);
}
