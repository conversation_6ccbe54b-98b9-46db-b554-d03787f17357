﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;

namespace ServeIt.BL
{
    class AssetsCategoryBL
    {
        public string  AssetsCategoryID{get;set;}
        public string AssetsCategoryParent { get; set; }
        public string AssetsCategoryName { get; set; }
        public string Description { get; set; }
        public string UserName { get; set; }

        public string AddOrUpdateAssetCategory(AssetsCategoryBL aset_cat_bl)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[6];
                para[0] = new SqlParameter("@check","a");
                para[1] = new SqlParameter("@asset_cat_id",aset_cat_bl.AssetsCategoryID);
                para[2] = new SqlParameter("@parent_id",aset_cat_bl.AssetsCategoryParent);
                para[3] = new SqlParameter("@asset_cat_name",aset_cat_bl.AssetsCategoryName);
                para[4] = new SqlParameter("@description",aset_cat_bl.Description);
                para[5] = new SqlParameter("@user_name",aset_cat_bl.UserName);
                return DAL.InsUpdDel("ManageAssetsCategory", para);
            }
        }

        public string DeleteAssetsCategory(string AssetCategoryID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "d");
                para[1] = new SqlParameter("@asset_cat_id", AssetsCategoryID);
                return DAL.InsUpdDel("ManageAssetsCategory", para);
            }
        }

        public string GetMaxAssetCategoryID()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@check", "m");
                return DAL.GetValue("ManageAssetsCategory", para);
            }
        }

        public DataTable GetAssetCategoryByID(string AssetCategoryID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "g");
                para[1] = new SqlParameter("@asset_cat_id", AssetCategoryID);

                return DAL.GetData("ManageAssetsCategory", para);
            }
        }

        public DataTable SearchAssetCategory(string AssetCategoryName)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "s");
                para[1] = new SqlParameter("@asset_cat_name", AssetCategoryName);

                return DAL.GetData("ManageAssetsCategory", para);
            }
        }

        public DataTable GetAssetCategoryNodes(string AssetCategoryParentID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "p");
                para[1] = new SqlParameter("@parent_id", AssetCategoryParentID);
                return DAL.GetData("ManageAssetsCategory", para);
            }
        }
    }
}
