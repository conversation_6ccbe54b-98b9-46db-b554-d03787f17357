﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;

namespace ServeIt.BL
{
    class MaintenanceWorkShopBL
    {
        public string WorkShopID { get; set; }
        public string WorkShopName { get; set; }
        public string WorkShopAddress { get; set; }
        public bool WorkShopIsActive { get; set; }

        public string GetMaxMaintenanceWorkShop()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetValue("GetMaxMaintenanceWorkshop");
            }
        }

        public string AddOrUpdateMaintenanceWorkShop(MaintenanceWorkShopBL maint_wkshop)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[5];
                para[0] = new SqlParameter("@check", "a");
                para[1] = new SqlParameter("@workshop_id", maint_wkshop.WorkShopID);
                para[2] = new SqlParameter("@workshop_name", maint_wkshop.WorkShopName);
                para[3] = new SqlParameter("@workshop_address", maint_wkshop.WorkShopAddress);
                para[4] = new SqlParameter("@is_active", maint_wkshop.WorkShopIsActive);
                return DAL.InsUpdDel("ManageMaintenanceWorkShops", para);
            }
        }

        public string DeleteMaintenanceWorkShop(string WorkShopID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "d");
                para[1] = new SqlParameter("@workshop_id", WorkShopID);
                return DAL.InsUpdDel("ManageMaintenanceWorkShops", para);
            }
        }

        public DataTable SearchMaintenanceWorkshop(string WorkShopName)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@workshop_name", WorkShopName);
                return DAL.GetData("SearchMaintenanceWorkshop", para);
            }
        }


        public DataTable GetAllMaintenanceWorkShops()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetData("GetAllMaintenanceWorkShops", null);
            }
        }
    }
}
