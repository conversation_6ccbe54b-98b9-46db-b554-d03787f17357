﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ServeIt.BL
{
    class TreasuryBL
    {


        public DataTable GetAllTreasuries()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetData("getalltreasuries", null);
            }
        }


        public DataTable GetTreasuryByName(BE.TreasuryBE treabe) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@name", treabe.Name);
                return DAL.GetData("gettreasurybyname", para);
            }
        }


        public string AddOrUpdateTreasury(BE.TreasuryBE treabe)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[8];
                para[0] = new SqlParameter("@check", "a");
                para[1] = new SqlParameter("@treasury_id", treabe.ID);
                para[2] = new SqlParameter("@cost_center_id", treabe.CostCenterID);
                para[3] = new SqlParameter("@treasury_name", treabe.Name);
                para[4] = new SqlParameter("@adding_date", treabe.Adding_Date);
                para[5] = new SqlParameter("@intial_balance", treabe.InitialBalance);
                para[6] = new SqlParameter("@location", treabe.Location);
                para[7] = new SqlParameter("@user_name", treabe.User_Name);
                return DAL.InsUpdDel("managetreasury", para);
            }
        }



        public string DeleteTreasury(BE.TreasuryBE treabe)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[3];
                para[0] = new SqlParameter("@check", "d");
                para[1] = new SqlParameter("@treasury_id", treabe.ID);
                para[2] = new SqlParameter("@user_name", treabe.User_Name);
                return DAL.InsUpdDel("managetreasury", para);
            }
        }

        public DataTable GetPreviousTreasuryID(string CurrentID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@current_id", CurrentID);
                return DAL.GetData("GetPreviousTreasuryID", para);
            }
        }

        public DataTable GetNextTreasuryID(string CurrentID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@current_id", CurrentID);
                return DAL.GetData("GetNextTreasuryID", para);
            }
        }


        public string GetMaxTreasuryId() 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetValue("getmaxtreasuryid");
            }
        }



        #region Manage Reports

        public DataTable GetTreasutyMovement(BE.TreasuryBE treasbe) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[3];
                para[0] = new SqlParameter("@treas_id", treasbe.ID);
                para[1] = new SqlParameter("@datefrom", treasbe.DateFrom);
                para[2] = new SqlParameter("@dateto", treasbe.DateTo);
                return DAL.GetData("RPTGetTreasutyMovement", para);
            }
        }


        public DataTable GetTreasuryBalance(BE.TreasuryBE treasbe)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[3];
                para[0] = new SqlParameter("@treas_id", treasbe.ID);
                para[1] = new SqlParameter("@datefrom", treasbe.DateFrom);
                para[2] = new SqlParameter("@dateto", treasbe.DateTo);
                return DAL.GetData("RPTGetTreasuryBalance", para);
            }
        }


        #endregion

    }
}
