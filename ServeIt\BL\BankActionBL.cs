﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ServeIt.BL
{
    class BankActionBL
    {
        public string AddOrUpdateBankAction(BE.BankActionBE actionbe) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[9];
                para[0] = new SqlParameter("@check", "a");
                para[1] = new SqlParameter("@action_id", actionbe.Action_Id);
                para[2] = new SqlParameter("@account_id", actionbe.Account_Id);
                para[3] = new SqlParameter("@receipt_no", actionbe.Receipt_No);
                para[4] = new SqlParameter("@action_type", actionbe.Action_Type);
                para[5] = new SqlParameter("@date", actionbe.Date);
                para[6] = new SqlParameter("@value", actionbe.Value);
                para[7] = new SqlParameter("@notes", actionbe.Notes);
                para[8] = new SqlParameter("@user_name", actionbe.User_Name);
                return DAL.InsUpdDel("ManageBankActions", para);
            }
        }

        public string DeleteBankAction(int action_id)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "d");
                para[1] = new SqlParameter("@action_id", action_id);
                return DAL.InsUpdDel("ManageBankActions", para);
            }
        }

        public DataTable SearchBankAction(BE.BankActionBE actionbe) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[5];
                para[0] = new SqlParameter("@action_type", actionbe.Action_Type);
                para[1] = new SqlParameter("@receipt_no", actionbe.Receipt_No);
                para[2] = new SqlParameter("@account_id", actionbe.Account_Id);
                para[3] = new SqlParameter("@datefrom", actionbe.Date_From);
                para[4] = new SqlParameter("@dateto", actionbe.Date_To);
                return DAL.GetData("SearchBankActions", para);
            }
        }

        public string GetMaxBankActionId() 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetValue("GetMaxBankActionId");
            }
        }

        public DataTable GetBankAccountMovement(BE.BankActionBE bankactionbe) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[3];
                para[0] = new SqlParameter("@bank_account_id", bankactionbe.Account_Id);
                para[1] = new SqlParameter("@datefrom", bankactionbe.Date_From);
                para[2] = new SqlParameter("@dateto", bankactionbe.Date_To);
                return DAL.GetData("RPTGetBankAccountMovement", para);
            }
        }

        public DataTable GetBankAccountBalance(BE.BankActionBE bankactionbe) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[4];
                para[0] = new SqlParameter("@account_id",bankactionbe.Account_Id);
                para[1] = new SqlParameter("@zero_accounts",bankactionbe.Zero_Accounts);
                para[2] = new SqlParameter("@datefrom",bankactionbe.Date_From);
                para[3] = new SqlParameter("@dateto",bankactionbe.Date_To);
                return DAL.GetData("RPTGetBankAccountBalance", para);
            }
        }

        public DataTable GetBankActionByActionId(int action_id) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@action_id", action_id);
                return DAL.GetData("GetBankActionByActionID", para);
            }
        }

    }
}
