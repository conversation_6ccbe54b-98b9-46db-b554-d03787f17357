﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ServeIt.BE
{
    class CompanyDefinition
    {
        // Properties
        #region Company Definition Properties

        public string Id { set; get; }
        public string Name { set; get; }
        public string Activity { set; get; }
        public DateTime FirstPeriodDate { get; set; }
        public DateTime LastPeriodDate { get; set; }
        public string Address { set; get; }
        public string Commercial_Registration { get; set; }
        public string Tax_Card { get; set; }
        public string Phone { set; get; }
        public string Mobile { set; get; }
        public string Email { set; get; }
        public byte[] Logo { get; set; }
        public byte[] BackGround { get; set; }

        #endregion
    }
}
