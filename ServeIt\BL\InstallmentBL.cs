﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;

namespace ServeIt.BL
{
    class InstallmentBL
    {
        public DataTable SearchInstallments(string Cust_Name, DateTime? From, DateTime? To) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[3];
                para[0] = new SqlParameter("@Cust_name", Cust_Name);
                para[1] = new SqlParameter("@date_from", From);
                para[2] = new SqlParameter("@date_to", To);
                return DAL.GetData("SearchInstallments", para);
            }
        }

        public string CreateInstallmentPaids(BE.InstallmentBE inst_be) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[10];
                para[0] = new SqlParameter("@check",'a');
                para[1] = new SqlParameter("@paid_id",inst_be.PaidID);
                para[2] = new SqlParameter("@inv_id",inst_be.InvID);
                para[3] = new SqlParameter("@cust_id", inst_be.CustID);
                para[4] = new SqlParameter("@paid_required_date",inst_be.RequiredDate);
                para[5] = new SqlParameter("@collecting_date",inst_be.CollectingDate);
                para[6] = new SqlParameter("@paid_status",inst_be.PaidStatus);
                para[7] = new SqlParameter("@treasury_id",inst_be.TreasuryID);
                para[8] = new SqlParameter("@bank_account_id",inst_be.BankAccountID);
                para[9] = new SqlParameter("@value",inst_be.Value);
                return DAL.InsUpdDel("manageInstallmentPaids", para);
            }
        }

        public string CollectingInstallmentPaids(BE.InstallmentBE inst_be)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[11];
                para[0] = new SqlParameter("@check", 'c');
                para[1] = new SqlParameter("@paid_id", inst_be.PaidID);
                para[2] = new SqlParameter("@inv_id", inst_be.InvID);
                para[3] = new SqlParameter("@paid_required_date", inst_be.RequiredDate);
                para[4] = new SqlParameter("@collecting_date", inst_be.CollectingDate);
                para[5] = new SqlParameter("@paid_status", inst_be.PaidStatus);
                para[6] = new SqlParameter("@treasury_id", inst_be.TreasuryID);
                para[7] = new SqlParameter("@bank_account_id", inst_be.BankAccountID);
                para[8] = new SqlParameter("@value", inst_be.Value);
                para[9] = new SqlParameter("@paid", inst_be.Paid);
                para[10] = new SqlParameter("@user_name", inst_be.UserName);
                return DAL.InsUpdDel("manageInstallmentPaids", para);
            }
        }

        public DataTable GetInstallmentDetailsByInvID(string InvID) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@inv_id", InvID);
                return DAL.GetData("GetInstallmentDetailsByInvID", para);
            }
        }

        public string CheckRequiredInstallments(string AlertDate) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@AlertDate", AlertDate);
                return DAL.GetValue("CheckRequiredInstallments", para);
            }
        }

        public string CheckOverdueInstallments()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetValue("CheckOverdueInstallments");
            }
        }

        public DataTable RPTRequiredInstallments(string AlertDate) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@AlertDate", AlertDate);
                return DAL.GetData("RPTRequiredInstallments", para);
            }
        }

        public DataTable RPTOverdueInstallments()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetData("RPTOverdueInstallments", null);
            }
        }

        public DataTable GetSumOfInstallmentPayments(string CustName, DateTime DateFrom, DateTime DateTo)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[3];
                para[0] = new SqlParameter("@cust_name", CustName);
                para[1] = new SqlParameter("@date_from", DateFrom);
                para[2] = new SqlParameter("@date_to", DateTo);
                return DAL.GetData("GetSumOfInstallmentPayments", para);
            }
        }
    }
}
