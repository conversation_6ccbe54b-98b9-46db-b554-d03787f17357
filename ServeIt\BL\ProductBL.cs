﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ServeIt.BL
{
    class ProductBL
    {

        //  methods managing products

        #region Manage Product

        public string AddOrUpdateProduct(BE.ProductBE productbe)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[29];
                para[0] = new SqlParameter("@check", "a");
                para[1] = new SqlParameter("@pro_id", productbe.ProID);
                para[2] = new SqlParameter("@pro_id2", productbe.ProID2);
                para[3] = new SqlParameter("@pro_kind", productbe.ProKind);
                para[4] = new SqlParameter("@pro_name", productbe.ProName);
                para[5] = new SqlParameter("@cat_id", productbe.Cat_Id);
                para[6] = new SqlParameter("@img", productbe.image);
                para[7] = new SqlParameter("@manfacturing_country", productbe.Manfacture_Country);
                para[8] = new SqlParameter("@store_place", productbe.Store_Place);
                para[9] = new SqlParameter("@vat", productbe.VAT);
                para[10] = new SqlParameter("@request_limit", productbe.Request_limit);
                para[11] = new SqlParameter("@default_stock", productbe.DefaultStock);
                para[12] = new SqlParameter("@unit_id", productbe.Unit_Id);
                para[13] = new SqlParameter("@unit_kind", productbe.Unit_Kind);
                para[14] = new SqlParameter("@unit_count", productbe.Unit_Count);
                para[15] = new SqlParameter("@pur_price", productbe.Purchase_Price);
                para[16] = new SqlParameter("@wholesale_price", productbe.Wholsale_Price);
                para[17] = new SqlParameter("@half_wholesale_price", productbe.Half_Wholsale_Price);
                para[18] = new SqlParameter("@retail_price", productbe.Retail_Price);
                para[19] = new SqlParameter("@lowest_price", productbe.LowestPrice);
                para[20] = new SqlParameter("@highest_price", productbe.HighestPrice);
                para[21] = new SqlParameter("@discount", productbe.Discount);
                para[22] = new SqlParameter("@default_purchase_unit", productbe.DefaultPurchaseUnit);
                para[23] = new SqlParameter("@default_sale_unit", productbe.DefaultSaleUnit);
                para[24] = new SqlParameter("@international_barcode", productbe.InternationalBarcode);
                para[25] = new SqlParameter("@system_barcode", productbe.SystemBarcode);
                para[26] = new SqlParameter("@is_active", productbe.IsActive);
                para[27] = new SqlParameter("@notes", productbe.Notes);
                para[28] = new SqlParameter("@user_name", productbe.User_Name);

                return DAL.InsUpdDel("manageproduct", para);
            }
        }


        public string DeleteProduct(BE.ProductBE productbe)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[3];
                para[0] = new SqlParameter("@check", "d");
                para[1] = new SqlParameter("@pro_id", productbe.ProID);
                para[2] = new SqlParameter("@user_name", productbe.User_Name);
                return DAL.InsUpdDel("manageproduct", para);
            }
        }

        public string AddOrUpdateProductCollected(BE.ProductBE productbe)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[7];
                para[0] = new SqlParameter("@check", "a");
                para[1] = new SqlParameter("@pro_id", productbe.ProID);
                para[2] = new SqlParameter("@pro_raw_det_id", productbe.ProRawDetID);
                para[3] = new SqlParameter("@pro_raw_id", productbe.ProRawID);
                para[4] = new SqlParameter("@unit_id", productbe.ProRawUnitID);
                para[5] = new SqlParameter("@quantity", productbe.ProRawQuantity);
                para[6] = new SqlParameter("@price", productbe.ProRawPrice);
                return DAL.InsUpdDel("ManageProductsCollected", para);
            }
        }


        public string DeleteProductCollected(string ProID, string ProRowDetID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[3];
                para[0] = new SqlParameter("@check", "d");
                para[1] = new SqlParameter("@pro_id", ProID);
                para[2] = new SqlParameter("@pro_raw_det_id", ProRowDetID);
                return DAL.InsUpdDel("ManageProductsCollected", para);
            }
        }

        public DataTable GetProductCollectedDetails(string ProID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@pro_id", ProID);
                return DAL.GetData("GetProductCollectedDetailByProID", para);
            }
        }

        public DataTable GetAllProduct()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetData("getallproduct", null);
            }
        }

        public DataTable GetAllProductWithDetails()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetData("getallproductwithdetails", null);
            }
        }

        public DataTable GetCatChildsByCatId(int CatId)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@cat_id", CatId);
                return DAL.GetData("GetCatChildsByCatId", para);
            }
        }



        public DataTable GetProByCatID(int CatId)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@cat_id", CatId);
                return DAL.GetData("GetProByCatID", para);
            }
        }

        public DataTable GetProductDefaultStock(string ProID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@pro_id", ProID);
                return DAL.GetData("GetProductDefaultStock", para);
            }
        }

        public DataTable GetPreviousProID(string CurrentProID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@pro_id", CurrentProID);
                return DAL.GetData("GetPreviousProID", para);
            }
        }

        public DataTable GetNextProID(string CurrentProID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@pro_id", CurrentProID);
                return DAL.GetData("GetNextProID", para);
            }
        }

        public DataTable GetProIDAndUnitIDByInternationalBarcode(string InternationalBarcode)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@international_barcode", InternationalBarcode);
                return DAL.GetData("GetProIDAndUnitIDByInternationalBarcode", para);
            }
        }

        public DataTable GetProductIDByProID2(string ProID2)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@pro_id2", ProID2);
                return DAL.GetData("GetProductIDByProID2", para);
            }
        }

        public DataTable SearchInProducts(BE.ProductBE productbe)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[4];
                para[0] = new SqlParameter("@pro_name", productbe.ProName);
                para[1] = new SqlParameter("@pro_id", productbe.ProID);
                para[2] = new SqlParameter("@pro_id2", productbe.ProID2);
                para[3] = new SqlParameter("@cat_id", productbe.Cat_Id);
                return DAL.GetData("SearchInProducts", para);
            }
        }

        public DataTable GetProductNodesByCatID(string CatID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@cat_id", CatID);
                return DAL.GetData("GetProductNodesByCatID", para);
            }
        }


        public string GetMaxProductId()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetValue("getmaxproductid");
            }
        }

        public string CheckIfInternationalBarcodeExisits(string InternationalBarcode)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@international_barcode", InternationalBarcode);
                return DAL.GetValue("CheckIfInternationalBarcodeExisits", para);
            }
        }


        public DataTable GetUnitOfProduct(string ProID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@pro_id", ProID);
                return DAL.GetData("GetUnitOfProduct", para);
            }
        }

        public DataTable GetDefaultPurchaseUnit(string ProID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@pro_id", ProID);
                return DAL.GetData("GetDefaultPurchaseUnit", para);
            }
        }

        public DataTable GetDefaultSaleUnit(string ProID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@pro_id", ProID);
                return DAL.GetData("GetDefaultSaleUnit", para);
            }
        }

        public DataTable GetUnitOfProductByProID2(string ProID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@pro_id2", ProID);
                return DAL.GetData("GetUnitOfProductByProID2", para);
            }
        }

        public DataTable GetProductUnitDetailsByProIDAndUnitID(string ProID,string UnitID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@pro_id",ProID);
                para[1] = new SqlParameter("@unit_id", UnitID);
                return DAL.GetData("GetProductUnitCountByProIDAndUnitID", para);
            }
        }

        public DataTable GetProductSmallestUnit(string ProID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@pro_id", ProID);
                return DAL.GetData("GetProductSmallestUnit", para);
            }
        }

        public DataTable getProductFirstPeriodByProID(string ProID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@pro_id", ProID);
                return DAL.GetData("getProductFirstPeriodByProID", para);
            }
        }

        public DataTable GetProByProNoOrProId(BE.ProductBE productbe, string pricesys)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[5];
                para[0] = new SqlParameter("@proid", productbe.ProID);
                para[1] = new SqlParameter("@pro_id2", productbe.ProID2);
                para[2] = new SqlParameter("@unit_kind", productbe.Unit_Kind);
                para[3] = new SqlParameter("@unit_id", productbe.Unit_Id);
                para[4] = new SqlParameter("@pricesys", pricesys);
                return DAL.GetData("GetProByProNoOrProId", para);
            }
        }

        public DataTable GetProductById(String ProID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@proid", ProID);
                return DAL.GetData("GetProById", para);
            }
        }

        public DataTable GetProductCostPolicyById(String ProID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@pro_id", ProID);
                return DAL.GetData("GetProductCostPolicyByProID", para);
            }
        }

        public DataTable GetAllProductCostPolicy()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetData("GetAllProCostPolicy", null);
            }
        }

        public DataTable GetProPrice(string pro_id, int unit_id, string pricesys)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[3];
                para[0] = new SqlParameter("@pro_id", pro_id);
                para[1] = new SqlParameter("@unit_id", unit_id);
                para[2] = new SqlParameter("@pricesys", pricesys);
                return DAL.GetData("GetProPrice", para);
            }
        }

        public DataTable GetProPriceRange(string ProID,string UnitID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@pro_id", ProID);
                para[1] = new SqlParameter("@unit_id", UnitID);
                return DAL.GetData("GetProPriceRange",para);
            }
        }

        public DataTable GetProductPrices(string ProID, string CustID, string UnitID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[3];
                para[0] = new SqlParameter("@pro_id", ProID);
                para[1] = new SqlParameter("@cust_id", CustID);
                para[2] = new SqlParameter("@unit_id", UnitID);
                return DAL.GetData("GetProductPrices", para);
            }
        }

        public DataTable GetProductImageById(String ProID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@pro_id", ProID);
                return DAL.GetData("GetProImgByPriID", para);
            }
        }

        public DataTable GetProductUnitById(string ProID) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@pro_id", ProID);
                return DAL.GetData("GetProductUnitByProId", para);
            }
        }

        public DataTable GetBarcodeData(BE.ProductBE probe) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[4];
                para[0] = new SqlParameter("@pro_id", probe.ProID);
                para[1] = new SqlParameter("@unit_id", probe.Unit_Id);
                para[2] = new SqlParameter("@price_sys", probe.Price_System);
                para[3] = new SqlParameter("@count", probe.BarcodeCount);
                return DAL.GetData("GetBarcodeData", para);
            }
        }

        public DataTable GetProductExpireyDateAndQuantity(string StockID, string ProID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@stock_id", StockID);
                para[1] = new SqlParameter("@pro_id", ProID);
                return DAL.GetData("GetSelectedProductExpireyDateInOutingPermissions", para);
            }
        }

        public DataTable GetProductCostAndQuantity(string StockID, string ProID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@stock_id", StockID);
                para[1] = new SqlParameter("@pro_id", ProID);
                return DAL.GetData("GetSelectedProductCostInOutingPermissions", para);
            }
        }

        public string CheckIfProductQuantityWillBeUnderZero(string action_type, string inv_id, string inv_det_id, string inv_kind, string pro_id, string unit_id, decimal quantity, string StockID) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[8];
                para[0] = new SqlParameter("@action_Type", action_type);
                para[1] = new SqlParameter("@inv_id", inv_id);
                para[2] = new SqlParameter("@inv_det_id", inv_det_id);
                para[3] = new SqlParameter("@inv_kind", inv_kind);
                para[4] = new SqlParameter("@pro_id", pro_id);
                para[5] = new SqlParameter("@unit_id", unit_id);
                para[6] = new SqlParameter("@quanity", quantity);
                para[7] = new SqlParameter("@stock_id", StockID);
                return DAL.GetValue("CheckIfQuantityWillBeUnderZero", para);
            }
        }

        public string CheckIfProductQuantityWillBeUnderZeroInStockPermission(string action_type, string per_id, string per_det_id, string per_kind, string pro_id, string unit_id, decimal quantity, string StockID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[8];
                para[0] = new SqlParameter("@action_type", action_type);
                para[1] = new SqlParameter("@per_id", per_id);
                para[2] = new SqlParameter("@per_det_id", per_det_id);
                para[3] = new SqlParameter("@per_kind", per_kind);
                para[4] = new SqlParameter("@pro_id", pro_id);
                para[5] = new SqlParameter("@unit_id", unit_id);
                para[6] = new SqlParameter("@quanity", quantity);
                para[7] = new SqlParameter("@stock_id", StockID);
                return DAL.GetValue("CheckIfQuantityWillBeUnderZeroInStockPermission", para);
            }
        }

        public string UpdateProductPurchasePriceFromPurchaseInvoice(BE.ProductBE probe) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[3];
                para[0] = new SqlParameter("@pro_id",probe.ProID);
                para[1] = new SqlParameter("@unit_id", probe.Unit_Id);
                para[2] = new SqlParameter("@pur_price", probe.Purchase_Price);
                return DAL.InsUpdDel("UpdatePurchasePriceFromPurchaseInvoice", para);
            }
        }

        public string UpdateSalePriceFromSaleInvoice(string ProID, string UnitID, decimal Price,string PriceSystem)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[4];
                para[0] = new SqlParameter("@pro_id", ProID);
                para[1] = new SqlParameter("@unit_id", UnitID);
                para[2] = new SqlParameter("@price", Price);
                para[3] = new SqlParameter("@pricesys", PriceSystem);
                return DAL.InsUpdDel("UpdateSalePriceFromSaleInvoice", para);
            }
        }

        public DataTable GetProductPurchasePrice(BE.ProductBE probe) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@pro_id", probe.ProID);
                para[1] = new SqlParameter("unit_id", probe.Unit_Id);
                return DAL.GetData("GetProductPurchasePrice", para);
            }
        }

        public DataTable RPTGetItemProfit(BE.ProductBE probe) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[3];
                para[0] = new SqlParameter("@cat_id", probe.Cat_Id);
                para[1] = new SqlParameter("@pro_id", probe.ProID);
                para[2] = new SqlParameter("@price_type", probe.Price_Type);
                return DAL.GetData("RPTItemProfit", para);
            }
        }

        public DataTable RPTGetItemProfitFIFO(BE.ProductBE probe)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[4];
                para[0] = new SqlParameter("@cat_id",probe.Cat_Id);
                para[1] = new SqlParameter("@pro_id", probe.ProID);
                para[2] = new SqlParameter("@date_from", probe.DateFrom);
                para[3] = new SqlParameter("@date_to", probe.DateTo);
                return DAL.GetData("RPTItemProfitFiFOPolicy", para);
            }
        }

        public string CheckIfProUnderReqLimit() 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetValue("CheckIfProUnderReqLimit");
            }
        }

        public string CheckIfThereIsItemsExpired(string ExpireyDaysLimit)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@expirey_limit", ExpireyDaysLimit);
                return DAL.GetValue("CheckIfThereIsItemsExpired", para);
            }
        }

        public string CheckIfProductNameExistBefore(string pro_name) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@pro_name",pro_name);
                return DAL.GetValue("CheckIfProductNameExistBefore",para);
            }
        }

        public string CheckIfProductCode2ExistBefore(string ProID2)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@pro_id2", ProID2);
                return DAL.GetValue("CheckIfProductCode2ExistBefore", para);
            }
        }

        public DataTable GetProductUnitsDetails(string ProId)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@pro_id", ProId);
                return DAL.GetData("GetProductUnitsDetails", para);
            }
        }

        public DataTable RPTItemList(BE.ProductBE ProBE) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@pro_id", ProBE.ProID);
                para[1] = new SqlParameter("@price_type", ProBE.ProID);
                return DAL.GetData("RPTItemProfit", para);
            }
        }

        public DataTable CheckIfItemIsService(string ItemID) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@pro_id", ItemID);
                return DAL.GetData("CheckIfItemIsService", para);
            }
        }

        public string DeleteProductUnit(string ProID, string UnitID, String UnitKind) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[4];
                para[0] = new SqlParameter("@check", "d");
                para[1] = new SqlParameter("@pro_id", ProID);
                para[2] = new SqlParameter("@unit_id", UnitID);
                para[3] = new SqlParameter("@unit_kind", UnitKind);
                return DAL.InsUpdDel("ManageProductUnit", para);
            }
        }

        public DataTable GetProExistingCosts(string ProID, DateTime ToDate)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@pro_id", ProID);
                para[1] = new SqlParameter("@date_to", ToDate);
                return DAL.GetData("GetProExistingCosts", para);
            }
        }
        #endregion
    }
}
