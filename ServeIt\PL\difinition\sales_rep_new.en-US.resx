﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="LblWindowCount.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label1.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="TxtNote.Location" type="System.Drawing.Point, System.Drawing">
    <value>215, 225</value>
  </data>
  <data name="TxtAddress.Location" type="System.Drawing.Point, System.Drawing">
    <value>215, 131</value>
  </data>
  <data name="TxtAddress.Size" type="System.Drawing.Size, System.Drawing">
    <value>383, 24</value>
  </data>
  <data name="label5.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label5.Location" type="System.Drawing.Point, System.Drawing">
    <value>153, 134</value>
  </data>
  <data name="label5.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 17</value>
  </data>
  <data name="label5.Text" xml:space="preserve">
    <value>Address</value>
  </data>
  <data name="label4.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label4.Location" type="System.Drawing.Point, System.Drawing">
    <value>135, 104</value>
  </data>
  <data name="label4.Size" type="System.Drawing.Size, System.Drawing">
    <value>74, 17</value>
  </data>
  <data name="label4.Text" xml:space="preserve">
    <value>National ID</value>
  </data>
  <data name="TxtPhone.Location" type="System.Drawing.Point, System.Drawing">
    <value>215, 193</value>
  </data>
  <data name="TxtPhone.Size" type="System.Drawing.Size, System.Drawing">
    <value>383, 24</value>
  </data>
  <data name="TxtMobile.Location" type="System.Drawing.Point, System.Drawing">
    <value>215, 162</value>
  </data>
  <data name="TxtMobile.Size" type="System.Drawing.Size, System.Drawing">
    <value>383, 24</value>
  </data>
  <data name="TxtName.Location" type="System.Drawing.Point, System.Drawing">
    <value>215, 71</value>
  </data>
  <data name="TxtName.Size" type="System.Drawing.Size, System.Drawing">
    <value>383, 24</value>
  </data>
  <data name="TxtId.Location" type="System.Drawing.Point, System.Drawing">
    <value>215, 41</value>
  </data>
  <data name="TxtId.Size" type="System.Drawing.Size, System.Drawing">
    <value>383, 24</value>
  </data>
  <data name="label10.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label10.Location" type="System.Drawing.Point, System.Drawing">
    <value>166, 230</value>
  </data>
  <data name="label10.Size" type="System.Drawing.Size, System.Drawing">
    <value>43, 17</value>
  </data>
  <data name="label10.Text" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="label7.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label7.Location" type="System.Drawing.Point, System.Drawing">
    <value>163, 196</value>
  </data>
  <data name="label7.Size" type="System.Drawing.Size, System.Drawing">
    <value>46, 17</value>
  </data>
  <data name="label7.Text" xml:space="preserve">
    <value>Phone</value>
  </data>
  <data name="label6.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label6.Location" type="System.Drawing.Point, System.Drawing">
    <value>140, 165</value>
  </data>
  <data name="label6.Size" type="System.Drawing.Size, System.Drawing">
    <value>69, 17</value>
  </data>
  <data name="label6.Text" xml:space="preserve">
    <value>Mobile No</value>
  </data>
  <data name="label3.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label3.Location" type="System.Drawing.Point, System.Drawing">
    <value>74, 74</value>
  </data>
  <data name="label3.Size" type="System.Drawing.Size, System.Drawing">
    <value>135, 17</value>
  </data>
  <data name="label3.Text" xml:space="preserve">
    <value>Representative Name</value>
  </data>
  <data name="label2.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label2.Location" type="System.Drawing.Point, System.Drawing">
    <value>95, 44</value>
  </data>
  <data name="label2.Size" type="System.Drawing.Size, System.Drawing">
    <value>114, 17</value>
  </data>
  <data name="label2.Text" xml:space="preserve">
    <value>Representative ID</value>
  </data>
  <data name="TxtNatId.Location" type="System.Drawing.Point, System.Drawing">
    <value>215, 101</value>
  </data>
  <data name="TxtNatId.Size" type="System.Drawing.Size, System.Drawing">
    <value>383, 24</value>
  </data>
  <data name="BtnEdit.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 9.5pt, style=Bold</value>
  </data>
  <data name="BtnEdit.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAG8SURBVFhHtZfbkYIwGIUpwRL2DfcC2IHKNmAJvu3MhhlL
        ENkCLIESLIFh4Z0SLIES3JxMwDgGct0zcx4U8n8nf5gQApXWx3aVFm1PfTN0vz01OS9jJwf46O2pvfBy
        ZnqG193nT7PRcVr87nD/fWy752X1JJs5nUnFL2tpfawWadFc2di8Kfnfak213TQAhDFGY+fW3D5A3aEb
        +B0mh5cwIRuY3SBK9cDZBNgU7XmEx9l5GWe30e/k+pZ8r9iNKjhsE2DQMiblA3xwlFVacNg2wCScO6DF
        lXDYZlNRwWEE4JC6wyxlxlrymtrSgbMlGAJgA+FjnaUDD6OsS5LDwnsAIzjkM4AxHPIVwAoO+QhgDYdc
        AzjBIZcAYURyGVD0LBxyChB+9TLoYCUccgkggw4W4TigsAEy2QQYX6sTHRDhOIxgN2UDZbIKgFcr3UYp
        aD8HxxaO2l4DoPh95qR8/ch2CMMC0WDimgPsPcDTk08PFlgSfvlB/xIAQNZq1gVSTsEh7wGw5gCjC2Kr
        p+Q9wHiG05T3AKYyCmBz6pmT9ocJTXcRQ6ATrjb6NNM9FdsaE+SoaSGE2AlP1vg8D4I/WBjF5ZYnto0A
        AAAASUVORK5CYII=
</value>
  </data>
  <data name="BtnEdit.Size" type="System.Drawing.Size, System.Drawing">
    <value>72, 58</value>
  </data>
  <data name="BtnEdit.Text" xml:space="preserve">
    <value>Edit  F2</value>
  </data>
  <data name="BtnSave.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 9.5pt, style=Bold</value>
  </data>
  <data name="BtnSave.Size" type="System.Drawing.Size, System.Drawing">
    <value>88, 58</value>
  </data>
  <data name="BtnSave.Text" xml:space="preserve">
    <value>Save  F10</value>
  </data>
  <data name="BtnNew.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 9.5pt, style=Bold</value>
  </data>
  <data name="BtnNew.Size" type="System.Drawing.Size, System.Drawing">
    <value>79, 58</value>
  </data>
  <data name="BtnNew.Text" xml:space="preserve">
    <value>New   F1</value>
  </data>
  <data name="BtnDelete.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 9.5pt, style=Bold</value>
  </data>
  <data name="BtnDelete.Size" type="System.Drawing.Size, System.Drawing">
    <value>90, 58</value>
  </data>
  <data name="BtnDelete.Text" xml:space="preserve">
    <value>Delete  F3</value>
  </data>
  <data name="BtnRepresentativesArchive.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 9.5pt, style=Bold</value>
  </data>
  <data name="BtnRepresentativesArchive.Size" type="System.Drawing.Size, System.Drawing">
    <value>76, 58</value>
  </data>
  <data name="BtnRepresentativesArchive.Text" xml:space="preserve">
    <value>Archive</value>
  </data>
  <data name="BtnClose.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 9.5pt, style=Bold</value>
  </data>
  <data name="BtnClose.Size" type="System.Drawing.Size, System.Drawing">
    <value>89, 58</value>
  </data>
  <data name="BtnClose.Text" xml:space="preserve">
    <value>Close  Esc</value>
  </data>
  <data name="toolStrip1.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Inherit</value>
  </data>
  <data name="panel2.Font" type="System.Drawing.Font, System.Drawing">
    <value>Calibri, 10pt, style=Bold</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAICAAAAEAIACoEAAAFgAAACgAAAAgAAAAQAAAAAEAIAAAAAAAABAAACMuAAAjLgAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHb4tQBz+bIAefi5AHP6
        tAmJ+sZklfrRzpn71/WW+tblivrMimv4sxVy+L8AevnKALT//wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHv8ugBk9qoAcPiyAGru
        qwB4+bkygfjApor4yPWO+M7/jfjS/4v51v+J+dj+gPnOqWD2sRhj9rkAZPbAADTykQAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABr964Ab/ixAGb1
        qABx+bQWcfeye3b2teR59bf/ePS5/3b0u/909b//cvXD/3D1yP9x983/a/fFrU/1qRhR9LAAUfS1ADL/
        qwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABx+LMAfPq8AGX2
        qQB5/L0FavesUGn1qcZp86f/aPKm/2bxpv9j8af/YfGq/1/xrf9d8bD/WvGz/1fytv9Z9L3/VvS4rUDz
        oRhA8qYAP/KqABnMcQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACA/8UAW/WhAGf3
        qwBE7YcAZ/erK2P1pZ5h8p/0XvCb/1vvmP9Y7pf/Ve2Y/1LtmP9Q7Zn/Teya/0vsm/9I7Z3/Re2e/0Lu
        of9D8Kn/Q/KrrTHxmhgx8JwAMO+eAA6qWgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAZvarAGv3
        rwBb9KAAaviwEmH1pHJc8p3fWe+W/1Xukf9S7Y//TuyN/0vrjP9I6ov/RemK/0Hpiv8+6Yr/O+mL/zjp
        jP816Yz/MumM/y/qjf8x7Zf/M++drSXvkRgk7pEAJO2SAAaIRgAAAAAAAAAAAAAAAAAAAAAAbfewAIr9
        ygBf9aUAgP/JA2D1pUdb852+V/CV/VLtjv9P64r/S+qI/0fqhv9E6YT/QOiC/zzngP8554D/N+mF+zXs
        jdEx7pGjK+6RlSftj7El6ojnIuaA/x/mff8h6Yj/Je2RrRruiRga7IgAGuuJAAX/ugAAAAAAAAAAAFz1
        owBi9qkASe+OAGL2qCVb85+VVvCW8FHtjv9N64n/SeqG/0Xpg/9C6IH/Pud+/zrme/825Xr/NOd8/zTr
        h9RK64lwusM9VeOqFW/npw9727AZZWrYXlMY6oOvFuR2/hLicP8V5Xr/GuqGrRHsghgS6oEAEumBAAD/
        swAAAAAAVfSgAGb4rg1b9KBoVvGY2VHuj/9N64n/SeqF/0Xpg/9B6ID/Ped9/znlev815Hf/MeV3/zDo
        fu427YqRoclHU+WcBKHfkgPw24wC/9qLAv/dkQT+5p8Izbe+KVMM53mrC+Bp/wjeZP8L4nD/Eeh9rQrr
        fBgO6XwAEul+AADrcwBW9qMlVfKbrVHvkfxM7In/SOqF/0Tpgv9A6H//POZ8/zjlef805Hb/MOR1/y7m
        efwv64S4YuFxWuGjDHrfkgHg2IUA/9Z/AP/WfwD/1n8A/9aAAP/aiwL/6aQLvTveX0ME4WnqA9tc/wHb
        XP8F4Gj/Ded4rQrrexgN6XkAEeh6AE7ymbBM7Y3/SOqF/0Tpgv9A53//POZ8/zjlef805Hb/MON0/y3k
        dv8t6X/aPeuEdMizJF3jlgK/2okA/tZ/AP/VfQD/1X0A/9V9AP/VfgD/1X4A/9eDAP/loAzWedFALgDg
        ZdcA2Vf/ANhW/wDZWP8F32X/DeZ1rAnreRgN6XgARvCR4EPqg/8/533/O+Z7/zfleP8z5HX/L+Ny/yzj
        cv8r5njyLuyFmZDOT1LlmwSY3Y0B8deBAP/VfAD/1XwA/9V8AP/VfAD/1XwA/9V8AP/VfgD/3I4E/++l
        DX4G5WdNAN1d8wDWU/8A11P/ANdU/wDYVv8F3WL/DeVyqwnqeBo+8I+DOuqC+jbld/8y43P/LuJw/yri
        b/8o5HP+Kel+wFPjdF3dpA9y4JAB2diEAP/VfAD/1HsA/9R7AP/UewD/1HsA/9R7AP/UewD/1oAA/96P
        BOznow+DMd1bTADeXtEA11P/ANVQ/wDVUP8A1lH/ANZS/wDXVP8G3WH/DuZ0ojfzkgsy7YV1Luh73Crl
        dPYo5XPzJ+d40jLqgHm8tipY45QCttqGAfzVfAD/1HkA/9R5AP/UeQD/1HkA/9R5AP/UeQD/1HsA/9mG
        Av7klgi7tbgqVwrjZX0A3FvmANZR/wDUTv8A1E7/ANRO/wDUTv8A1U//ANVP/wHYVf8O5G/kNfGOAAD/
        /wAp7oQfJO2BPiPyijedxkM64pcGjtyIAe3VfAD/03YA/9J2AP/TdgD/0nYA/9J2AP/SdQD/03YA/9V9
        AP/ejATk4KERezraWFkA3169ANhT/gDTTf8A0kv/ANNL/wDTS/8A00v/ANNM/wDTTP8A1E3/BNpY/xDl
        b50y7YUAAP/LAI2/PgD/mwAI6JcEX92KAtPVewD/0nMA/9FxAP/RcQD/0XEA/9FxAP/RcQD/0XEA/9J0
        AP/YgQL745IIrpy8MlME4V+LANpV7QDTTP8A0Un/ANFI/wDRSP8A0Uj/ANFI/wDRSP8A0Un/ANNL/wTZ
        VvIN4maQFep3Ft+SAADhlgMA5J8GJd2NBK3WewH60W8A/89rAP/PawD/z2sA/89rAP/PawD/z2sA/89s
        AP/TdAH/3IUF29ieE3Am21VhANtVygDUS/8A0EX/AM9F/wDPRf8Az0X/AM9E/wDPRP8Az0T/ANBG/wLV
        TP4I3FnCEeNoSyvyjQQV6HMA4ZwBAOWkAQ3ciQGy0W8A/8xkAP/MYwD/zGMA/8xjAP/MYwD/zGMA/8xj
        AP/OZwD/1XYC9+CLCaKAvzVSAt1TmQDUSfMAzkL/AMw//wDMP/8AzD//AMw//wDMP/8AzD//AM1A/wHQ
        RP8F107hDN9cdhnncBQM4GAAHOl4ABrndADflQAA4ZgAJtZ5AOjKWwD/yVkA/8lZAP/JWQD/yVkA/8lZ
        AP/JWQD/zWMB/9h5BdLKmRVnGNhKawDVRtUAzj7/AMk5/wDJOf8AyTn/AMk5/wDJOf8AyTn/AMk5/wDL
        O/8B0kL0A9tQnw/nYi4AAAAAmaY/AOymKgDdtCoA4qorAN6UAADhngAO1ngAuMpYAP/FTQD/xU0A/8VN
        AP/FTQD/xU0A/8pZAf/YdwawcbotSADXQaYAzTj5AMcz/wDFMf8AxTH/AMUx/wDFMf8AxTH/AMUx/wDG
        Mv8Byzb/AdVCxzTTSWfYoipj6JQklOSWJqbnnimA6aorK/GUJgDisy0A4Y0AANh6AADZhAAv0GYAzcVJ
        AP/AQAD/wEAA/8BAAP/CQwD/0WUF2pSnHDgA1Da0AMct/wDBKf8AwCn/AMAp/wDAKf8AwCn/AMAp/wDB
        Kf8AxSv/AM4z5Q7WQISspSti4oIZvNx3GvvacRz/23Me/999If/lkibb6aYpReydJwDUlwAA//8AANVw
        AADVeQAwzFoAzcE8AP+8MgD/vDIA/8A7Af/UZAWqCNQxSwDJJvkAvCD/ALwg/wC8IP8AvCD/ALwg/wC8
        IP8AviH/AMYm+AHSMqpiuzFc3n8WlttuE+/WYxT/1WAW/9djGP/ZZxr/22sb/+B4Hv/pmyfE57EsEwAA
        AADSjgAA3P8AANFkAADScQAwyU8Azr0vAP+3JQD/uywB/89VBMk6vSFMAMke7AC6GP8Atxj/ALcY/wC3
        GP8AuRj/AL8a/wDKI84jyy1syoUXc9tqDtbTWw7/0lQP/9NXEf/WWxL/2F8T/9pjFf/cZxb/4HEZ/+ma
        K9rnsC4fAAAAAAAAAADYjgAA1/8AAM5bAADPaQAxxkcAzromAP+2HAH/wTcC/MhkBogJyx15AMQV6gC7
        Ev8AuRH/ALwT/wDEF+cGzSKMlpobXtpoC7TTVwr7z0sK/9BLC//STwz/1FQN/9dYDv/ZXQ//22EP/99p
        Ef/mhiH766k3fdnBHQIAAAAAAAAAAAAAAADMggAA1P8AAMxTAQDOZQExxUMBz7ohAf+3GgH/wzgC98pc
        BZVUqxNdB8oWeQHMFYQQyxxyZa0cXNNrC47TVQfqzUYG/8xBB//PRQf/0UoI/9NOCf/WUwn/2FcJ/9td
        Cv/gbhD/54oi2+unOGnmvkMJ6bQ6AAAAAAAAAAAAAAAAAAAAAADSggAA0PYCAMxRAQDOZgIxx0UCz7si
        Af+5GgH/wC0C/8tEA9zTUwSp1lkEl9ZXBbDRTgThzEIE/8o7BP/LPQT/zkIF/9FHBf/TSwX/1lAG/9hV
        Bf/cYAj/43sV8uiYK5nrtEMo74EWAOi3QQDpszwAAAAAAAAAAAAAAAAAAAAAAAAAAADMfgIAyeUHAM5V
        AQDQawMyykwCz74nAf+7HAH/viIB/8IrAv/FMQL/xjIC/8czAv/JNgL/zDsC/85AA//RRQP/00oD/9ZO
        A//ZVgP/32wL/uaKH8LppjVM6OpvBOuuPwDXy1cA47REAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AADJfgUAw9gPANFfAgDUdwQyzlcE0MMvAv/AJAH/wigB/8QtAf/HMQH/yTYB/8w7Af/OQAH/0UQB/9RJ
        Av/WTwH/3F8F/+N8FOLnmSl36bpFFOycLQDmuEUA6LNAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAADRgwgAvroWANduCADYhA4y02MN0Mg4A//FLgH/xzIB/8o3AP/MOwD/z0AA/9FF
        Af/USgH/2FYC/99vDPbljB6k6Kk0MO8zAADorjsA86Y2AM2jOgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADMgQsAuaIOANx9FQDcjxwz12wT0M1CA//LOAD/zTwA/9BB
        AP/SRgD/1k4A/9xjBv/jgBXL55woVufQTwfoojAA3b1FAOKvOQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADNhg8As5MAAOGIIQDflSEy23UUy9RS
        Bf/SRwD/1EsA/9lZAv/gdA/m5ZEggeewNhrpjBwA5LA4AOesNADNpz4AAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADMihQAsZQAAOSP
        IgDjniEq4osfp+B7Fe3gdw3w5IkbteejLED/AAAA5qcwAP+JFgDhrzcAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAKAAgQAAAAAAAAAAAAAAACAAAAYAAAAAAAAAAAAAAAIAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwAAAAAAA
        AAAAAAAALAAAAgAAAAAAAAAAAAAAAAAAAAA=
</value>
  </data>
  <data name="$this.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Inherit</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="$this.RightToLeftLayout" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
</root>