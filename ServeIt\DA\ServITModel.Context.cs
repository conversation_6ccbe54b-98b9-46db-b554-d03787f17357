﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace ServeIt.DA
{
    using System;
    using System.Data.Entity;
    using System.Data.Entity.Infrastructure;
    using System.Data.Entity.Core.Objects;
    using System.Linq;
    
    public partial class Entities : DbContext
    {
        public Entities()
            : base("name=Entities")
        {
        }
    
        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            throw new UnintentionalCodeFirstException();
        }
    
        public virtual DbSet<action_type> action_type { get; set; }
        public virtual DbSet<Active_permissions> Active_permissions { get; set; }
        public virtual DbSet<asset> assets { get; set; }
        public virtual DbSet<assets_category> assets_category { get; set; }
        public virtual DbSet<assets_depreciations> assets_depreciations { get; set; }
        public virtual DbSet<attendance_departure> attendance_departure { get; set; }
        public virtual DbSet<attendance_state> attendance_state { get; set; }
        public virtual DbSet<bank> banks { get; set; }
        public virtual DbSet<bank_account> bank_account { get; set; }
        public virtual DbSet<bank_action> bank_action { get; set; }
        public virtual DbSet<bank_branch> bank_branch { get; set; }
        public virtual DbSet<category> categories { get; set; }
        public virtual DbSet<company> companies { get; set; }
        public virtual DbSet<cost_center> cost_center { get; set; }
        public virtual DbSet<customer_vendor> customer_vendor { get; set; }
        public virtual DbSet<customer_initial_balance> customer_initial_balance { get; set; }
        public virtual DbSet<customer_kind> customer_kind { get; set; }
        public virtual DbSet<Customers_Transactions> Customers_Transactions { get; set; }
        public virtual DbSet<delivery> deliveries { get; set; }
        public virtual DbSet<document_type> document_type { get; set; }
        public virtual DbSet<employee> employees { get; set; }
        public virtual DbSet<employee_activity> employee_activity { get; set; }
        public virtual DbSet<employee_borrowing_payments> employee_borrowing_payments { get; set; }
        public virtual DbSet<employee_borrowings> employee_borrowings { get; set; }
        public virtual DbSet<employee_payroll_header> employee_payroll_header { get; set; }
        public virtual DbSet<employee_salary> employee_salary { get; set; }
        public virtual DbSet<employee_vacations> employee_vacations { get; set; }
        public virtual DbSet<expens> expenses { get; set; }
        public virtual DbSet<hall> halls { get; set; }
        public virtual DbSet<hall_tables> hall_tables { get; set; }
        public virtual DbSet<Installment_paids> Installment_paids { get; set; }
        public virtual DbSet<invoice_detail> invoice_detail { get; set; }
        public virtual DbSet<invoice_header> invoice_header { get; set; }
        public virtual DbSet<invoice_kind> invoice_kind { get; set; }
        public virtual DbSet<job> jobs { get; set; }
        public virtual DbSet<kitchen_products> kitchen_products { get; set; }
        public virtual DbSet<kitchen> kitchens { get; set; }
        public virtual DbSet<maintenance> maintenances { get; set; }
        public virtual DbSet<maintenance_engineers> maintenance_engineers { get; set; }
        public virtual DbSet<maintenance_workshops> maintenance_workshops { get; set; }
        public virtual DbSet<management> managements { get; set; }
        public virtual DbSet<manufacture_order_Finished_detail> manufacture_order_Finished_detail { get; set; }
        public virtual DbSet<manufacture_order_header> manufacture_order_header { get; set; }
        public virtual DbSet<manufacture_order_Raw_detail> manufacture_order_Raw_detail { get; set; }
        public virtual DbSet<manufacture_order_type> manufacture_order_type { get; set; }
        public virtual DbSet<order_status> order_status { get; set; }
        public virtual DbSet<order_type> order_type { get; set; }
        public virtual DbSet<paper> papers { get; set; }
        public virtual DbSet<payment> payments { get; set; }
        public virtual DbSet<product> products { get; set; }
        public virtual DbSet<product_collected> product_collected { get; set; }
        public virtual DbSet<product_cost_policy> product_cost_policy { get; set; }
        public virtual DbSet<product_initial_balance_detail> product_initial_balance_detail { get; set; }
        public virtual DbSet<product_partitioning_header> product_partitioning_header { get; set; }
        public virtual DbSet<product_partitioning_inputs> product_partitioning_inputs { get; set; }
        public virtual DbSet<product_partitioning_outputs> product_partitioning_outputs { get; set; }
        public virtual DbSet<product_unit> product_unit { get; set; }
        public virtual DbSet<revenues_items> revenues_items { get; set; }
        public virtual DbSet<salary_items> salary_items { get; set; }
        public virtual DbSet<salary_Items_type> salary_Items_type { get; set; }
        public virtual DbSet<sale_price_change_alert> sale_price_change_alert { get; set; }
        public virtual DbSet<sales_representative> sales_representative { get; set; }
        public virtual DbSet<stock> stocks { get; set; }
        public virtual DbSet<stock_permission_detail> stock_permission_detail { get; set; }
        public virtual DbSet<stock_permission_header> stock_permission_header { get; set; }
        public virtual DbSet<stock_permission_kind> stock_permission_kind { get; set; }
        public virtual DbSet<treasury> treasuries { get; set; }
        public virtual DbSet<treasury_initial_balance> treasury_initial_balance { get; set; }
        public virtual DbSet<treasury_permission> treasury_permission { get; set; }
        public virtual DbSet<treasury_permssion_kind> treasury_permssion_kind { get; set; }
        public virtual DbSet<unit> units { get; set; }
        public virtual DbSet<user> users { get; set; }
        public virtual DbSet<user_log> user_log { get; set; }
        public virtual DbSet<users_permissions> users_permissions { get; set; }
        public virtual DbSet<vacations_type> vacations_type { get; set; }
        public virtual DbSet<work_periods> work_periods { get; set; }
        public virtual DbSet<employee_payroll_details> employee_payroll_details { get; set; }
    
        [DbFunction("Entities", "BankTransaction")]
        public virtual IQueryable<BankTransaction_Result> BankTransaction(Nullable<int> account_id)
        {
            var account_idParameter = account_id.HasValue ?
                new ObjectParameter("account_id", account_id) :
                new ObjectParameter("account_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.CreateQuery<BankTransaction_Result>("[Entities].[BankTransaction](@account_id)", account_idParameter);
        }
    
        [DbFunction("Entities", "CalculateProductsCostsAndBalances")]
        public virtual IQueryable<CalculateProductsCostsAndBalances_Result> CalculateProductsCostsAndBalances(Nullable<long> pro_id, Nullable<int> cat_id, Nullable<System.DateTime> dateto)
        {
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            var cat_idParameter = cat_id.HasValue ?
                new ObjectParameter("cat_id", cat_id) :
                new ObjectParameter("cat_id", typeof(int));
    
            var datetoParameter = dateto.HasValue ?
                new ObjectParameter("dateto", dateto) :
                new ObjectParameter("dateto", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.CreateQuery<CalculateProductsCostsAndBalances_Result>("[Entities].[CalculateProductsCostsAndBalances](@pro_id, @cat_id, @dateto)", pro_idParameter, cat_idParameter, datetoParameter);
        }
    
        [DbFunction("Entities", "CustomerTransaction")]
        public virtual IQueryable<CustomerTransaction_Result> CustomerTransaction(Nullable<int> cust_id)
        {
            var cust_idParameter = cust_id.HasValue ?
                new ObjectParameter("cust_id", cust_id) :
                new ObjectParameter("cust_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.CreateQuery<CustomerTransaction_Result>("[Entities].[CustomerTransaction](@cust_id)", cust_idParameter);
        }
    
        [DbFunction("Entities", "GetEmployeeVacationCalenderDuringPeriod")]
        public virtual IQueryable<GetEmployeeVacationCalenderDuringPeriod_Result> GetEmployeeVacationCalenderDuringPeriod(Nullable<int> employee_id, Nullable<System.DateTime> date_from, Nullable<System.DateTime> date_to)
        {
            var employee_idParameter = employee_id.HasValue ?
                new ObjectParameter("employee_id", employee_id) :
                new ObjectParameter("employee_id", typeof(int));
    
            var date_fromParameter = date_from.HasValue ?
                new ObjectParameter("date_from", date_from) :
                new ObjectParameter("date_from", typeof(System.DateTime));
    
            var date_toParameter = date_to.HasValue ?
                new ObjectParameter("date_to", date_to) :
                new ObjectParameter("date_to", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.CreateQuery<GetEmployeeVacationCalenderDuringPeriod_Result>("[Entities].[GetEmployeeVacationCalenderDuringPeriod](@employee_id, @date_from, @date_to)", employee_idParameter, date_fromParameter, date_toParameter);
        }
    
        [DbFunction("Entities", "GetProductImportingPricesAndCurrentBalancesTable")]
        public virtual IQueryable<GetProductImportingPricesAndCurrentBalancesTable_Result> GetProductImportingPricesAndCurrentBalancesTable(Nullable<long> pro_id)
        {
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.CreateQuery<GetProductImportingPricesAndCurrentBalancesTable_Result>("[Entities].[GetProductImportingPricesAndCurrentBalancesTable](@pro_id)", pro_idParameter);
        }
    
        [DbFunction("Entities", "GetProductsExpireyDateAndQuantity")]
        public virtual IQueryable<GetProductsExpireyDateAndQuantity_Result> GetProductsExpireyDateAndQuantity(Nullable<int> stock_id, Nullable<long> pro_id)
        {
            var stock_idParameter = stock_id.HasValue ?
                new ObjectParameter("stock_id", stock_id) :
                new ObjectParameter("stock_id", typeof(int));
    
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.CreateQuery<GetProductsExpireyDateAndQuantity_Result>("[Entities].[GetProductsExpireyDateAndQuantity](@stock_id, @pro_id)", stock_idParameter, pro_idParameter);
        }
    
        [DbFunction("Entities", "GetSubCategoriesOfCategory")]
        public virtual IQueryable<GetSubCategoriesOfCategory_Result> GetSubCategoriesOfCategory(Nullable<int> cat_id)
        {
            var cat_idParameter = cat_id.HasValue ?
                new ObjectParameter("cat_id", cat_id) :
                new ObjectParameter("cat_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.CreateQuery<GetSubCategoriesOfCategory_Result>("[Entities].[GetSubCategoriesOfCategory](@cat_id)", cat_idParameter);
        }
    
        [DbFunction("Entities", "IncomeStatement")]
        public virtual IQueryable<IncomeStatement_Result> IncomeStatement(Nullable<int> pricetype, Nullable<System.DateTime> datefrom, Nullable<System.DateTime> dateto)
        {
            var pricetypeParameter = pricetype.HasValue ?
                new ObjectParameter("pricetype", pricetype) :
                new ObjectParameter("pricetype", typeof(int));
    
            var datefromParameter = datefrom.HasValue ?
                new ObjectParameter("datefrom", datefrom) :
                new ObjectParameter("datefrom", typeof(System.DateTime));
    
            var datetoParameter = dateto.HasValue ?
                new ObjectParameter("dateto", dateto) :
                new ObjectParameter("dateto", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.CreateQuery<IncomeStatement_Result>("[Entities].[IncomeStatement](@pricetype, @datefrom, @dateto)", pricetypeParameter, datefromParameter, datetoParameter);
        }
    
        [DbFunction("Entities", "PrintInvoice")]
        public virtual IQueryable<PrintInvoice_Result> PrintInvoice(Nullable<int> inv_id, Nullable<bool> print_previous_Balance)
        {
            var inv_idParameter = inv_id.HasValue ?
                new ObjectParameter("inv_id", inv_id) :
                new ObjectParameter("inv_id", typeof(int));
    
            var print_previous_BalanceParameter = print_previous_Balance.HasValue ?
                new ObjectParameter("print_previous_Balance", print_previous_Balance) :
                new ObjectParameter("print_previous_Balance", typeof(bool));
    
            return ((IObjectContextAdapter)this).ObjectContext.CreateQuery<PrintInvoice_Result>("[Entities].[PrintInvoice](@inv_id, @print_previous_Balance)", inv_idParameter, print_previous_BalanceParameter);
        }
    
        [DbFunction("Entities", "PrintStockPermission")]
        public virtual IQueryable<PrintStockPermission_Result> PrintStockPermission(Nullable<int> per_id)
        {
            var per_idParameter = per_id.HasValue ?
                new ObjectParameter("per_id", per_id) :
                new ObjectParameter("per_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.CreateQuery<PrintStockPermission_Result>("[Entities].[PrintStockPermission](@per_id)", per_idParameter);
        }
    
        [DbFunction("Entities", "StockTransaction")]
        public virtual IQueryable<StockTransaction_Result> StockTransaction(Nullable<long> pro_id)
        {
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.CreateQuery<StockTransaction_Result>("[Entities].[StockTransaction](@pro_id)", pro_idParameter);
        }
    
        [DbFunction("Entities", "StockTransactionExpirey")]
        public virtual IQueryable<StockTransactionExpirey_Result> StockTransactionExpirey(Nullable<long> pro_id)
        {
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.CreateQuery<StockTransactionExpirey_Result>("[Entities].[StockTransactionExpirey](@pro_id)", pro_idParameter);
        }
    
        [DbFunction("Entities", "StockTransactionSerial")]
        public virtual IQueryable<StockTransactionSerial_Result> StockTransactionSerial(Nullable<long> pro_id)
        {
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.CreateQuery<StockTransactionSerial_Result>("[Entities].[StockTransactionSerial](@pro_id)", pro_idParameter);
        }
    
        [DbFunction("Entities", "StockTransactionWithServicesProducts")]
        public virtual IQueryable<StockTransactionWithServicesProducts_Result> StockTransactionWithServicesProducts(Nullable<long> pro_id)
        {
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.CreateQuery<StockTransactionWithServicesProducts_Result>("[Entities].[StockTransactionWithServicesProducts](@pro_id)", pro_idParameter);
        }
    
        [DbFunction("Entities", "TreasuryTransaction")]
        public virtual IQueryable<TreasuryTransaction_Result> TreasuryTransaction(Nullable<int> treas_id)
        {
            var treas_idParameter = treas_id.HasValue ?
                new ObjectParameter("treas_id", treas_id) :
                new ObjectParameter("treas_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.CreateQuery<TreasuryTransaction_Result>("[Entities].[TreasuryTransaction](@treas_id)", treas_idParameter);
        }
    
        public virtual int AddNewProductToInvoice(Nullable<long> inv_id, Nullable<int> inv_det_id, Nullable<long> pro_id, string pro_serial, Nullable<System.DateTime> pro_expirey, Nullable<int> unit_id, Nullable<int> stock_id, Nullable<decimal> quantity, Nullable<decimal> price, Nullable<decimal> cost_price, Nullable<decimal> quantity_out, Nullable<decimal> pro_discount, Nullable<decimal> vAT, string pro_notes)
        {
            var inv_idParameter = inv_id.HasValue ?
                new ObjectParameter("inv_id", inv_id) :
                new ObjectParameter("inv_id", typeof(long));
    
            var inv_det_idParameter = inv_det_id.HasValue ?
                new ObjectParameter("inv_det_id", inv_det_id) :
                new ObjectParameter("inv_det_id", typeof(int));
    
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            var pro_serialParameter = pro_serial != null ?
                new ObjectParameter("pro_serial", pro_serial) :
                new ObjectParameter("pro_serial", typeof(string));
    
            var pro_expireyParameter = pro_expirey.HasValue ?
                new ObjectParameter("pro_expirey", pro_expirey) :
                new ObjectParameter("pro_expirey", typeof(System.DateTime));
    
            var unit_idParameter = unit_id.HasValue ?
                new ObjectParameter("unit_id", unit_id) :
                new ObjectParameter("unit_id", typeof(int));
    
            var stock_idParameter = stock_id.HasValue ?
                new ObjectParameter("stock_id", stock_id) :
                new ObjectParameter("stock_id", typeof(int));
    
            var quantityParameter = quantity.HasValue ?
                new ObjectParameter("quantity", quantity) :
                new ObjectParameter("quantity", typeof(decimal));
    
            var priceParameter = price.HasValue ?
                new ObjectParameter("price", price) :
                new ObjectParameter("price", typeof(decimal));
    
            var cost_priceParameter = cost_price.HasValue ?
                new ObjectParameter("cost_price", cost_price) :
                new ObjectParameter("cost_price", typeof(decimal));
    
            var quantity_outParameter = quantity_out.HasValue ?
                new ObjectParameter("quantity_out", quantity_out) :
                new ObjectParameter("quantity_out", typeof(decimal));
    
            var pro_discountParameter = pro_discount.HasValue ?
                new ObjectParameter("pro_discount", pro_discount) :
                new ObjectParameter("pro_discount", typeof(decimal));
    
            var vATParameter = vAT.HasValue ?
                new ObjectParameter("VAT", vAT) :
                new ObjectParameter("VAT", typeof(decimal));
    
            var pro_notesParameter = pro_notes != null ?
                new ObjectParameter("pro_notes", pro_notes) :
                new ObjectParameter("pro_notes", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("AddNewProductToInvoice", inv_idParameter, inv_det_idParameter, pro_idParameter, pro_serialParameter, pro_expireyParameter, unit_idParameter, stock_idParameter, quantityParameter, priceParameter, cost_priceParameter, quantity_outParameter, pro_discountParameter, vATParameter, pro_notesParameter);
        }
    
        public virtual ObjectResult<CalculateEmployeePayrollDetailsOnPeriod_Result> CalculateEmployeePayrollDetailsOnPeriod(Nullable<int> employee_id, Nullable<System.DateTime> date_from, Nullable<System.DateTime> date_to)
        {
            var employee_idParameter = employee_id.HasValue ?
                new ObjectParameter("employee_id", employee_id) :
                new ObjectParameter("employee_id", typeof(int));
    
            var date_fromParameter = date_from.HasValue ?
                new ObjectParameter("date_from", date_from) :
                new ObjectParameter("date_from", typeof(System.DateTime));
    
            var date_toParameter = date_to.HasValue ?
                new ObjectParameter("date_to", date_to) :
                new ObjectParameter("date_to", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<CalculateEmployeePayrollDetailsOnPeriod_Result>("CalculateEmployeePayrollDetailsOnPeriod", employee_idParameter, date_fromParameter, date_toParameter);
        }
    
        public virtual ObjectResult<CalculateEmployeePayrollHeaderOnPeriod_Result> CalculateEmployeePayrollHeaderOnPeriod(Nullable<int> employee_id, Nullable<System.DateTime> date_from, Nullable<System.DateTime> date_to)
        {
            var employee_idParameter = employee_id.HasValue ?
                new ObjectParameter("employee_id", employee_id) :
                new ObjectParameter("employee_id", typeof(int));
    
            var date_fromParameter = date_from.HasValue ?
                new ObjectParameter("date_from", date_from) :
                new ObjectParameter("date_from", typeof(System.DateTime));
    
            var date_toParameter = date_to.HasValue ?
                new ObjectParameter("date_to", date_to) :
                new ObjectParameter("date_to", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<CalculateEmployeePayrollHeaderOnPeriod_Result>("CalculateEmployeePayrollHeaderOnPeriod", employee_idParameter, date_fromParameter, date_toParameter);
        }
    
        public virtual ObjectResult<CalculateProductsCosts_Result> CalculateProductsCosts(Nullable<long> pro_id, Nullable<int> cat_id)
        {
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            var cat_idParameter = cat_id.HasValue ?
                new ObjectParameter("cat_id", cat_id) :
                new ObjectParameter("cat_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<CalculateProductsCosts_Result>("CalculateProductsCosts", pro_idParameter, cat_idParameter);
        }
    
        public virtual ObjectResult<CalculateUserShiftCash_Result> CalculateUserShiftCash(string user_name, Nullable<int> treasury_id, Nullable<System.DateTime> start_date, Nullable<System.DateTime> end_date)
        {
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            var treasury_idParameter = treasury_id.HasValue ?
                new ObjectParameter("treasury_id", treasury_id) :
                new ObjectParameter("treasury_id", typeof(int));
    
            var start_dateParameter = start_date.HasValue ?
                new ObjectParameter("start_date", start_date) :
                new ObjectParameter("start_date", typeof(System.DateTime));
    
            var end_dateParameter = end_date.HasValue ?
                new ObjectParameter("end_date", end_date) :
                new ObjectParameter("end_date", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<CalculateUserShiftCash_Result>("CalculateUserShiftCash", user_nameParameter, treasury_idParameter, start_dateParameter, end_dateParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> CheckAlterPassword(string user_name, string alter_password)
        {
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            var alter_passwordParameter = alter_password != null ?
                new ObjectParameter("alter_password", alter_password) :
                new ObjectParameter("alter_password", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("CheckAlterPassword", user_nameParameter, alter_passwordParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> CheckDeletePassword(string user_name, string delete_password)
        {
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            var delete_passwordParameter = delete_password != null ?
                new ObjectParameter("delete_password", delete_password) :
                new ObjectParameter("delete_password", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("CheckDeletePassword", user_nameParameter, delete_passwordParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> CheckForSalePriceChangeAlert()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("CheckForSalePriceChangeAlert");
        }
    
        public virtual ObjectResult<Nullable<int>> CheckIfInternationalBarcodeExisits(string international_barcode)
        {
            var international_barcodeParameter = international_barcode != null ?
                new ObjectParameter("international_barcode", international_barcode) :
                new ObjectParameter("international_barcode", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("CheckIfInternationalBarcodeExisits", international_barcodeParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> CheckIfItemIsService(Nullable<long> pro_id)
        {
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("CheckIfItemIsService", pro_idParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> CheckIfProductCode2ExistBefore(string pro_id2)
        {
            var pro_id2Parameter = pro_id2 != null ?
                new ObjectParameter("pro_id2", pro_id2) :
                new ObjectParameter("pro_id2", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("CheckIfProductCode2ExistBefore", pro_id2Parameter);
        }
    
        public virtual ObjectResult<Nullable<int>> CheckIfProductNameExistBefore(string pro_name)
        {
            var pro_nameParameter = pro_name != null ?
                new ObjectParameter("pro_name", pro_name) :
                new ObjectParameter("pro_name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("CheckIfProductNameExistBefore", pro_nameParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> CheckIfProUnderReqLimit()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("CheckIfProUnderReqLimit");
        }
    
        public virtual ObjectResult<Nullable<decimal>> CheckIfQuantityWillBeUnderZero(string action_Type, Nullable<int> inv_id, Nullable<int> inv_det_id, Nullable<int> inv_kind, Nullable<long> pro_id, Nullable<int> unit_id, Nullable<decimal> quanity, Nullable<int> stock_id)
        {
            var action_TypeParameter = action_Type != null ?
                new ObjectParameter("action_Type", action_Type) :
                new ObjectParameter("action_Type", typeof(string));
    
            var inv_idParameter = inv_id.HasValue ?
                new ObjectParameter("inv_id", inv_id) :
                new ObjectParameter("inv_id", typeof(int));
    
            var inv_det_idParameter = inv_det_id.HasValue ?
                new ObjectParameter("inv_det_id", inv_det_id) :
                new ObjectParameter("inv_det_id", typeof(int));
    
            var inv_kindParameter = inv_kind.HasValue ?
                new ObjectParameter("inv_kind", inv_kind) :
                new ObjectParameter("inv_kind", typeof(int));
    
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            var unit_idParameter = unit_id.HasValue ?
                new ObjectParameter("unit_id", unit_id) :
                new ObjectParameter("unit_id", typeof(int));
    
            var quanityParameter = quanity.HasValue ?
                new ObjectParameter("quanity", quanity) :
                new ObjectParameter("quanity", typeof(decimal));
    
            var stock_idParameter = stock_id.HasValue ?
                new ObjectParameter("stock_id", stock_id) :
                new ObjectParameter("stock_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<decimal>>("CheckIfQuantityWillBeUnderZero", action_TypeParameter, inv_idParameter, inv_det_idParameter, inv_kindParameter, pro_idParameter, unit_idParameter, quanityParameter, stock_idParameter);
        }
    
        public virtual ObjectResult<Nullable<decimal>> CheckIfQuantityWillBeUnderZeroInStockPermission(string action_type, Nullable<int> per_id, Nullable<int> per_det_id, Nullable<int> per_kind, Nullable<long> pro_id, Nullable<int> unit_id, Nullable<decimal> quanity, Nullable<int> stock_id)
        {
            var action_typeParameter = action_type != null ?
                new ObjectParameter("action_type", action_type) :
                new ObjectParameter("action_type", typeof(string));
    
            var per_idParameter = per_id.HasValue ?
                new ObjectParameter("per_id", per_id) :
                new ObjectParameter("per_id", typeof(int));
    
            var per_det_idParameter = per_det_id.HasValue ?
                new ObjectParameter("per_det_id", per_det_id) :
                new ObjectParameter("per_det_id", typeof(int));
    
            var per_kindParameter = per_kind.HasValue ?
                new ObjectParameter("per_kind", per_kind) :
                new ObjectParameter("per_kind", typeof(int));
    
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            var unit_idParameter = unit_id.HasValue ?
                new ObjectParameter("unit_id", unit_id) :
                new ObjectParameter("unit_id", typeof(int));
    
            var quanityParameter = quanity.HasValue ?
                new ObjectParameter("quanity", quanity) :
                new ObjectParameter("quanity", typeof(decimal));
    
            var stock_idParameter = stock_id.HasValue ?
                new ObjectParameter("stock_id", stock_id) :
                new ObjectParameter("stock_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<decimal>>("CheckIfQuantityWillBeUnderZeroInStockPermission", action_typeParameter, per_idParameter, per_det_idParameter, per_kindParameter, pro_idParameter, unit_idParameter, quanityParameter, stock_idParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> CheckIfThereIsItemsExpired(Nullable<int> expirey_limit)
        {
            var expirey_limitParameter = expirey_limit.HasValue ?
                new ObjectParameter("expirey_limit", expirey_limit) :
                new ObjectParameter("expirey_limit", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("CheckIfThereIsItemsExpired", expirey_limitParameter);
        }
    
        public virtual ObjectResult<CheckIfThereIsWorkShiftExistAndOpenedAndGetResult_Result> CheckIfThereIsWorkShiftExistAndOpenedAndGetResult(string user_name, Nullable<System.DateTime> date)
        {
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            var dateParameter = date.HasValue ?
                new ObjectParameter("date", date) :
                new ObjectParameter("date", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<CheckIfThereIsWorkShiftExistAndOpenedAndGetResult_Result>("CheckIfThereIsWorkShiftExistAndOpenedAndGetResult", user_nameParameter, dateParameter);
        }
    
        public virtual ObjectResult<string> CheckIfUserHaveChangeDiscountPermission(string user_name)
        {
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<string>("CheckIfUserHaveChangeDiscountPermission", user_nameParameter);
        }
    
        public virtual ObjectResult<string> CheckIfUserHaveChangePricePermission(string user_name)
        {
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<string>("CheckIfUserHaveChangePricePermission", user_nameParameter);
        }
    
        public virtual ObjectResult<string> CheckIfUserHaveCostPricePermissionInReports(string user_name)
        {
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<string>("CheckIfUserHaveCostPricePermissionInReports", user_nameParameter);
        }
    
        public virtual ObjectResult<string> CheckIfUserHaveCreditInvociePermission(string user_name)
        {
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<string>("CheckIfUserHaveCreditInvociePermission", user_nameParameter);
        }
    
        public virtual ObjectResult<string> CheckIfUserHavePurchasePriceVisible(string user_name)
        {
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<string>("CheckIfUserHavePurchasePriceVisible", user_nameParameter);
        }
    
        public virtual ObjectResult<string> CheckIfUserHaveSalePriceVisible(string user_name)
        {
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<string>("CheckIfUserHaveSalePriceVisible", user_nameParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> CheckMaintenanceDeliveryDate(Nullable<int> alertDate)
        {
            var alertDateParameter = alertDate.HasValue ?
                new ObjectParameter("AlertDate", alertDate) :
                new ObjectParameter("AlertDate", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("CheckMaintenanceDeliveryDate", alertDateParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> CheckOverdueInstallments()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("CheckOverdueInstallments");
        }
    
        public virtual ObjectResult<Nullable<int>> CheckRequiredInstallments(Nullable<int> alertDate)
        {
            var alertDateParameter = alertDate.HasValue ?
                new ObjectParameter("AlertDate", alertDate) :
                new ObjectParameter("AlertDate", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("CheckRequiredInstallments", alertDateParameter);
        }
    
        public virtual int CollectingOrRefusingPapers(string check, string balance_affected, Nullable<int> paper_type, Nullable<int> paper_id, Nullable<int> paper_status, Nullable<int> treas_id, Nullable<int> account_id, Nullable<System.DateTime> collecting_date, Nullable<decimal> value)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var balance_affectedParameter = balance_affected != null ?
                new ObjectParameter("balance_affected", balance_affected) :
                new ObjectParameter("balance_affected", typeof(string));
    
            var paper_typeParameter = paper_type.HasValue ?
                new ObjectParameter("paper_type", paper_type) :
                new ObjectParameter("paper_type", typeof(int));
    
            var paper_idParameter = paper_id.HasValue ?
                new ObjectParameter("paper_id", paper_id) :
                new ObjectParameter("paper_id", typeof(int));
    
            var paper_statusParameter = paper_status.HasValue ?
                new ObjectParameter("paper_status", paper_status) :
                new ObjectParameter("paper_status", typeof(int));
    
            var treas_idParameter = treas_id.HasValue ?
                new ObjectParameter("treas_id", treas_id) :
                new ObjectParameter("treas_id", typeof(int));
    
            var account_idParameter = account_id.HasValue ?
                new ObjectParameter("account_id", account_id) :
                new ObjectParameter("account_id", typeof(int));
    
            var collecting_dateParameter = collecting_date.HasValue ?
                new ObjectParameter("collecting_date", collecting_date) :
                new ObjectParameter("collecting_date", typeof(System.DateTime));
    
            var valueParameter = value.HasValue ?
                new ObjectParameter("value", value) :
                new ObjectParameter("value", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("CollectingOrRefusingPapers", checkParameter, balance_affectedParameter, paper_typeParameter, paper_idParameter, paper_statusParameter, treas_idParameter, account_idParameter, collecting_dateParameter, valueParameter);
        }
    
        public virtual int ConvertFromPriceListToSaleInvoice(Nullable<long> inv_id)
        {
            var inv_idParameter = inv_id.HasValue ?
                new ObjectParameter("inv_id", inv_id) :
                new ObjectParameter("inv_id", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("ConvertFromPriceListToSaleInvoice", inv_idParameter);
        }
    
        public virtual int deleteinvoicerow(Nullable<int> inv_id, Nullable<int> inv_det_id)
        {
            var inv_idParameter = inv_id.HasValue ?
                new ObjectParameter("inv_id", inv_id) :
                new ObjectParameter("inv_id", typeof(int));
    
            var inv_det_idParameter = inv_det_id.HasValue ?
                new ObjectParameter("inv_det_id", inv_det_id) :
                new ObjectParameter("inv_det_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("deleteinvoicerow", inv_idParameter, inv_det_idParameter);
        }
    
        public virtual ObjectResult<GetAccountByBranchId_Result> GetAccountByBranchId(Nullable<int> branch_id)
        {
            var branch_idParameter = branch_id.HasValue ?
                new ObjectParameter("branch_id", branch_id) :
                new ObjectParameter("branch_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetAccountByBranchId_Result>("GetAccountByBranchId", branch_idParameter);
        }
    
        public virtual ObjectResult<GetActionTypes_Result> GetActionTypes()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetActionTypes_Result>("GetActionTypes");
        }
    
        public virtual ObjectResult<getallbankaccounts_Result> getallbankaccounts()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<getallbankaccounts_Result>("getallbankaccounts");
        }
    
        public virtual ObjectResult<GetAllBankBranch_Result> GetAllBankBranch()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetAllBankBranch_Result>("GetAllBankBranch");
        }
    
        public virtual ObjectResult<getallbanks_Result> getallbanks()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<getallbanks_Result>("getallbanks");
        }
    
        public virtual ObjectResult<getallcat_Result> getallcat()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<getallcat_Result>("getallcat");
        }
    
        public virtual ObjectResult<GetAllCostCenters_Result> GetAllCostCenters()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetAllCostCenters_Result>("GetAllCostCenters");
        }
    
        public virtual ObjectResult<getallcustomer_Result> getallcustomer()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<getallcustomer_Result>("getallcustomer");
        }
    
        public virtual ObjectResult<GetAllExpenses_Result> GetAllExpenses()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetAllExpenses_Result>("GetAllExpenses");
        }
    
        public virtual ObjectResult<getallinvoicebytype_Result> getallinvoicebytype(Nullable<int> inv_kind)
        {
            var inv_kindParameter = inv_kind.HasValue ?
                new ObjectParameter("inv_kind", inv_kind) :
                new ObjectParameter("inv_kind", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<getallinvoicebytype_Result>("getallinvoicebytype", inv_kindParameter);
        }
    
        public virtual ObjectResult<getallinvpaymentkind_Result> getallinvpaymentkind()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<getallinvpaymentkind_Result>("getallinvpaymentkind");
        }
    
        public virtual ObjectResult<GetAllMaintenanceEngineers_Result> GetAllMaintenanceEngineers()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetAllMaintenanceEngineers_Result>("GetAllMaintenanceEngineers");
        }
    
        public virtual ObjectResult<GetAllMaintenanceWorkShops_Result> GetAllMaintenanceWorkShops()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetAllMaintenanceWorkShops_Result>("GetAllMaintenanceWorkShops");
        }
    
        public virtual ObjectResult<GetAllProCostPolicy_Result> GetAllProCostPolicy()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetAllProCostPolicy_Result>("GetAllProCostPolicy");
        }
    
        public virtual ObjectResult<getallproduct_Result> getallproduct()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<getallproduct_Result>("getallproduct");
        }
    
        public virtual ObjectResult<getallproductwithdetails_Result> getallproductwithdetails()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<getallproductwithdetails_Result>("getallproductwithdetails");
        }
    
        public virtual ObjectResult<GetAllRevenueItems_Result> GetAllRevenueItems()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetAllRevenueItems_Result>("GetAllRevenueItems");
        }
    
        public virtual ObjectResult<GetAllSalePriceChangeAlerts_Result> GetAllSalePriceChangeAlerts()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetAllSalePriceChangeAlerts_Result>("GetAllSalePriceChangeAlerts");
        }
    
        public virtual ObjectResult<getallsalesrepresentative_Result> getallsalesrepresentative()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<getallsalesrepresentative_Result>("getallsalesrepresentative");
        }
    
        public virtual ObjectResult<getallstock_Result> getallstock()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<getallstock_Result>("getallstock");
        }
    
        public virtual ObjectResult<GetAllSystemDatabases_Result> GetAllSystemDatabases()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetAllSystemDatabases_Result>("GetAllSystemDatabases");
        }
    
        public virtual ObjectResult<getalltreasuries_Result> getalltreasuries()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<getalltreasuries_Result>("getalltreasuries");
        }
    
        public virtual ObjectResult<getallunit_Result> getallunit()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<getallunit_Result>("getallunit");
        }
    
        public virtual ObjectResult<GetAllUser_Result> GetAllUser()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetAllUser_Result>("GetAllUser");
        }
    
        public virtual ObjectResult<string> GetAllUserNames()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<string>("GetAllUserNames");
        }
    
        public virtual ObjectResult<byte[]> GetBackGroundImage()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<byte[]>("GetBackGroundImage");
        }
    
        public virtual ObjectResult<GetBankActionByActionID_Result> GetBankActionByActionID(Nullable<int> action_id)
        {
            var action_idParameter = action_id.HasValue ?
                new ObjectParameter("action_id", action_id) :
                new ObjectParameter("action_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetBankActionByActionID_Result>("GetBankActionByActionID", action_idParameter);
        }
    
        public virtual ObjectResult<getbankbranchbybankid_Result> getbankbranchbybankid(Nullable<int> bank_id)
        {
            var bank_idParameter = bank_id.HasValue ?
                new ObjectParameter("bank_id", bank_id) :
                new ObjectParameter("bank_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<getbankbranchbybankid_Result>("getbankbranchbybankid", bank_idParameter);
        }
    
        public virtual ObjectResult<GetBarcodeData_Result> GetBarcodeData(Nullable<long> pro_id, Nullable<int> unit_id, string price_sys, Nullable<int> count)
        {
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            var unit_idParameter = unit_id.HasValue ?
                new ObjectParameter("unit_id", unit_id) :
                new ObjectParameter("unit_id", typeof(int));
    
            var price_sysParameter = price_sys != null ?
                new ObjectParameter("price_sys", price_sys) :
                new ObjectParameter("price_sys", typeof(string));
    
            var countParameter = count.HasValue ?
                new ObjectParameter("count", count) :
                new ObjectParameter("count", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetBarcodeData_Result>("GetBarcodeData", pro_idParameter, unit_idParameter, price_sysParameter, countParameter);
        }
    
        public virtual ObjectResult<GetCatChildsByCatId_Result> GetCatChildsByCatId(Nullable<int> cat_id)
        {
            var cat_idParameter = cat_id.HasValue ?
                new ObjectParameter("cat_id", cat_id) :
                new ObjectParameter("cat_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetCatChildsByCatId_Result>("GetCatChildsByCatId", cat_idParameter);
        }
    
        public virtual ObjectResult<GetCategoryByID_Result> GetCategoryByID(Nullable<int> cat_id)
        {
            var cat_idParameter = cat_id.HasValue ?
                new ObjectParameter("cat_id", cat_id) :
                new ObjectParameter("cat_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetCategoryByID_Result>("GetCategoryByID", cat_idParameter);
        }
    
        public virtual ObjectResult<GetCategoryNodes_Result> GetCategoryNodes(Nullable<int> parent_id)
        {
            var parent_idParameter = parent_id.HasValue ?
                new ObjectParameter("parent_id", parent_id) :
                new ObjectParameter("parent_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetCategoryNodes_Result>("GetCategoryNodes", parent_idParameter);
        }
    
        public virtual ObjectResult<getcatwithid_Result> getcatwithid(Nullable<int> id)
        {
            var idParameter = id.HasValue ?
                new ObjectParameter("id", id) :
                new ObjectParameter("id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<getcatwithid_Result>("getcatwithid", idParameter);
        }
    
        public virtual ObjectResult<GetChildExpenses_Result> GetChildExpenses()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetChildExpenses_Result>("GetChildExpenses");
        }
    
        public virtual ObjectResult<GetChildRevenues_Result> GetChildRevenues()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetChildRevenues_Result>("GetChildRevenues");
        }
    
        public virtual ObjectResult<getcompanydetail_Result> getcompanydetail()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<getcompanydetail_Result>("getcompanydetail");
        }
    
        public virtual ObjectResult<GetCustomerById_Result> GetCustomerById(Nullable<int> cust_id)
        {
            var cust_idParameter = cust_id.HasValue ?
                new ObjectParameter("cust_id", cust_id) :
                new ObjectParameter("cust_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetCustomerById_Result>("GetCustomerById", cust_idParameter);
        }
    
        public virtual ObjectResult<getcustomerbyname_Result> getcustomerbyname(string cus_name)
        {
            var cus_nameParameter = cus_name != null ?
                new ObjectParameter("cus_name", cus_name) :
                new ObjectParameter("cus_name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<getcustomerbyname_Result>("getcustomerbyname", cus_nameParameter);
        }
    
        public virtual ObjectResult<string> getcustomerkind()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<string>("getcustomerkind");
        }
    
        public virtual ObjectResult<getcustomerorvendor_Result> getcustomerorvendor(string cust_kind)
        {
            var cust_kindParameter = cust_kind != null ?
                new ObjectParameter("cust_kind", cust_kind) :
                new ObjectParameter("cust_kind", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<getcustomerorvendor_Result>("getcustomerorvendor", cust_kindParameter);
        }
    
        public virtual ObjectResult<getcustomerorvendorbyname_Result> getcustomerorvendorbyname(string kind, string name)
        {
            var kindParameter = kind != null ?
                new ObjectParameter("kind", kind) :
                new ObjectParameter("kind", typeof(string));
    
            var nameParameter = name != null ?
                new ObjectParameter("name", name) :
                new ObjectParameter("name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<getcustomerorvendorbyname_Result>("getcustomerorvendorbyname", kindParameter, nameParameter);
        }
    
        public virtual ObjectResult<string> GetCustomerPriceSystemByCustID(Nullable<int> cust_id)
        {
            var cust_idParameter = cust_id.HasValue ?
                new ObjectParameter("cust_id", cust_id) :
                new ObjectParameter("cust_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<string>("GetCustomerPriceSystemByCustID", cust_idParameter);
        }
    
        public virtual ObjectResult<byte[]> GetCustomerVendorPictureByCustID(Nullable<int> cust_id)
        {
            var cust_idParameter = cust_id.HasValue ?
                new ObjectParameter("cust_id", cust_id) :
                new ObjectParameter("cust_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<byte[]>("GetCustomerVendorPictureByCustID", cust_idParameter);
        }
    
        public virtual ObjectResult<GetDefaultCustomer_Result> GetDefaultCustomer()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetDefaultCustomer_Result>("GetDefaultCustomer");
        }
    
        public virtual ObjectResult<Nullable<bool>> GetDefaultPurchaseUnit(Nullable<long> pro_id)
        {
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<bool>>("GetDefaultPurchaseUnit", pro_idParameter);
        }
    
        public virtual ObjectResult<Nullable<bool>> GetDefaultSaleUnit(Nullable<long> pro_id)
        {
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<bool>>("GetDefaultSaleUnit", pro_idParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> getdefaultstock()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("getdefaultstock");
        }
    
        public virtual ObjectResult<GetDeliveryByID_Result> GetDeliveryByID(Nullable<int> delivery_id)
        {
            var delivery_idParameter = delivery_id.HasValue ?
                new ObjectParameter("delivery_id", delivery_id) :
                new ObjectParameter("delivery_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetDeliveryByID_Result>("GetDeliveryByID", delivery_idParameter);
        }
    
        public virtual ObjectResult<GetDisplayedCategory_Result> GetDisplayedCategory()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetDisplayedCategory_Result>("GetDisplayedCategory");
        }
    
        public virtual ObjectResult<string> GetDistinctDamageCategory()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<string>("GetDistinctDamageCategory");
        }
    
        public virtual ObjectResult<string> GetDistinctItemCategoryInMaintenance()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<string>("GetDistinctItemCategoryInMaintenance");
        }
    
        public virtual ObjectResult<string> GetDistinctItemInMaintenance()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<string>("GetDistinctItemInMaintenance");
        }
    
        public virtual ObjectResult<GetDocumentTypes_Result> GetDocumentTypes()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetDocumentTypes_Result>("GetDocumentTypes");
        }
    
        public virtual ObjectResult<GetEmployeeDefaultSalaryItems_Result> GetEmployeeDefaultSalaryItems()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetEmployeeDefaultSalaryItems_Result>("GetEmployeeDefaultSalaryItems");
        }
    
        public virtual ObjectResult<GetEmployeeSalaryItems_Result> GetEmployeeSalaryItems(Nullable<int> employee_id)
        {
            var employee_idParameter = employee_id.HasValue ?
                new ObjectParameter("employee_id", employee_id) :
                new ObjectParameter("employee_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetEmployeeSalaryItems_Result>("GetEmployeeSalaryItems", employee_idParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> GetEmpoyeeIDByFingerPrintIDAndEmpFingerPrintID(Nullable<int> finerprint_id, Nullable<int> employee_fingerprint_id)
        {
            var finerprint_idParameter = finerprint_id.HasValue ?
                new ObjectParameter("finerprint_id", finerprint_id) :
                new ObjectParameter("finerprint_id", typeof(int));
    
            var employee_fingerprint_idParameter = employee_fingerprint_id.HasValue ?
                new ObjectParameter("employee_fingerprint_id", employee_fingerprint_id) :
                new ObjectParameter("employee_fingerprint_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("GetEmpoyeeIDByFingerPrintIDAndEmpFingerPrintID", finerprint_idParameter, employee_fingerprint_idParameter);
        }
    
        public virtual ObjectResult<GetExpensesNodes_Result> GetExpensesNodes(Nullable<int> parent_id)
        {
            var parent_idParameter = parent_id.HasValue ?
                new ObjectParameter("parent_id", parent_id) :
                new ObjectParameter("parent_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetExpensesNodes_Result>("GetExpensesNodes", parent_idParameter);
        }
    
        public virtual ObjectResult<GetInstallmentDetailsByInvID_Result> GetInstallmentDetailsByInvID(Nullable<int> inv_id)
        {
            var inv_idParameter = inv_id.HasValue ?
                new ObjectParameter("inv_id", inv_id) :
                new ObjectParameter("inv_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetInstallmentDetailsByInvID_Result>("GetInstallmentDetailsByInvID", inv_idParameter);
        }
    
        public virtual ObjectResult<GetInvoiceDetailByInvID_Result> GetInvoiceDetailByInvID(Nullable<int> inv_id)
        {
            var inv_idParameter = inv_id.HasValue ?
                new ObjectParameter("inv_id", inv_id) :
                new ObjectParameter("inv_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetInvoiceDetailByInvID_Result>("GetInvoiceDetailByInvID", inv_idParameter);
        }
    
        public virtual ObjectResult<GetInvoiceDetails_Result> GetInvoiceDetails(Nullable<int> inv_id)
        {
            var inv_idParameter = inv_id.HasValue ?
                new ObjectParameter("inv_id", inv_id) :
                new ObjectParameter("inv_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetInvoiceDetails_Result>("GetInvoiceDetails", inv_idParameter);
        }
    
        public virtual ObjectResult<GetInvoiceHeaderByInvID_Result> GetInvoiceHeaderByInvID(Nullable<int> inv_id)
        {
            var inv_idParameter = inv_id.HasValue ?
                new ObjectParameter("inv_id", inv_id) :
                new ObjectParameter("inv_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetInvoiceHeaderByInvID_Result>("GetInvoiceHeaderByInvID", inv_idParameter);
        }
    
        public virtual ObjectResult<Nullable<long>> GetInvoiceIDByTableID(Nullable<int> table_id)
        {
            var table_idParameter = table_id.HasValue ?
                new ObjectParameter("table_id", table_id) :
                new ObjectParameter("table_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<long>>("GetInvoiceIDByTableID", table_idParameter);
        }
    
        public virtual ObjectResult<GetInvoiceKinds_Result> GetInvoiceKinds()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetInvoiceKinds_Result>("GetInvoiceKinds");
        }
    
        public virtual ObjectResult<GetInvoiceStockAndDetailByInvID_Result> GetInvoiceStockAndDetailByInvID(Nullable<int> inv_id)
        {
            var inv_idParameter = inv_id.HasValue ?
                new ObjectParameter("inv_id", inv_id) :
                new ObjectParameter("inv_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetInvoiceStockAndDetailByInvID_Result>("GetInvoiceStockAndDetailByInvID", inv_idParameter);
        }
    
        public virtual ObjectResult<GetMainCategories_Result> GetMainCategories()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetMainCategories_Result>("GetMainCategories");
        }
    
        public virtual ObjectResult<GetMaintenanceByMaintenanceID_Result> GetMaintenanceByMaintenanceID(Nullable<int> maintenance_id)
        {
            var maintenance_idParameter = maintenance_id.HasValue ?
                new ObjectParameter("maintenance_id", maintenance_id) :
                new ObjectParameter("maintenance_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetMaintenanceByMaintenanceID_Result>("GetMaintenanceByMaintenanceID", maintenance_idParameter);
        }
    
        public virtual ObjectResult<GetMaintenanceSparePartsDetailsByMaintenanceID_Result> GetMaintenanceSparePartsDetailsByMaintenanceID(Nullable<int> maintenance_id)
        {
            var maintenance_idParameter = maintenance_id.HasValue ?
                new ObjectParameter("maintenance_id", maintenance_id) :
                new ObjectParameter("maintenance_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetMaintenanceSparePartsDetailsByMaintenanceID_Result>("GetMaintenanceSparePartsDetailsByMaintenanceID", maintenance_idParameter);
        }
    
        public virtual int GetManufactureOrderFinishedProductDetailAndStockByOrderID(Nullable<int> manufacture_order_id)
        {
            var manufacture_order_idParameter = manufacture_order_id.HasValue ?
                new ObjectParameter("manufacture_order_id", manufacture_order_id) :
                new ObjectParameter("manufacture_order_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("GetManufactureOrderFinishedProductDetailAndStockByOrderID", manufacture_order_idParameter);
        }
    
        public virtual ObjectResult<GetManufactureOrderFinishedProductDetailByOrderID_Result> GetManufactureOrderFinishedProductDetailByOrderID(Nullable<int> manufacture_order_id)
        {
            var manufacture_order_idParameter = manufacture_order_id.HasValue ?
                new ObjectParameter("manufacture_order_id", manufacture_order_id) :
                new ObjectParameter("manufacture_order_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetManufactureOrderFinishedProductDetailByOrderID_Result>("GetManufactureOrderFinishedProductDetailByOrderID", manufacture_order_idParameter);
        }
    
        public virtual ObjectResult<GetManufactureOrderHeaderByOrderID_Result> GetManufactureOrderHeaderByOrderID(Nullable<int> manufacture_order_id)
        {
            var manufacture_order_idParameter = manufacture_order_id.HasValue ?
                new ObjectParameter("manufacture_order_id", manufacture_order_id) :
                new ObjectParameter("manufacture_order_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetManufactureOrderHeaderByOrderID_Result>("GetManufactureOrderHeaderByOrderID", manufacture_order_idParameter);
        }
    
        public virtual ObjectResult<GetManufactureOrderRawMaterialDetailByOrderID_Result> GetManufactureOrderRawMaterialDetailByOrderID(Nullable<int> manufacture_order_id)
        {
            var manufacture_order_idParameter = manufacture_order_id.HasValue ?
                new ObjectParameter("manufacture_order_id", manufacture_order_id) :
                new ObjectParameter("manufacture_order_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetManufactureOrderRawMaterialDetailByOrderID_Result>("GetManufactureOrderRawMaterialDetailByOrderID", manufacture_order_idParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> getmaxbankaccountid()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("getmaxbankaccountid");
        }
    
        public virtual ObjectResult<Nullable<int>> GetMaxBankActionId()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("GetMaxBankActionId");
        }
    
        public virtual ObjectResult<Nullable<int>> getmaxbankbranchid()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("getmaxbankbranchid");
        }
    
        public virtual ObjectResult<Nullable<int>> getmaxbankid()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("getmaxbankid");
        }
    
        public virtual ObjectResult<Nullable<int>> getmaxcatid()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("getmaxcatid");
        }
    
        public virtual ObjectResult<Nullable<int>> GetMaxCostCenterID()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("GetMaxCostCenterID");
        }
    
        public virtual ObjectResult<Nullable<int>> getmaxcustid()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("getmaxcustid");
        }
    
        public virtual ObjectResult<Nullable<int>> GetMaxDeliveryID()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("GetMaxDeliveryID");
        }
    
        public virtual ObjectResult<Nullable<int>> GetMaxInventoryPermissionId()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("GetMaxInventoryPermissionId");
        }
    
        public virtual ObjectResult<Nullable<long>> getmaxinvoiceid()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<long>>("getmaxinvoiceid");
        }
    
        public virtual ObjectResult<Nullable<int>> GetMaxMaintenanceEngineer()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("GetMaxMaintenanceEngineer");
        }
    
        public virtual ObjectResult<Nullable<int>> GetMaxMaintenanceID()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("GetMaxMaintenanceID");
        }
    
        public virtual ObjectResult<Nullable<int>> GetMaxMaintenanceWorkshop()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("GetMaxMaintenanceWorkshop");
        }
    
        public virtual ObjectResult<Nullable<int>> GetMaxPaperId()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("GetMaxPaperId");
        }
    
        public virtual ObjectResult<Nullable<long>> getmaxproductid()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<long>>("getmaxproductid");
        }
    
        public virtual ObjectResult<Nullable<int>> getmaxsalesrepresentativeid()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("getmaxsalesrepresentativeid");
        }
    
        public virtual ObjectResult<Nullable<int>> getmaxstockid()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("getmaxstockid");
        }
    
        public virtual ObjectResult<Nullable<int>> getmaxtreasuryid()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("getmaxtreasuryid");
        }
    
        public virtual ObjectResult<Nullable<int>> GetMaxTreasuryPermssion()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("GetMaxTreasuryPermssion");
        }
    
        public virtual ObjectResult<Nullable<int>> getmaxunitid()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("getmaxunitid");
        }
    
        public virtual ObjectResult<Nullable<int>> GetNextCategoryID(Nullable<int> current_id)
        {
            var current_idParameter = current_id.HasValue ?
                new ObjectParameter("current_id", current_id) :
                new ObjectParameter("current_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("GetNextCategoryID", current_idParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> GetNextCustomerDepositTreasuryPer(Nullable<int> current_id)
        {
            var current_idParameter = current_id.HasValue ?
                new ObjectParameter("current_id", current_id) :
                new ObjectParameter("current_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("GetNextCustomerDepositTreasuryPer", current_idParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> GetNextCustomerID(Nullable<long> current_cust_id)
        {
            var current_cust_idParameter = current_cust_id.HasValue ?
                new ObjectParameter("current_cust_id", current_cust_id) :
                new ObjectParameter("current_cust_id", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("GetNextCustomerID", current_cust_idParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> GetNextCustomerPaymentTreasuryPer(Nullable<int> current_id)
        {
            var current_idParameter = current_id.HasValue ?
                new ObjectParameter("current_id", current_id) :
                new ObjectParameter("current_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("GetNextCustomerPaymentTreasuryPer", current_idParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> GetNextDepositTreasuryPer(Nullable<int> current_id)
        {
            var current_idParameter = current_id.HasValue ?
                new ObjectParameter("current_id", current_id) :
                new ObjectParameter("current_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("GetNextDepositTreasuryPer", current_idParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> GetNextExpenseItemID(Nullable<int> current_id)
        {
            var current_idParameter = current_id.HasValue ?
                new ObjectParameter("current_id", current_id) :
                new ObjectParameter("current_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("GetNextExpenseItemID", current_idParameter);
        }
    
        public virtual ObjectResult<Nullable<long>> GetNextInvID(Nullable<int> inv_id, Nullable<int> inv_kind_id, string user_name)
        {
            var inv_idParameter = inv_id.HasValue ?
                new ObjectParameter("inv_id", inv_id) :
                new ObjectParameter("inv_id", typeof(int));
    
            var inv_kind_idParameter = inv_kind_id.HasValue ?
                new ObjectParameter("inv_kind_id", inv_kind_id) :
                new ObjectParameter("inv_kind_id", typeof(int));
    
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<long>>("GetNextInvID", inv_idParameter, inv_kind_idParameter, user_nameParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> GetNextManufacture(Nullable<int> manf_id, Nullable<int> manf_type_id)
        {
            var manf_idParameter = manf_id.HasValue ?
                new ObjectParameter("manf_id", manf_id) :
                new ObjectParameter("manf_id", typeof(int));
    
            var manf_type_idParameter = manf_type_id.HasValue ?
                new ObjectParameter("manf_type_id", manf_type_id) :
                new ObjectParameter("manf_type_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("GetNextManufacture", manf_idParameter, manf_type_idParameter);
        }
    
        public virtual ObjectResult<Nullable<long>> GetNextProID(Nullable<long> pro_id)
        {
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<long>>("GetNextProID", pro_idParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> GetNextRelocateTreasuryPer(Nullable<int> current_id)
        {
            var current_idParameter = current_id.HasValue ?
                new ObjectParameter("current_id", current_id) :
                new ObjectParameter("current_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("GetNextRelocateTreasuryPer", current_idParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> GetNextRevenueItemID(Nullable<int> current_id)
        {
            var current_idParameter = current_id.HasValue ?
                new ObjectParameter("current_id", current_id) :
                new ObjectParameter("current_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("GetNextRevenueItemID", current_idParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> GetNextSaleRepresentativeID(Nullable<int> current_id)
        {
            var current_idParameter = current_id.HasValue ?
                new ObjectParameter("current_id", current_id) :
                new ObjectParameter("current_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("GetNextSaleRepresentativeID", current_idParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> GetNextStockID(Nullable<int> current_id)
        {
            var current_idParameter = current_id.HasValue ?
                new ObjectParameter("current_id", current_id) :
                new ObjectParameter("current_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("GetNextStockID", current_idParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> GetNextStockPermissionID(Nullable<int> current_id, Nullable<int> per_kind_id)
        {
            var current_idParameter = current_id.HasValue ?
                new ObjectParameter("current_id", current_id) :
                new ObjectParameter("current_id", typeof(int));
    
            var per_kind_idParameter = per_kind_id.HasValue ?
                new ObjectParameter("per_kind_id", per_kind_id) :
                new ObjectParameter("per_kind_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("GetNextStockPermissionID", current_idParameter, per_kind_idParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> GetNextTreasuryID(Nullable<int> current_id)
        {
            var current_idParameter = current_id.HasValue ?
                new ObjectParameter("current_id", current_id) :
                new ObjectParameter("current_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("GetNextTreasuryID", current_idParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> GetNextUnitID(Nullable<int> current_id)
        {
            var current_idParameter = current_id.HasValue ?
                new ObjectParameter("current_id", current_id) :
                new ObjectParameter("current_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("GetNextUnitID", current_idParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> GetNextWithdrawTreasuryPer(Nullable<int> current_id)
        {
            var current_idParameter = current_id.HasValue ?
                new ObjectParameter("current_id", current_id) :
                new ObjectParameter("current_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("GetNextWithdrawTreasuryPer", current_idParameter);
        }
    
        public virtual ObjectResult<GetOrderKitchenDetails_Result> GetOrderKitchenDetails(Nullable<int> inv_id)
        {
            var inv_idParameter = inv_id.HasValue ?
                new ObjectParameter("inv_id", inv_id) :
                new ObjectParameter("inv_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetOrderKitchenDetails_Result>("GetOrderKitchenDetails", inv_idParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> GetOutStandingChecksCount()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("GetOutStandingChecksCount");
        }
    
        public virtual ObjectResult<GetPaperDetailsByPaperId_Result> GetPaperDetailsByPaperId(Nullable<int> paper_id)
        {
            var paper_idParameter = paper_id.HasValue ?
                new ObjectParameter("paper_id", paper_id) :
                new ObjectParameter("paper_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetPaperDetailsByPaperId_Result>("GetPaperDetailsByPaperId", paper_idParameter);
        }
    
        public virtual ObjectResult<byte[]> GetPaperImage(Nullable<int> paper_id)
        {
            var paper_idParameter = paper_id.HasValue ?
                new ObjectParameter("paper_id", paper_id) :
                new ObjectParameter("paper_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<byte[]>("GetPaperImage", paper_idParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> GetPayableChecksCount()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("GetPayableChecksCount");
        }
    
        public virtual ObjectResult<Nullable<int>> GetPreviousCategoryID(Nullable<int> current_id)
        {
            var current_idParameter = current_id.HasValue ?
                new ObjectParameter("current_id", current_id) :
                new ObjectParameter("current_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("GetPreviousCategoryID", current_idParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> GetPreviousCustomerDepositTreasuryPerID(Nullable<int> current_id)
        {
            var current_idParameter = current_id.HasValue ?
                new ObjectParameter("current_id", current_id) :
                new ObjectParameter("current_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("GetPreviousCustomerDepositTreasuryPerID", current_idParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> GetPreviousCustomerID(Nullable<int> current_cust_id)
        {
            var current_cust_idParameter = current_cust_id.HasValue ?
                new ObjectParameter("current_cust_id", current_cust_id) :
                new ObjectParameter("current_cust_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("GetPreviousCustomerID", current_cust_idParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> GetPreviousCustomerPaymentTreasuryPerID(Nullable<int> current_id)
        {
            var current_idParameter = current_id.HasValue ?
                new ObjectParameter("current_id", current_id) :
                new ObjectParameter("current_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("GetPreviousCustomerPaymentTreasuryPerID", current_idParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> GetPreviousDepositTreasuryPerID(Nullable<int> current_id)
        {
            var current_idParameter = current_id.HasValue ?
                new ObjectParameter("current_id", current_id) :
                new ObjectParameter("current_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("GetPreviousDepositTreasuryPerID", current_idParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> GetPreviousExpenseItemID(Nullable<int> current_id)
        {
            var current_idParameter = current_id.HasValue ?
                new ObjectParameter("current_id", current_id) :
                new ObjectParameter("current_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("GetPreviousExpenseItemID", current_idParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> GetPreviousExpenseTreasuryPerID(Nullable<int> current_id)
        {
            var current_idParameter = current_id.HasValue ?
                new ObjectParameter("current_id", current_id) :
                new ObjectParameter("current_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("GetPreviousExpenseTreasuryPerID", current_idParameter);
        }
    
        public virtual ObjectResult<Nullable<long>> GetPreviousInvID(Nullable<int> inv_id, Nullable<int> inv_kind_id, string user_name)
        {
            var inv_idParameter = inv_id.HasValue ?
                new ObjectParameter("inv_id", inv_id) :
                new ObjectParameter("inv_id", typeof(int));
    
            var inv_kind_idParameter = inv_kind_id.HasValue ?
                new ObjectParameter("inv_kind_id", inv_kind_id) :
                new ObjectParameter("inv_kind_id", typeof(int));
    
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<long>>("GetPreviousInvID", inv_idParameter, inv_kind_idParameter, user_nameParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> GetPreviousManufacture(Nullable<int> manf_id, Nullable<int> manf_type_id)
        {
            var manf_idParameter = manf_id.HasValue ?
                new ObjectParameter("manf_id", manf_id) :
                new ObjectParameter("manf_id", typeof(int));
    
            var manf_type_idParameter = manf_type_id.HasValue ?
                new ObjectParameter("manf_type_id", manf_type_id) :
                new ObjectParameter("manf_type_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("GetPreviousManufacture", manf_idParameter, manf_type_idParameter);
        }
    
        public virtual ObjectResult<Nullable<long>> GetPreviousProID(Nullable<long> pro_id)
        {
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<long>>("GetPreviousProID", pro_idParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> GetPreviousRelocateTreasuryPerID(Nullable<int> current_id)
        {
            var current_idParameter = current_id.HasValue ?
                new ObjectParameter("current_id", current_id) :
                new ObjectParameter("current_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("GetPreviousRelocateTreasuryPerID", current_idParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> GetPreviousRevenueItemID(Nullable<int> current_id)
        {
            var current_idParameter = current_id.HasValue ?
                new ObjectParameter("current_id", current_id) :
                new ObjectParameter("current_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("GetPreviousRevenueItemID", current_idParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> GetPreviousSaleRepresentativeID(Nullable<int> current_id)
        {
            var current_idParameter = current_id.HasValue ?
                new ObjectParameter("current_id", current_id) :
                new ObjectParameter("current_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("GetPreviousSaleRepresentativeID", current_idParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> GetPreviousStockID(Nullable<int> current_id)
        {
            var current_idParameter = current_id.HasValue ?
                new ObjectParameter("current_id", current_id) :
                new ObjectParameter("current_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("GetPreviousStockID", current_idParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> GetPreviousStockPermissionID(Nullable<int> current_id, Nullable<int> per_kind_id)
        {
            var current_idParameter = current_id.HasValue ?
                new ObjectParameter("current_id", current_id) :
                new ObjectParameter("current_id", typeof(int));
    
            var per_kind_idParameter = per_kind_id.HasValue ?
                new ObjectParameter("per_kind_id", per_kind_id) :
                new ObjectParameter("per_kind_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("GetPreviousStockPermissionID", current_idParameter, per_kind_idParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> GetPreviousTreasuryID(Nullable<int> current_id)
        {
            var current_idParameter = current_id.HasValue ?
                new ObjectParameter("current_id", current_id) :
                new ObjectParameter("current_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("GetPreviousTreasuryID", current_idParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> GetPreviousUnitID(Nullable<int> current_id)
        {
            var current_idParameter = current_id.HasValue ?
                new ObjectParameter("current_id", current_id) :
                new ObjectParameter("current_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("GetPreviousUnitID", current_idParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> GetPreviousWithdrawTreasuryPerID(Nullable<int> current_id)
        {
            var current_idParameter = current_id.HasValue ?
                new ObjectParameter("current_id", current_id) :
                new ObjectParameter("current_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("GetPreviousWithdrawTreasuryPerID", current_idParameter);
        }
    
        public virtual ObjectResult<Nullable<decimal>> GetProBalanceOfSmallUnitInStock(Nullable<int> stock_id, Nullable<long> pro_id)
        {
            var stock_idParameter = stock_id.HasValue ?
                new ObjectParameter("stock_id", stock_id) :
                new ObjectParameter("stock_id", typeof(int));
    
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<decimal>>("GetProBalanceOfSmallUnitInStock", stock_idParameter, pro_idParameter);
        }
    
        public virtual ObjectResult<GetProByCatID_Result> GetProByCatID(Nullable<int> cat_id)
        {
            var cat_idParameter = cat_id.HasValue ?
                new ObjectParameter("cat_id", cat_id) :
                new ObjectParameter("cat_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetProByCatID_Result>("GetProByCatID", cat_idParameter);
        }
    
        public virtual ObjectResult<GetProById_Result> GetProById(Nullable<int> proid)
        {
            var proidParameter = proid.HasValue ?
                new ObjectParameter("proid", proid) :
                new ObjectParameter("proid", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetProById_Result>("GetProById", proidParameter);
        }
    
        public virtual ObjectResult<GetProByProNoOrProId_Result> GetProByProNoOrProId(Nullable<long> proid, string pro_id2, string pricesys, Nullable<int> unit_kind, Nullable<int> unit_id)
        {
            var proidParameter = proid.HasValue ?
                new ObjectParameter("proid", proid) :
                new ObjectParameter("proid", typeof(long));
    
            var pro_id2Parameter = pro_id2 != null ?
                new ObjectParameter("pro_id2", pro_id2) :
                new ObjectParameter("pro_id2", typeof(string));
    
            var pricesysParameter = pricesys != null ?
                new ObjectParameter("pricesys", pricesys) :
                new ObjectParameter("pricesys", typeof(string));
    
            var unit_kindParameter = unit_kind.HasValue ?
                new ObjectParameter("unit_kind", unit_kind) :
                new ObjectParameter("unit_kind", typeof(int));
    
            var unit_idParameter = unit_id.HasValue ?
                new ObjectParameter("unit_id", unit_id) :
                new ObjectParameter("unit_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetProByProNoOrProId_Result>("GetProByProNoOrProId", proidParameter, pro_id2Parameter, pricesysParameter, unit_kindParameter, unit_idParameter);
        }
    
        public virtual ObjectResult<string> GetProDetailedBalanceByProIDAndStockID(Nullable<int> stock_id, Nullable<long> pro_id)
        {
            var stock_idParameter = stock_id.HasValue ?
                new ObjectParameter("stock_id", stock_id) :
                new ObjectParameter("stock_id", typeof(int));
    
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<string>("GetProDetailedBalanceByProIDAndStockID", stock_idParameter, pro_idParameter);
        }
    
        public virtual ObjectResult<GetProductCollectedDetailByProID_Result> GetProductCollectedDetailByProID(Nullable<long> pro_id)
        {
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetProductCollectedDetailByProID_Result>("GetProductCollectedDetailByProID", pro_idParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> GetProductDefaultStock(Nullable<long> pro_id)
        {
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("GetProductDefaultStock", pro_idParameter);
        }
    
        public virtual ObjectResult<GetProductFirstPeriodBalancesByStockID_Result> GetProductFirstPeriodBalancesByStockID(Nullable<int> stock_id)
        {
            var stock_idParameter = stock_id.HasValue ?
                new ObjectParameter("stock_id", stock_id) :
                new ObjectParameter("stock_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetProductFirstPeriodBalancesByStockID_Result>("GetProductFirstPeriodBalancesByStockID", stock_idParameter);
        }
    
        public virtual ObjectResult<getProductFirstPeriodByProID_Result> getProductFirstPeriodByProID(Nullable<long> pro_id)
        {
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<getProductFirstPeriodByProID_Result>("getProductFirstPeriodByProID", pro_idParameter);
        }
    
        public virtual ObjectResult<Nullable<long>> GetProductIDByProID2(string pro_id2)
        {
            var pro_id2Parameter = pro_id2 != null ?
                new ObjectParameter("pro_id2", pro_id2) :
                new ObjectParameter("pro_id2", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<long>>("GetProductIDByProID2", pro_id2Parameter);
        }
    
        public virtual ObjectResult<GetProductNodesByCatID_Result> GetProductNodesByCatID(Nullable<int> cat_id)
        {
            var cat_idParameter = cat_id.HasValue ?
                new ObjectParameter("cat_id", cat_id) :
                new ObjectParameter("cat_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetProductNodesByCatID_Result>("GetProductNodesByCatID", cat_idParameter);
        }
    
        public virtual ObjectResult<GetProductPrices_Result> GetProductPrices(Nullable<long> pro_id, Nullable<int> cust_id, Nullable<int> unit_id)
        {
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            var cust_idParameter = cust_id.HasValue ?
                new ObjectParameter("cust_id", cust_id) :
                new ObjectParameter("cust_id", typeof(int));
    
            var unit_idParameter = unit_id.HasValue ?
                new ObjectParameter("unit_id", unit_id) :
                new ObjectParameter("unit_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetProductPrices_Result>("GetProductPrices", pro_idParameter, cust_idParameter, unit_idParameter);
        }
    
        public virtual ObjectResult<Nullable<decimal>> GetProductPurchasePrice(Nullable<long> pro_id, Nullable<int> unit_id)
        {
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            var unit_idParameter = unit_id.HasValue ?
                new ObjectParameter("unit_id", unit_id) :
                new ObjectParameter("unit_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<decimal>>("GetProductPurchasePrice", pro_idParameter, unit_idParameter);
        }
    
        public virtual ObjectResult<Nullable<decimal>> GetProductQuantityInSmallUnit(Nullable<long> pro_id, Nullable<int> unit_id, Nullable<decimal> qty)
        {
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            var unit_idParameter = unit_id.HasValue ?
                new ObjectParameter("unit_id", unit_id) :
                new ObjectParameter("unit_id", typeof(int));
    
            var qtyParameter = qty.HasValue ?
                new ObjectParameter("qty", qty) :
                new ObjectParameter("qty", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<decimal>>("GetProductQuantityInSmallUnit", pro_idParameter, unit_idParameter, qtyParameter);
        }
    
        public virtual ObjectResult<Nullable<decimal>> GetProductSmallBalanceByProIDAndStockID(Nullable<int> pro_id, Nullable<int> stock_id)
        {
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(int));
    
            var stock_idParameter = stock_id.HasValue ?
                new ObjectParameter("stock_id", stock_id) :
                new ObjectParameter("stock_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<decimal>>("GetProductSmallBalanceByProIDAndStockID", pro_idParameter, stock_idParameter);
        }
    
        public virtual ObjectResult<GetProductSmallestUnit_Result> GetProductSmallestUnit(Nullable<long> pro_id)
        {
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetProductSmallestUnit_Result>("GetProductSmallestUnit", pro_idParameter);
        }
    
        public virtual ObjectResult<GetProductUnitByProId_Result> GetProductUnitByProId(Nullable<long> pro_id)
        {
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetProductUnitByProId_Result>("GetProductUnitByProId", pro_idParameter);
        }
    
        public virtual ObjectResult<GetProductUnitCountByProIDAndUnitID_Result> GetProductUnitCountByProIDAndUnitID(Nullable<long> pro_id, Nullable<int> unit_id)
        {
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            var unit_idParameter = unit_id.HasValue ?
                new ObjectParameter("unit_id", unit_id) :
                new ObjectParameter("unit_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetProductUnitCountByProIDAndUnitID_Result>("GetProductUnitCountByProIDAndUnitID", pro_idParameter, unit_idParameter);
        }
    
        public virtual ObjectResult<string> GetProductUnitsDetails(Nullable<long> pro_id)
        {
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<string>("GetProductUnitsDetails", pro_idParameter);
        }
    
        public virtual ObjectResult<GetProExistingCosts_Result> GetProExistingCosts(Nullable<long> pro_id, Nullable<System.DateTime> date_to)
        {
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            var date_toParameter = date_to.HasValue ?
                new ObjectParameter("date_to", date_to) :
                new ObjectParameter("date_to", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetProExistingCosts_Result>("GetProExistingCosts", pro_idParameter, date_toParameter);
        }
    
        public virtual ObjectResult<GetProIDAndUnitIDByInternationalBarcode_Result> GetProIDAndUnitIDByInternationalBarcode(string international_barcode)
        {
            var international_barcodeParameter = international_barcode != null ?
                new ObjectParameter("international_barcode", international_barcode) :
                new ObjectParameter("international_barcode", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetProIDAndUnitIDByInternationalBarcode_Result>("GetProIDAndUnitIDByInternationalBarcode", international_barcodeParameter);
        }
    
        public virtual ObjectResult<byte[]> GetProImgByPriID(Nullable<long> pro_id)
        {
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<byte[]>("GetProImgByPriID", pro_idParameter);
        }
    
        public virtual ObjectResult<GetProPrice_Result> GetProPrice(Nullable<long> pro_id, Nullable<int> unit_id, string pricesys)
        {
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            var unit_idParameter = unit_id.HasValue ?
                new ObjectParameter("unit_id", unit_id) :
                new ObjectParameter("unit_id", typeof(int));
    
            var pricesysParameter = pricesys != null ?
                new ObjectParameter("pricesys", pricesys) :
                new ObjectParameter("pricesys", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetProPrice_Result>("GetProPrice", pro_idParameter, unit_idParameter, pricesysParameter);
        }
    
        public virtual ObjectResult<GetProPriceRange_Result> GetProPriceRange(Nullable<long> pro_id, Nullable<int> unit_id)
        {
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            var unit_idParameter = unit_id.HasValue ?
                new ObjectParameter("unit_id", unit_id) :
                new ObjectParameter("unit_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetProPriceRange_Result>("GetProPriceRange", pro_idParameter, unit_idParameter);
        }
    
        public virtual ObjectResult<GetProPrinterAndKitchenByProID_Result> GetProPrinterAndKitchenByProID(Nullable<long> pro_id)
        {
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetProPrinterAndKitchenByProID_Result>("GetProPrinterAndKitchenByProID", pro_idParameter);
        }
    
        public virtual ObjectResult<GetProUnitDetailsByUnitIDAndProID_Result> GetProUnitDetailsByUnitIDAndProID(Nullable<long> pro_id, Nullable<int> unit_id)
        {
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            var unit_idParameter = unit_id.HasValue ?
                new ObjectParameter("unit_id", unit_id) :
                new ObjectParameter("unit_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetProUnitDetailsByUnitIDAndProID_Result>("GetProUnitDetailsByUnitIDAndProID", pro_idParameter, unit_idParameter);
        }
    
        public virtual ObjectResult<GetQUickItemBalance_Result> GetQUickItemBalance(string pro_name, Nullable<int> stock_id, Nullable<int> cat_id, Nullable<int> pro_id, Nullable<int> unit_kind)
        {
            var pro_nameParameter = pro_name != null ?
                new ObjectParameter("pro_name", pro_name) :
                new ObjectParameter("pro_name", typeof(string));
    
            var stock_idParameter = stock_id.HasValue ?
                new ObjectParameter("stock_id", stock_id) :
                new ObjectParameter("stock_id", typeof(int));
    
            var cat_idParameter = cat_id.HasValue ?
                new ObjectParameter("Cat_id", cat_id) :
                new ObjectParameter("Cat_id", typeof(int));
    
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(int));
    
            var unit_kindParameter = unit_kind.HasValue ?
                new ObjectParameter("unit_kind", unit_kind) :
                new ObjectParameter("unit_kind", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetQUickItemBalance_Result>("GetQUickItemBalance", pro_nameParameter, stock_idParameter, cat_idParameter, pro_idParameter, unit_kindParameter);
        }
    
        public virtual ObjectResult<GetRevenuesNodes_Result> GetRevenuesNodes(Nullable<int> parent_id)
        {
            var parent_idParameter = parent_id.HasValue ?
                new ObjectParameter("parent_id", parent_id) :
                new ObjectParameter("parent_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetRevenuesNodes_Result>("GetRevenuesNodes", parent_idParameter);
        }
    
        public virtual ObjectResult<GetSalaryItemTypes_Result> GetSalaryItemTypes()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetSalaryItemTypes_Result>("GetSalaryItemTypes");
        }
    
        public virtual ObjectResult<GetSaleRepresentativeDetailsByID_Result> GetSaleRepresentativeDetailsByID(Nullable<int> sale_rep_id)
        {
            var sale_rep_idParameter = sale_rep_id.HasValue ?
                new ObjectParameter("sale_rep_id", sale_rep_id) :
                new ObjectParameter("sale_rep_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetSaleRepresentativeDetailsByID_Result>("GetSaleRepresentativeDetailsByID", sale_rep_idParameter);
        }
    
        public virtual int GetSelectedProductCostInOutingPermissions(Nullable<long> pro_id, Nullable<int> stock_id)
        {
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            var stock_idParameter = stock_id.HasValue ?
                new ObjectParameter("stock_id", stock_id) :
                new ObjectParameter("stock_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("GetSelectedProductCostInOutingPermissions", pro_idParameter, stock_idParameter);
        }
    
        public virtual ObjectResult<GetSelectedProductExpireyDateInOutingPermissions_Result> GetSelectedProductExpireyDateInOutingPermissions(Nullable<long> pro_id, Nullable<int> stock_id)
        {
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            var stock_idParameter = stock_id.HasValue ?
                new ObjectParameter("stock_id", stock_id) :
                new ObjectParameter("stock_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetSelectedProductExpireyDateInOutingPermissions_Result>("GetSelectedProductExpireyDateInOutingPermissions", pro_idParameter, stock_idParameter);
        }
    
        public virtual ObjectResult<GetStockDetailsByID_Result> GetStockDetailsByID(Nullable<int> stock_id)
        {
            var stock_idParameter = stock_id.HasValue ?
                new ObjectParameter("stock_id", stock_id) :
                new ObjectParameter("stock_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetStockDetailsByID_Result>("GetStockDetailsByID", stock_idParameter);
        }
    
        public virtual ObjectResult<GetStockPermissionDetailAndStockByPerId_Result> GetStockPermissionDetailAndStockByPerId(Nullable<int> per_id)
        {
            var per_idParameter = per_id.HasValue ?
                new ObjectParameter("per_id", per_id) :
                new ObjectParameter("per_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetStockPermissionDetailAndStockByPerId_Result>("GetStockPermissionDetailAndStockByPerId", per_idParameter);
        }
    
        public virtual ObjectResult<GetStockPermissionDetailByPerId_Result> GetStockPermissionDetailByPerId(Nullable<int> per_id)
        {
            var per_idParameter = per_id.HasValue ?
                new ObjectParameter("per_id", per_id) :
                new ObjectParameter("per_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetStockPermissionDetailByPerId_Result>("GetStockPermissionDetailByPerId", per_idParameter);
        }
    
        public virtual ObjectResult<GetStockPermissionHeaderByPerId_Result> GetStockPermissionHeaderByPerId(Nullable<int> per_id)
        {
            var per_idParameter = per_id.HasValue ?
                new ObjectParameter("per_id", per_id) :
                new ObjectParameter("per_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetStockPermissionHeaderByPerId_Result>("GetStockPermissionHeaderByPerId", per_idParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> GetStockPermissionIDByMaintenanceID(Nullable<int> maintenance_id)
        {
            var maintenance_idParameter = maintenance_id.HasValue ?
                new ObjectParameter("maintenance_id", maintenance_id) :
                new ObjectParameter("maintenance_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("GetStockPermissionIDByMaintenanceID", maintenance_idParameter);
        }
    
        public virtual int GetSumOfInstallmentPayments(string cust_name, Nullable<System.DateTime> date_from, Nullable<System.DateTime> date_to)
        {
            var cust_nameParameter = cust_name != null ?
                new ObjectParameter("cust_name", cust_name) :
                new ObjectParameter("cust_name", typeof(string));
    
            var date_fromParameter = date_from.HasValue ?
                new ObjectParameter("date_from", date_from) :
                new ObjectParameter("date_from", typeof(System.DateTime));
    
            var date_toParameter = date_to.HasValue ?
                new ObjectParameter("date_to", date_to) :
                new ObjectParameter("date_to", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("GetSumOfInstallmentPayments", cust_nameParameter, date_fromParameter, date_toParameter);
        }
    
        public virtual ObjectResult<string> GetTableNameByTableID(Nullable<int> table_id)
        {
            var table_idParameter = table_id.HasValue ?
                new ObjectParameter("table_id", table_id) :
                new ObjectParameter("table_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<string>("GetTableNameByTableID", table_idParameter);
        }
    
        public virtual ObjectResult<GetTablesDetailsByHallID_Result> GetTablesDetailsByHallID(Nullable<int> hall_id)
        {
            var hall_idParameter = hall_id.HasValue ?
                new ObjectParameter("hall_id", hall_id) :
                new ObjectParameter("hall_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetTablesDetailsByHallID_Result>("GetTablesDetailsByHallID", hall_idParameter);
        }
    
        public virtual ObjectResult<Nullable<decimal>> GetTotalCustomerBalance(Nullable<int> cust_id)
        {
            var cust_idParameter = cust_id.HasValue ?
                new ObjectParameter("cust_id", cust_id) :
                new ObjectParameter("cust_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<decimal>>("GetTotalCustomerBalance", cust_idParameter);
        }
    
        public virtual ObjectResult<gettreasurybyname_Result> gettreasurybyname(string name)
        {
            var nameParameter = name != null ?
                new ObjectParameter("name", name) :
                new ObjectParameter("name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<gettreasurybyname_Result>("gettreasurybyname", nameParameter);
        }
    
        public virtual ObjectResult<GetTreasuryPermissionById_Result> GetTreasuryPermissionById(Nullable<int> per_id)
        {
            var per_idParameter = per_id.HasValue ?
                new ObjectParameter("per_id", per_id) :
                new ObjectParameter("per_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetTreasuryPermissionById_Result>("GetTreasuryPermissionById", per_idParameter);
        }
    
        public virtual ObjectResult<GetUnitDetailsByUnitID_Result> GetUnitDetailsByUnitID(Nullable<int> unit_id)
        {
            var unit_idParameter = unit_id.HasValue ?
                new ObjectParameter("unit_id", unit_id) :
                new ObjectParameter("unit_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetUnitDetailsByUnitID_Result>("GetUnitDetailsByUnitID", unit_idParameter);
        }
    
        public virtual ObjectResult<GetUnitOfProduct_Result> GetUnitOfProduct(Nullable<long> pro_id)
        {
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetUnitOfProduct_Result>("GetUnitOfProduct", pro_idParameter);
        }
    
        public virtual ObjectResult<GetUnitOfProductByProID2_Result> GetUnitOfProductByProID2(string pro_id2)
        {
            var pro_id2Parameter = pro_id2 != null ?
                new ObjectParameter("pro_id2", pro_id2) :
                new ObjectParameter("pro_id2", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetUnitOfProductByProID2_Result>("GetUnitOfProductByProID2", pro_id2Parameter);
        }
    
        public virtual ObjectResult<GetUserDetailsByUserName_Result> GetUserDetailsByUserName(string user_name)
        {
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetUserDetailsByUserName_Result>("GetUserDetailsByUserName", user_nameParameter);
        }
    
        public virtual ObjectResult<string> GetUserInvoiceCashierName(string user_name)
        {
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<string>("GetUserInvoiceCashierName", user_nameParameter);
        }
    
        public virtual ObjectResult<string> GetUserPermissions(string user_name)
        {
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<string>("GetUserPermissions", user_nameParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> GetUserWorkShiftIDByUserNameAndDate(string user_name, Nullable<System.DateTime> current_date)
        {
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            var current_dateParameter = current_date.HasValue ?
                new ObjectParameter("current_date", current_date) :
                new ObjectParameter("current_date", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("GetUserWorkShiftIDByUserNameAndDate", user_nameParameter, current_dateParameter);
        }
    
        public virtual ObjectResult<GetVacationAllTypes_Result> GetVacationAllTypes()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GetVacationAllTypes_Result>("GetVacationAllTypes");
        }
    
        public virtual ObjectResult<Nullable<int>> Login(string user_name, string password)
        {
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            var passwordParameter = password != null ?
                new ObjectParameter("password", password) :
                new ObjectParameter("password", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("Login", user_nameParameter, passwordParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> ManageAssets(string check, Nullable<int> asset_id, string asset_name, Nullable<int> asset_cat_id, Nullable<decimal> asset_value, Nullable<int> asset_age, Nullable<decimal> asset_scrap_value, string notes, string user_name)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var asset_idParameter = asset_id.HasValue ?
                new ObjectParameter("asset_id", asset_id) :
                new ObjectParameter("asset_id", typeof(int));
    
            var asset_nameParameter = asset_name != null ?
                new ObjectParameter("asset_name", asset_name) :
                new ObjectParameter("asset_name", typeof(string));
    
            var asset_cat_idParameter = asset_cat_id.HasValue ?
                new ObjectParameter("asset_cat_id", asset_cat_id) :
                new ObjectParameter("asset_cat_id", typeof(int));
    
            var asset_valueParameter = asset_value.HasValue ?
                new ObjectParameter("asset_value", asset_value) :
                new ObjectParameter("asset_value", typeof(decimal));
    
            var asset_ageParameter = asset_age.HasValue ?
                new ObjectParameter("asset_age", asset_age) :
                new ObjectParameter("asset_age", typeof(int));
    
            var asset_scrap_valueParameter = asset_scrap_value.HasValue ?
                new ObjectParameter("asset_scrap_value", asset_scrap_value) :
                new ObjectParameter("asset_scrap_value", typeof(decimal));
    
            var notesParameter = notes != null ?
                new ObjectParameter("notes", notes) :
                new ObjectParameter("notes", typeof(string));
    
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("ManageAssets", checkParameter, asset_idParameter, asset_nameParameter, asset_cat_idParameter, asset_valueParameter, asset_ageParameter, asset_scrap_valueParameter, notesParameter, user_nameParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> ManageAssetsCategory(string check, Nullable<int> asset_cat_id, Nullable<int> parent_id, string asset_cat_name, string description, string user_name)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var asset_cat_idParameter = asset_cat_id.HasValue ?
                new ObjectParameter("asset_cat_id", asset_cat_id) :
                new ObjectParameter("asset_cat_id", typeof(int));
    
            var parent_idParameter = parent_id.HasValue ?
                new ObjectParameter("parent_id", parent_id) :
                new ObjectParameter("parent_id", typeof(int));
    
            var asset_cat_nameParameter = asset_cat_name != null ?
                new ObjectParameter("asset_cat_name", asset_cat_name) :
                new ObjectParameter("asset_cat_name", typeof(string));
    
            var descriptionParameter = description != null ?
                new ObjectParameter("description", description) :
                new ObjectParameter("description", typeof(string));
    
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("ManageAssetsCategory", checkParameter, asset_cat_idParameter, parent_idParameter, asset_cat_nameParameter, descriptionParameter, user_nameParameter);
        }
    
        public virtual int managebank(string check, Nullable<int> id, string name)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var idParameter = id.HasValue ?
                new ObjectParameter("id", id) :
                new ObjectParameter("id", typeof(int));
    
            var nameParameter = name != null ?
                new ObjectParameter("name", name) :
                new ObjectParameter("name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("managebank", checkParameter, idParameter, nameParameter);
        }
    
        public virtual int managebankaccount(string check, Nullable<int> id, string account_no, Nullable<int> bank_id, Nullable<int> branch_id, string currency, string note)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var idParameter = id.HasValue ?
                new ObjectParameter("id", id) :
                new ObjectParameter("id", typeof(int));
    
            var account_noParameter = account_no != null ?
                new ObjectParameter("account_no", account_no) :
                new ObjectParameter("account_no", typeof(string));
    
            var bank_idParameter = bank_id.HasValue ?
                new ObjectParameter("bank_id", bank_id) :
                new ObjectParameter("bank_id", typeof(int));
    
            var branch_idParameter = branch_id.HasValue ?
                new ObjectParameter("branch_id", branch_id) :
                new ObjectParameter("branch_id", typeof(int));
    
            var currencyParameter = currency != null ?
                new ObjectParameter("currency", currency) :
                new ObjectParameter("currency", typeof(string));
    
            var noteParameter = note != null ?
                new ObjectParameter("note", note) :
                new ObjectParameter("note", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("managebankaccount", checkParameter, idParameter, account_noParameter, bank_idParameter, branch_idParameter, currencyParameter, noteParameter);
        }
    
        public virtual int ManageBankActions(string check, Nullable<int> action_id, Nullable<int> account_id, string receipt_no, Nullable<int> action_type, Nullable<System.DateTime> date, Nullable<decimal> value, string notes, string user_name)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var action_idParameter = action_id.HasValue ?
                new ObjectParameter("action_id", action_id) :
                new ObjectParameter("action_id", typeof(int));
    
            var account_idParameter = account_id.HasValue ?
                new ObjectParameter("account_id", account_id) :
                new ObjectParameter("account_id", typeof(int));
    
            var receipt_noParameter = receipt_no != null ?
                new ObjectParameter("receipt_no", receipt_no) :
                new ObjectParameter("receipt_no", typeof(string));
    
            var action_typeParameter = action_type.HasValue ?
                new ObjectParameter("action_type", action_type) :
                new ObjectParameter("action_type", typeof(int));
    
            var dateParameter = date.HasValue ?
                new ObjectParameter("date", date) :
                new ObjectParameter("date", typeof(System.DateTime));
    
            var valueParameter = value.HasValue ?
                new ObjectParameter("value", value) :
                new ObjectParameter("value", typeof(decimal));
    
            var notesParameter = notes != null ?
                new ObjectParameter("notes", notes) :
                new ObjectParameter("notes", typeof(string));
    
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("ManageBankActions", checkParameter, action_idParameter, account_idParameter, receipt_noParameter, action_typeParameter, dateParameter, valueParameter, notesParameter, user_nameParameter);
        }
    
        public virtual int managebankbranch(string check, Nullable<int> id, string name, Nullable<int> bank_id, string phone1, string phone2, string fax, string address, string note)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var idParameter = id.HasValue ?
                new ObjectParameter("id", id) :
                new ObjectParameter("id", typeof(int));
    
            var nameParameter = name != null ?
                new ObjectParameter("name", name) :
                new ObjectParameter("name", typeof(string));
    
            var bank_idParameter = bank_id.HasValue ?
                new ObjectParameter("bank_id", bank_id) :
                new ObjectParameter("bank_id", typeof(int));
    
            var phone1Parameter = phone1 != null ?
                new ObjectParameter("phone1", phone1) :
                new ObjectParameter("phone1", typeof(string));
    
            var phone2Parameter = phone2 != null ?
                new ObjectParameter("phone2", phone2) :
                new ObjectParameter("phone2", typeof(string));
    
            var faxParameter = fax != null ?
                new ObjectParameter("fax", fax) :
                new ObjectParameter("fax", typeof(string));
    
            var addressParameter = address != null ?
                new ObjectParameter("address", address) :
                new ObjectParameter("address", typeof(string));
    
            var noteParameter = note != null ?
                new ObjectParameter("note", note) :
                new ObjectParameter("note", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("managebankbranch", checkParameter, idParameter, nameParameter, bank_idParameter, phone1Parameter, phone2Parameter, faxParameter, addressParameter, noteParameter);
        }
    
        public virtual int managecategory(string check, Nullable<int> cat_id, Nullable<int> parent_id, string cat_name, string description, Nullable<bool> is_displayed, string user_name)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var cat_idParameter = cat_id.HasValue ?
                new ObjectParameter("cat_id", cat_id) :
                new ObjectParameter("cat_id", typeof(int));
    
            var parent_idParameter = parent_id.HasValue ?
                new ObjectParameter("parent_id", parent_id) :
                new ObjectParameter("parent_id", typeof(int));
    
            var cat_nameParameter = cat_name != null ?
                new ObjectParameter("cat_name", cat_name) :
                new ObjectParameter("cat_name", typeof(string));
    
            var descriptionParameter = description != null ?
                new ObjectParameter("description", description) :
                new ObjectParameter("description", typeof(string));
    
            var is_displayedParameter = is_displayed.HasValue ?
                new ObjectParameter("is_displayed", is_displayed) :
                new ObjectParameter("is_displayed", typeof(bool));
    
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("managecategory", checkParameter, cat_idParameter, parent_idParameter, cat_nameParameter, descriptionParameter, is_displayedParameter, user_nameParameter);
        }
    
        public virtual int managecompany(string check, Nullable<int> company_id, string company_name, string activity, Nullable<System.DateTime> first_period_date, Nullable<System.DateTime> last_period_date, string commercial_registration, string tax_card, string address, string phone, string mobile, string email, byte[] logo, byte[] background)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var company_idParameter = company_id.HasValue ?
                new ObjectParameter("company_id", company_id) :
                new ObjectParameter("company_id", typeof(int));
    
            var company_nameParameter = company_name != null ?
                new ObjectParameter("company_name", company_name) :
                new ObjectParameter("company_name", typeof(string));
    
            var activityParameter = activity != null ?
                new ObjectParameter("activity", activity) :
                new ObjectParameter("activity", typeof(string));
    
            var first_period_dateParameter = first_period_date.HasValue ?
                new ObjectParameter("first_period_date", first_period_date) :
                new ObjectParameter("first_period_date", typeof(System.DateTime));
    
            var last_period_dateParameter = last_period_date.HasValue ?
                new ObjectParameter("last_period_date", last_period_date) :
                new ObjectParameter("last_period_date", typeof(System.DateTime));
    
            var commercial_registrationParameter = commercial_registration != null ?
                new ObjectParameter("commercial_registration", commercial_registration) :
                new ObjectParameter("commercial_registration", typeof(string));
    
            var tax_cardParameter = tax_card != null ?
                new ObjectParameter("tax_card", tax_card) :
                new ObjectParameter("tax_card", typeof(string));
    
            var addressParameter = address != null ?
                new ObjectParameter("address", address) :
                new ObjectParameter("address", typeof(string));
    
            var phoneParameter = phone != null ?
                new ObjectParameter("phone", phone) :
                new ObjectParameter("phone", typeof(string));
    
            var mobileParameter = mobile != null ?
                new ObjectParameter("mobile", mobile) :
                new ObjectParameter("mobile", typeof(string));
    
            var emailParameter = email != null ?
                new ObjectParameter("email", email) :
                new ObjectParameter("email", typeof(string));
    
            var logoParameter = logo != null ?
                new ObjectParameter("logo", logo) :
                new ObjectParameter("logo", typeof(byte[]));
    
            var backgroundParameter = background != null ?
                new ObjectParameter("background", background) :
                new ObjectParameter("background", typeof(byte[]));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("managecompany", checkParameter, company_idParameter, company_nameParameter, activityParameter, first_period_dateParameter, last_period_dateParameter, commercial_registrationParameter, tax_cardParameter, addressParameter, phoneParameter, mobileParameter, emailParameter, logoParameter, backgroundParameter);
        }
    
        public virtual int ManageCostCenters(string check, Nullable<int> cost_center_id, string cost_center_name, Nullable<bool> is_main_cost_center)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var cost_center_idParameter = cost_center_id.HasValue ?
                new ObjectParameter("cost_center_id", cost_center_id) :
                new ObjectParameter("cost_center_id", typeof(int));
    
            var cost_center_nameParameter = cost_center_name != null ?
                new ObjectParameter("cost_center_name", cost_center_name) :
                new ObjectParameter("cost_center_name", typeof(string));
    
            var is_main_cost_centerParameter = is_main_cost_center.HasValue ?
                new ObjectParameter("is_main_cost_center", is_main_cost_center) :
                new ObjectParameter("is_main_cost_center", typeof(bool));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("ManageCostCenters", checkParameter, cost_center_idParameter, cost_center_nameParameter, is_main_cost_centerParameter);
        }
    
        public virtual int managecustomer_vendors(string check, Nullable<int> cust_id, string cust_name, Nullable<System.DateTime> adding_date, string cust_kind, Nullable<decimal> initial_balance, string price_system, byte[] cust_picture, string city, string area, string address, string mobile_no, string phone, Nullable<int> sales_rep_id, Nullable<bool> active, Nullable<bool> default_customer, string notes, string user_name, string box1_value, string box2_value)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var cust_idParameter = cust_id.HasValue ?
                new ObjectParameter("cust_id", cust_id) :
                new ObjectParameter("cust_id", typeof(int));
    
            var cust_nameParameter = cust_name != null ?
                new ObjectParameter("cust_name", cust_name) :
                new ObjectParameter("cust_name", typeof(string));
    
            var adding_dateParameter = adding_date.HasValue ?
                new ObjectParameter("adding_date", adding_date) :
                new ObjectParameter("adding_date", typeof(System.DateTime));
    
            var cust_kindParameter = cust_kind != null ?
                new ObjectParameter("cust_kind", cust_kind) :
                new ObjectParameter("cust_kind", typeof(string));
    
            var initial_balanceParameter = initial_balance.HasValue ?
                new ObjectParameter("initial_balance", initial_balance) :
                new ObjectParameter("initial_balance", typeof(decimal));
    
            var price_systemParameter = price_system != null ?
                new ObjectParameter("price_system", price_system) :
                new ObjectParameter("price_system", typeof(string));
    
            var cust_pictureParameter = cust_picture != null ?
                new ObjectParameter("cust_picture", cust_picture) :
                new ObjectParameter("cust_picture", typeof(byte[]));
    
            var cityParameter = city != null ?
                new ObjectParameter("city", city) :
                new ObjectParameter("city", typeof(string));
    
            var areaParameter = area != null ?
                new ObjectParameter("area", area) :
                new ObjectParameter("area", typeof(string));
    
            var addressParameter = address != null ?
                new ObjectParameter("address", address) :
                new ObjectParameter("address", typeof(string));
    
            var mobile_noParameter = mobile_no != null ?
                new ObjectParameter("mobile_no", mobile_no) :
                new ObjectParameter("mobile_no", typeof(string));
    
            var phoneParameter = phone != null ?
                new ObjectParameter("phone", phone) :
                new ObjectParameter("phone", typeof(string));
    
            var sales_rep_idParameter = sales_rep_id.HasValue ?
                new ObjectParameter("sales_rep_id", sales_rep_id) :
                new ObjectParameter("sales_rep_id", typeof(int));
    
            var activeParameter = active.HasValue ?
                new ObjectParameter("active", active) :
                new ObjectParameter("active", typeof(bool));
    
            var default_customerParameter = default_customer.HasValue ?
                new ObjectParameter("default_customer", default_customer) :
                new ObjectParameter("default_customer", typeof(bool));
    
            var notesParameter = notes != null ?
                new ObjectParameter("notes", notes) :
                new ObjectParameter("notes", typeof(string));
    
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            var box1_valueParameter = box1_value != null ?
                new ObjectParameter("box1_value", box1_value) :
                new ObjectParameter("box1_value", typeof(string));
    
            var box2_valueParameter = box2_value != null ?
                new ObjectParameter("box2_value", box2_value) :
                new ObjectParameter("box2_value", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("managecustomer_vendors", checkParameter, cust_idParameter, cust_nameParameter, adding_dateParameter, cust_kindParameter, initial_balanceParameter, price_systemParameter, cust_pictureParameter, cityParameter, areaParameter, addressParameter, mobile_noParameter, phoneParameter, sales_rep_idParameter, activeParameter, default_customerParameter, notesParameter, user_nameParameter, box1_valueParameter, box2_valueParameter);
        }
    
        public virtual int managecustomerinitialbalance(string check, Nullable<int> cust_id, Nullable<decimal> quantity)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var cust_idParameter = cust_id.HasValue ?
                new ObjectParameter("cust_id", cust_id) :
                new ObjectParameter("cust_id", typeof(int));
    
            var quantityParameter = quantity.HasValue ?
                new ObjectParameter("quantity", quantity) :
                new ObjectParameter("quantity", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("managecustomerinitialbalance", checkParameter, cust_idParameter, quantityParameter);
        }
    
        public virtual ObjectResult<ManageDelivery_Result> ManageDelivery(string check, Nullable<int> delivery_id, string delivery_name, Nullable<bool> is_active, string notes)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var delivery_idParameter = delivery_id.HasValue ?
                new ObjectParameter("delivery_id", delivery_id) :
                new ObjectParameter("delivery_id", typeof(int));
    
            var delivery_nameParameter = delivery_name != null ?
                new ObjectParameter("delivery_name", delivery_name) :
                new ObjectParameter("delivery_name", typeof(string));
    
            var is_activeParameter = is_active.HasValue ?
                new ObjectParameter("is_active", is_active) :
                new ObjectParameter("is_active", typeof(bool));
    
            var notesParameter = notes != null ?
                new ObjectParameter("notes", notes) :
                new ObjectParameter("notes", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<ManageDelivery_Result>("ManageDelivery", checkParameter, delivery_idParameter, delivery_nameParameter, is_activeParameter, notesParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> ManageEmployee(string check, Nullable<int> employee_id, Nullable<int> fingerprint_id, Nullable<int> employee_fingerprint_id, string employee_name, string employee_address, string phone, string mobile, Nullable<System.DateTime> birth_date, Nullable<System.DateTime> hiring_date, string insurance_no, string insurance_facility, string national_id, string passport_no, Nullable<int> management_id, string nationality, Nullable<int> job_id, Nullable<int> work_period, string driving_license, Nullable<bool> is_required_for_recruitment, Nullable<bool> is_insured, Nullable<bool> is_married, Nullable<bool> is_active, string educational_qualification, Nullable<System.DateTime> graduation_date, string university_evaluation, string experience_years, string notes, Nullable<System.DateTime> date_from, Nullable<System.DateTime> date_to, string user_name)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var employee_idParameter = employee_id.HasValue ?
                new ObjectParameter("employee_id", employee_id) :
                new ObjectParameter("employee_id", typeof(int));
    
            var fingerprint_idParameter = fingerprint_id.HasValue ?
                new ObjectParameter("fingerprint_id", fingerprint_id) :
                new ObjectParameter("fingerprint_id", typeof(int));
    
            var employee_fingerprint_idParameter = employee_fingerprint_id.HasValue ?
                new ObjectParameter("employee_fingerprint_id", employee_fingerprint_id) :
                new ObjectParameter("employee_fingerprint_id", typeof(int));
    
            var employee_nameParameter = employee_name != null ?
                new ObjectParameter("employee_name", employee_name) :
                new ObjectParameter("employee_name", typeof(string));
    
            var employee_addressParameter = employee_address != null ?
                new ObjectParameter("employee_address", employee_address) :
                new ObjectParameter("employee_address", typeof(string));
    
            var phoneParameter = phone != null ?
                new ObjectParameter("phone", phone) :
                new ObjectParameter("phone", typeof(string));
    
            var mobileParameter = mobile != null ?
                new ObjectParameter("mobile", mobile) :
                new ObjectParameter("mobile", typeof(string));
    
            var birth_dateParameter = birth_date.HasValue ?
                new ObjectParameter("birth_date", birth_date) :
                new ObjectParameter("birth_date", typeof(System.DateTime));
    
            var hiring_dateParameter = hiring_date.HasValue ?
                new ObjectParameter("hiring_date", hiring_date) :
                new ObjectParameter("hiring_date", typeof(System.DateTime));
    
            var insurance_noParameter = insurance_no != null ?
                new ObjectParameter("insurance_no", insurance_no) :
                new ObjectParameter("insurance_no", typeof(string));
    
            var insurance_facilityParameter = insurance_facility != null ?
                new ObjectParameter("insurance_facility", insurance_facility) :
                new ObjectParameter("insurance_facility", typeof(string));
    
            var national_idParameter = national_id != null ?
                new ObjectParameter("national_id", national_id) :
                new ObjectParameter("national_id", typeof(string));
    
            var passport_noParameter = passport_no != null ?
                new ObjectParameter("passport_no", passport_no) :
                new ObjectParameter("passport_no", typeof(string));
    
            var management_idParameter = management_id.HasValue ?
                new ObjectParameter("management_id", management_id) :
                new ObjectParameter("management_id", typeof(int));
    
            var nationalityParameter = nationality != null ?
                new ObjectParameter("nationality", nationality) :
                new ObjectParameter("nationality", typeof(string));
    
            var job_idParameter = job_id.HasValue ?
                new ObjectParameter("job_id", job_id) :
                new ObjectParameter("job_id", typeof(int));
    
            var work_periodParameter = work_period.HasValue ?
                new ObjectParameter("work_period", work_period) :
                new ObjectParameter("work_period", typeof(int));
    
            var driving_licenseParameter = driving_license != null ?
                new ObjectParameter("driving_license", driving_license) :
                new ObjectParameter("driving_license", typeof(string));
    
            var is_required_for_recruitmentParameter = is_required_for_recruitment.HasValue ?
                new ObjectParameter("is_required_for_recruitment", is_required_for_recruitment) :
                new ObjectParameter("is_required_for_recruitment", typeof(bool));
    
            var is_insuredParameter = is_insured.HasValue ?
                new ObjectParameter("is_insured", is_insured) :
                new ObjectParameter("is_insured", typeof(bool));
    
            var is_marriedParameter = is_married.HasValue ?
                new ObjectParameter("is_married", is_married) :
                new ObjectParameter("is_married", typeof(bool));
    
            var is_activeParameter = is_active.HasValue ?
                new ObjectParameter("is_active", is_active) :
                new ObjectParameter("is_active", typeof(bool));
    
            var educational_qualificationParameter = educational_qualification != null ?
                new ObjectParameter("educational_qualification", educational_qualification) :
                new ObjectParameter("educational_qualification", typeof(string));
    
            var graduation_dateParameter = graduation_date.HasValue ?
                new ObjectParameter("graduation_date", graduation_date) :
                new ObjectParameter("graduation_date", typeof(System.DateTime));
    
            var university_evaluationParameter = university_evaluation != null ?
                new ObjectParameter("university_evaluation", university_evaluation) :
                new ObjectParameter("university_evaluation", typeof(string));
    
            var experience_yearsParameter = experience_years != null ?
                new ObjectParameter("experience_years", experience_years) :
                new ObjectParameter("experience_years", typeof(string));
    
            var notesParameter = notes != null ?
                new ObjectParameter("notes", notes) :
                new ObjectParameter("notes", typeof(string));
    
            var date_fromParameter = date_from.HasValue ?
                new ObjectParameter("date_from", date_from) :
                new ObjectParameter("date_from", typeof(System.DateTime));
    
            var date_toParameter = date_to.HasValue ?
                new ObjectParameter("date_to", date_to) :
                new ObjectParameter("date_to", typeof(System.DateTime));
    
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("ManageEmployee", checkParameter, employee_idParameter, fingerprint_idParameter, employee_fingerprint_idParameter, employee_nameParameter, employee_addressParameter, phoneParameter, mobileParameter, birth_dateParameter, hiring_dateParameter, insurance_noParameter, insurance_facilityParameter, national_idParameter, passport_noParameter, management_idParameter, nationalityParameter, job_idParameter, work_periodParameter, driving_licenseParameter, is_required_for_recruitmentParameter, is_insuredParameter, is_marriedParameter, is_activeParameter, educational_qualificationParameter, graduation_dateParameter, university_evaluationParameter, experience_yearsParameter, notesParameter, date_fromParameter, date_toParameter, user_nameParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> ManageEmployeeActivity(string check, Nullable<int> activity_id, Nullable<int> employee_id, Nullable<int> salary_item_id, Nullable<decimal> value, Nullable<System.DateTime> activity_date, string notes, string user_name, Nullable<int> management_id, string employee_name, Nullable<System.DateTime> date_from, Nullable<System.DateTime> date_to)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var activity_idParameter = activity_id.HasValue ?
                new ObjectParameter("activity_id", activity_id) :
                new ObjectParameter("activity_id", typeof(int));
    
            var employee_idParameter = employee_id.HasValue ?
                new ObjectParameter("employee_id", employee_id) :
                new ObjectParameter("employee_id", typeof(int));
    
            var salary_item_idParameter = salary_item_id.HasValue ?
                new ObjectParameter("salary_item_id", salary_item_id) :
                new ObjectParameter("salary_item_id", typeof(int));
    
            var valueParameter = value.HasValue ?
                new ObjectParameter("value", value) :
                new ObjectParameter("value", typeof(decimal));
    
            var activity_dateParameter = activity_date.HasValue ?
                new ObjectParameter("activity_date", activity_date) :
                new ObjectParameter("activity_date", typeof(System.DateTime));
    
            var notesParameter = notes != null ?
                new ObjectParameter("notes", notes) :
                new ObjectParameter("notes", typeof(string));
    
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            var management_idParameter = management_id.HasValue ?
                new ObjectParameter("management_id", management_id) :
                new ObjectParameter("management_id", typeof(int));
    
            var employee_nameParameter = employee_name != null ?
                new ObjectParameter("employee_name", employee_name) :
                new ObjectParameter("employee_name", typeof(string));
    
            var date_fromParameter = date_from.HasValue ?
                new ObjectParameter("date_from", date_from) :
                new ObjectParameter("date_from", typeof(System.DateTime));
    
            var date_toParameter = date_to.HasValue ?
                new ObjectParameter("date_to", date_to) :
                new ObjectParameter("date_to", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("ManageEmployeeActivity", checkParameter, activity_idParameter, employee_idParameter, salary_item_idParameter, valueParameter, activity_dateParameter, notesParameter, user_nameParameter, management_idParameter, employee_nameParameter, date_fromParameter, date_toParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> ManageEmployeeAttendanceDeparture(string check, Nullable<int> attendance_departure_id, Nullable<int> management_id, Nullable<int> work_period_id, Nullable<int> employee_id, string employee_name, Nullable<System.DateTime> date_from, Nullable<System.DateTime> date_to, Nullable<System.DateTime> day, Nullable<System.TimeSpan> time, Nullable<int> check_state_id)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var attendance_departure_idParameter = attendance_departure_id.HasValue ?
                new ObjectParameter("attendance_departure_id", attendance_departure_id) :
                new ObjectParameter("attendance_departure_id", typeof(int));
    
            var management_idParameter = management_id.HasValue ?
                new ObjectParameter("management_id", management_id) :
                new ObjectParameter("management_id", typeof(int));
    
            var work_period_idParameter = work_period_id.HasValue ?
                new ObjectParameter("work_period_id", work_period_id) :
                new ObjectParameter("work_period_id", typeof(int));
    
            var employee_idParameter = employee_id.HasValue ?
                new ObjectParameter("employee_id", employee_id) :
                new ObjectParameter("employee_id", typeof(int));
    
            var employee_nameParameter = employee_name != null ?
                new ObjectParameter("employee_name", employee_name) :
                new ObjectParameter("employee_name", typeof(string));
    
            var date_fromParameter = date_from.HasValue ?
                new ObjectParameter("date_from", date_from) :
                new ObjectParameter("date_from", typeof(System.DateTime));
    
            var date_toParameter = date_to.HasValue ?
                new ObjectParameter("date_to", date_to) :
                new ObjectParameter("date_to", typeof(System.DateTime));
    
            var dayParameter = day.HasValue ?
                new ObjectParameter("day", day) :
                new ObjectParameter("day", typeof(System.DateTime));
    
            var timeParameter = time.HasValue ?
                new ObjectParameter("time", time) :
                new ObjectParameter("time", typeof(System.TimeSpan));
    
            var check_state_idParameter = check_state_id.HasValue ?
                new ObjectParameter("check_state_id", check_state_id) :
                new ObjectParameter("check_state_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("ManageEmployeeAttendanceDeparture", checkParameter, attendance_departure_idParameter, management_idParameter, work_period_idParameter, employee_idParameter, employee_nameParameter, date_fromParameter, date_toParameter, dayParameter, timeParameter, check_state_idParameter);
        }
    
        public virtual ObjectResult<ManageEmployeeBorrowingPayments_Result> ManageEmployeeBorrowingPayments(string check, Nullable<int> employee_borrowing_id, Nullable<System.DateTime> payment_default_date, Nullable<decimal> payment_value, Nullable<System.DateTime> payment_actual_date, Nullable<bool> is_paid)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var employee_borrowing_idParameter = employee_borrowing_id.HasValue ?
                new ObjectParameter("employee_borrowing_id", employee_borrowing_id) :
                new ObjectParameter("employee_borrowing_id", typeof(int));
    
            var payment_default_dateParameter = payment_default_date.HasValue ?
                new ObjectParameter("payment_default_date", payment_default_date) :
                new ObjectParameter("payment_default_date", typeof(System.DateTime));
    
            var payment_valueParameter = payment_value.HasValue ?
                new ObjectParameter("payment_value", payment_value) :
                new ObjectParameter("payment_value", typeof(decimal));
    
            var payment_actual_dateParameter = payment_actual_date.HasValue ?
                new ObjectParameter("payment_actual_date", payment_actual_date) :
                new ObjectParameter("payment_actual_date", typeof(System.DateTime));
    
            var is_paidParameter = is_paid.HasValue ?
                new ObjectParameter("is_paid", is_paid) :
                new ObjectParameter("is_paid", typeof(bool));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<ManageEmployeeBorrowingPayments_Result>("ManageEmployeeBorrowingPayments", checkParameter, employee_borrowing_idParameter, payment_default_dateParameter, payment_valueParameter, payment_actual_dateParameter, is_paidParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> ManageEmployeeBorrowings(string check, Nullable<int> employee_borrowing_id, Nullable<int> employee_id, string employee_name, Nullable<System.DateTime> request_date, Nullable<System.DateTime> start_date, Nullable<bool> is_accepted, Nullable<decimal> value, Nullable<int> payment_count, string notes)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var employee_borrowing_idParameter = employee_borrowing_id.HasValue ?
                new ObjectParameter("employee_borrowing_id", employee_borrowing_id) :
                new ObjectParameter("employee_borrowing_id", typeof(int));
    
            var employee_idParameter = employee_id.HasValue ?
                new ObjectParameter("employee_id", employee_id) :
                new ObjectParameter("employee_id", typeof(int));
    
            var employee_nameParameter = employee_name != null ?
                new ObjectParameter("employee_name", employee_name) :
                new ObjectParameter("employee_name", typeof(string));
    
            var request_dateParameter = request_date.HasValue ?
                new ObjectParameter("request_date", request_date) :
                new ObjectParameter("request_date", typeof(System.DateTime));
    
            var start_dateParameter = start_date.HasValue ?
                new ObjectParameter("start_date", start_date) :
                new ObjectParameter("start_date", typeof(System.DateTime));
    
            var is_acceptedParameter = is_accepted.HasValue ?
                new ObjectParameter("is_accepted", is_accepted) :
                new ObjectParameter("is_accepted", typeof(bool));
    
            var valueParameter = value.HasValue ?
                new ObjectParameter("value", value) :
                new ObjectParameter("value", typeof(decimal));
    
            var payment_countParameter = payment_count.HasValue ?
                new ObjectParameter("payment_count", payment_count) :
                new ObjectParameter("payment_count", typeof(int));
    
            var notesParameter = notes != null ?
                new ObjectParameter("notes", notes) :
                new ObjectParameter("notes", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("ManageEmployeeBorrowings", checkParameter, employee_borrowing_idParameter, employee_idParameter, employee_nameParameter, request_dateParameter, start_dateParameter, is_acceptedParameter, valueParameter, payment_countParameter, notesParameter);
        }
    
        public virtual ObjectResult<ManageEmployeePayrollDetails_Result> ManageEmployeePayrollDetails(string check, Nullable<int> payroll_id, Nullable<int> payroll_detail_id, Nullable<int> salary_item_id, Nullable<bool> is_activity, Nullable<decimal> salary_item_value)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var payroll_idParameter = payroll_id.HasValue ?
                new ObjectParameter("payroll_id", payroll_id) :
                new ObjectParameter("payroll_id", typeof(int));
    
            var payroll_detail_idParameter = payroll_detail_id.HasValue ?
                new ObjectParameter("payroll_detail_id", payroll_detail_id) :
                new ObjectParameter("payroll_detail_id", typeof(int));
    
            var salary_item_idParameter = salary_item_id.HasValue ?
                new ObjectParameter("salary_item_id", salary_item_id) :
                new ObjectParameter("salary_item_id", typeof(int));
    
            var is_activityParameter = is_activity.HasValue ?
                new ObjectParameter("is_activity", is_activity) :
                new ObjectParameter("is_activity", typeof(bool));
    
            var salary_item_valueParameter = salary_item_value.HasValue ?
                new ObjectParameter("salary_item_value", salary_item_value) :
                new ObjectParameter("salary_item_value", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<ManageEmployeePayrollDetails_Result>("ManageEmployeePayrollDetails", checkParameter, payroll_idParameter, payroll_detail_idParameter, salary_item_idParameter, is_activityParameter, salary_item_valueParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> ManageEmployeePayrollHeader(string check, Nullable<int> payroll_id, Nullable<int> employee_id, Nullable<int> management_id, string employee_name, Nullable<System.DateTime> tran_date, Nullable<System.DateTime> date_from, Nullable<System.DateTime> date_to, Nullable<int> attendance_days, Nullable<int> absence_days, Nullable<int> delay_minutes, Nullable<int> addition_minutes, Nullable<decimal> work_period_hours, Nullable<decimal> borrow_value)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var payroll_idParameter = payroll_id.HasValue ?
                new ObjectParameter("payroll_id", payroll_id) :
                new ObjectParameter("payroll_id", typeof(int));
    
            var employee_idParameter = employee_id.HasValue ?
                new ObjectParameter("employee_id", employee_id) :
                new ObjectParameter("employee_id", typeof(int));
    
            var management_idParameter = management_id.HasValue ?
                new ObjectParameter("management_id", management_id) :
                new ObjectParameter("management_id", typeof(int));
    
            var employee_nameParameter = employee_name != null ?
                new ObjectParameter("employee_name", employee_name) :
                new ObjectParameter("employee_name", typeof(string));
    
            var tran_dateParameter = tran_date.HasValue ?
                new ObjectParameter("tran_date", tran_date) :
                new ObjectParameter("tran_date", typeof(System.DateTime));
    
            var date_fromParameter = date_from.HasValue ?
                new ObjectParameter("date_from", date_from) :
                new ObjectParameter("date_from", typeof(System.DateTime));
    
            var date_toParameter = date_to.HasValue ?
                new ObjectParameter("date_to", date_to) :
                new ObjectParameter("date_to", typeof(System.DateTime));
    
            var attendance_daysParameter = attendance_days.HasValue ?
                new ObjectParameter("attendance_days", attendance_days) :
                new ObjectParameter("attendance_days", typeof(int));
    
            var absence_daysParameter = absence_days.HasValue ?
                new ObjectParameter("absence_days", absence_days) :
                new ObjectParameter("absence_days", typeof(int));
    
            var delay_minutesParameter = delay_minutes.HasValue ?
                new ObjectParameter("delay_minutes", delay_minutes) :
                new ObjectParameter("delay_minutes", typeof(int));
    
            var addition_minutesParameter = addition_minutes.HasValue ?
                new ObjectParameter("addition_minutes", addition_minutes) :
                new ObjectParameter("addition_minutes", typeof(int));
    
            var work_period_hoursParameter = work_period_hours.HasValue ?
                new ObjectParameter("work_period_hours", work_period_hours) :
                new ObjectParameter("work_period_hours", typeof(decimal));
    
            var borrow_valueParameter = borrow_value.HasValue ?
                new ObjectParameter("borrow_value", borrow_value) :
                new ObjectParameter("borrow_value", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("ManageEmployeePayrollHeader", checkParameter, payroll_idParameter, employee_idParameter, management_idParameter, employee_nameParameter, tran_dateParameter, date_fromParameter, date_toParameter, attendance_daysParameter, absence_daysParameter, delay_minutesParameter, addition_minutesParameter, work_period_hoursParameter, borrow_valueParameter);
        }
    
        public virtual ObjectResult<ManageEmployeeSalaryItems_Result> ManageEmployeeSalaryItems(string check, Nullable<int> employee_id, Nullable<int> employee_salary_item_id, Nullable<int> salary_item_id, Nullable<decimal> salary_item_value)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var employee_idParameter = employee_id.HasValue ?
                new ObjectParameter("employee_id", employee_id) :
                new ObjectParameter("employee_id", typeof(int));
    
            var employee_salary_item_idParameter = employee_salary_item_id.HasValue ?
                new ObjectParameter("employee_salary_item_id", employee_salary_item_id) :
                new ObjectParameter("employee_salary_item_id", typeof(int));
    
            var salary_item_idParameter = salary_item_id.HasValue ?
                new ObjectParameter("salary_item_id", salary_item_id) :
                new ObjectParameter("salary_item_id", typeof(int));
    
            var salary_item_valueParameter = salary_item_value.HasValue ?
                new ObjectParameter("salary_item_value", salary_item_value) :
                new ObjectParameter("salary_item_value", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<ManageEmployeeSalaryItems_Result>("ManageEmployeeSalaryItems", checkParameter, employee_idParameter, employee_salary_item_idParameter, salary_item_idParameter, salary_item_valueParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> ManageEmployeeVacations(string check, Nullable<int> vacation_id, Nullable<int> employee_id, string employee_name, Nullable<int> vacation_type_id, Nullable<System.DateTime> add_date, Nullable<System.DateTime> vacation_date_from, Nullable<System.DateTime> vacation_date_to, string user_name, string notes)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var vacation_idParameter = vacation_id.HasValue ?
                new ObjectParameter("vacation_id", vacation_id) :
                new ObjectParameter("vacation_id", typeof(int));
    
            var employee_idParameter = employee_id.HasValue ?
                new ObjectParameter("employee_id", employee_id) :
                new ObjectParameter("employee_id", typeof(int));
    
            var employee_nameParameter = employee_name != null ?
                new ObjectParameter("employee_name", employee_name) :
                new ObjectParameter("employee_name", typeof(string));
    
            var vacation_type_idParameter = vacation_type_id.HasValue ?
                new ObjectParameter("vacation_type_id", vacation_type_id) :
                new ObjectParameter("vacation_type_id", typeof(int));
    
            var add_dateParameter = add_date.HasValue ?
                new ObjectParameter("add_date", add_date) :
                new ObjectParameter("add_date", typeof(System.DateTime));
    
            var vacation_date_fromParameter = vacation_date_from.HasValue ?
                new ObjectParameter("vacation_date_from", vacation_date_from) :
                new ObjectParameter("vacation_date_from", typeof(System.DateTime));
    
            var vacation_date_toParameter = vacation_date_to.HasValue ?
                new ObjectParameter("vacation_date_to", vacation_date_to) :
                new ObjectParameter("vacation_date_to", typeof(System.DateTime));
    
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            var notesParameter = notes != null ?
                new ObjectParameter("notes", notes) :
                new ObjectParameter("notes", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("ManageEmployeeVacations", checkParameter, vacation_idParameter, employee_idParameter, employee_nameParameter, vacation_type_idParameter, add_dateParameter, vacation_date_fromParameter, vacation_date_toParameter, user_nameParameter, notesParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> ManageExpenses(string check, Nullable<int> expense_id, Nullable<int> parent_id, string expense_name, string notes, string user_name)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var expense_idParameter = expense_id.HasValue ?
                new ObjectParameter("expense_id", expense_id) :
                new ObjectParameter("expense_id", typeof(int));
    
            var parent_idParameter = parent_id.HasValue ?
                new ObjectParameter("parent_id", parent_id) :
                new ObjectParameter("parent_id", typeof(int));
    
            var expense_nameParameter = expense_name != null ?
                new ObjectParameter("expense_name", expense_name) :
                new ObjectParameter("expense_name", typeof(string));
    
            var notesParameter = notes != null ?
                new ObjectParameter("notes", notes) :
                new ObjectParameter("notes", typeof(string));
    
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("ManageExpenses", checkParameter, expense_idParameter, parent_idParameter, expense_nameParameter, notesParameter, user_nameParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> ManageHalls(string check, Nullable<int> hall_id, string hall_name, string notes)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var hall_idParameter = hall_id.HasValue ?
                new ObjectParameter("hall_id", hall_id) :
                new ObjectParameter("hall_id", typeof(int));
    
            var hall_nameParameter = hall_name != null ?
                new ObjectParameter("hall_name", hall_name) :
                new ObjectParameter("hall_name", typeof(string));
    
            var notesParameter = notes != null ?
                new ObjectParameter("notes", notes) :
                new ObjectParameter("notes", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("ManageHalls", checkParameter, hall_idParameter, hall_nameParameter, notesParameter);
        }
    
        public virtual int manageInstallmentPaids(string check, Nullable<int> paid_id, Nullable<int> inv_id, Nullable<int> cust_id, Nullable<System.DateTime> paid_required_date, Nullable<System.DateTime> collecting_date, Nullable<bool> paid_status, Nullable<int> treasury_id, Nullable<int> bank_account_id, Nullable<decimal> value, Nullable<decimal> paid, string user_name)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var paid_idParameter = paid_id.HasValue ?
                new ObjectParameter("paid_id", paid_id) :
                new ObjectParameter("paid_id", typeof(int));
    
            var inv_idParameter = inv_id.HasValue ?
                new ObjectParameter("inv_id", inv_id) :
                new ObjectParameter("inv_id", typeof(int));
    
            var cust_idParameter = cust_id.HasValue ?
                new ObjectParameter("cust_id", cust_id) :
                new ObjectParameter("cust_id", typeof(int));
    
            var paid_required_dateParameter = paid_required_date.HasValue ?
                new ObjectParameter("paid_required_date", paid_required_date) :
                new ObjectParameter("paid_required_date", typeof(System.DateTime));
    
            var collecting_dateParameter = collecting_date.HasValue ?
                new ObjectParameter("collecting_date", collecting_date) :
                new ObjectParameter("collecting_date", typeof(System.DateTime));
    
            var paid_statusParameter = paid_status.HasValue ?
                new ObjectParameter("paid_status", paid_status) :
                new ObjectParameter("paid_status", typeof(bool));
    
            var treasury_idParameter = treasury_id.HasValue ?
                new ObjectParameter("treasury_id", treasury_id) :
                new ObjectParameter("treasury_id", typeof(int));
    
            var bank_account_idParameter = bank_account_id.HasValue ?
                new ObjectParameter("bank_account_id", bank_account_id) :
                new ObjectParameter("bank_account_id", typeof(int));
    
            var valueParameter = value.HasValue ?
                new ObjectParameter("value", value) :
                new ObjectParameter("value", typeof(decimal));
    
            var paidParameter = paid.HasValue ?
                new ObjectParameter("paid", paid) :
                new ObjectParameter("paid", typeof(decimal));
    
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("manageInstallmentPaids", checkParameter, paid_idParameter, inv_idParameter, cust_idParameter, paid_required_dateParameter, collecting_dateParameter, paid_statusParameter, treasury_idParameter, bank_account_idParameter, valueParameter, paidParameter, user_nameParameter);
        }
    
        public virtual int ManageInventoryPermission(string check, Nullable<int> per_id, Nullable<int> maintenance_id, Nullable<int> per_no, Nullable<int> per_kind, Nullable<int> stock_id, Nullable<int> stock_id_to, Nullable<int> stock_id_from, Nullable<System.DateTime> per_date, string notes, string user_name, Nullable<int> per_detail_id, Nullable<long> pro_id, string pro_serial, Nullable<System.DateTime> pro_expirey, Nullable<int> unit_id, Nullable<decimal> quantity, Nullable<decimal> price, Nullable<decimal> cost_price, Nullable<System.DateTime> cost_price_date)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var per_idParameter = per_id.HasValue ?
                new ObjectParameter("per_id", per_id) :
                new ObjectParameter("per_id", typeof(int));
    
            var maintenance_idParameter = maintenance_id.HasValue ?
                new ObjectParameter("maintenance_id", maintenance_id) :
                new ObjectParameter("maintenance_id", typeof(int));
    
            var per_noParameter = per_no.HasValue ?
                new ObjectParameter("per_no", per_no) :
                new ObjectParameter("per_no", typeof(int));
    
            var per_kindParameter = per_kind.HasValue ?
                new ObjectParameter("per_kind", per_kind) :
                new ObjectParameter("per_kind", typeof(int));
    
            var stock_idParameter = stock_id.HasValue ?
                new ObjectParameter("stock_id", stock_id) :
                new ObjectParameter("stock_id", typeof(int));
    
            var stock_id_toParameter = stock_id_to.HasValue ?
                new ObjectParameter("stock_id_to", stock_id_to) :
                new ObjectParameter("stock_id_to", typeof(int));
    
            var stock_id_fromParameter = stock_id_from.HasValue ?
                new ObjectParameter("stock_id_from", stock_id_from) :
                new ObjectParameter("stock_id_from", typeof(int));
    
            var per_dateParameter = per_date.HasValue ?
                new ObjectParameter("per_date", per_date) :
                new ObjectParameter("per_date", typeof(System.DateTime));
    
            var notesParameter = notes != null ?
                new ObjectParameter("notes", notes) :
                new ObjectParameter("notes", typeof(string));
    
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            var per_detail_idParameter = per_detail_id.HasValue ?
                new ObjectParameter("per_detail_id", per_detail_id) :
                new ObjectParameter("per_detail_id", typeof(int));
    
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            var pro_serialParameter = pro_serial != null ?
                new ObjectParameter("pro_serial", pro_serial) :
                new ObjectParameter("pro_serial", typeof(string));
    
            var pro_expireyParameter = pro_expirey.HasValue ?
                new ObjectParameter("pro_expirey", pro_expirey) :
                new ObjectParameter("pro_expirey", typeof(System.DateTime));
    
            var unit_idParameter = unit_id.HasValue ?
                new ObjectParameter("unit_id", unit_id) :
                new ObjectParameter("unit_id", typeof(int));
    
            var quantityParameter = quantity.HasValue ?
                new ObjectParameter("quantity", quantity) :
                new ObjectParameter("quantity", typeof(decimal));
    
            var priceParameter = price.HasValue ?
                new ObjectParameter("price", price) :
                new ObjectParameter("price", typeof(decimal));
    
            var cost_priceParameter = cost_price.HasValue ?
                new ObjectParameter("cost_price", cost_price) :
                new ObjectParameter("cost_price", typeof(decimal));
    
            var cost_price_dateParameter = cost_price_date.HasValue ?
                new ObjectParameter("cost_price_date", cost_price_date) :
                new ObjectParameter("cost_price_date", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("ManageInventoryPermission", checkParameter, per_idParameter, maintenance_idParameter, per_noParameter, per_kindParameter, stock_idParameter, stock_id_toParameter, stock_id_fromParameter, per_dateParameter, notesParameter, user_nameParameter, per_detail_idParameter, pro_idParameter, pro_serialParameter, pro_expireyParameter, unit_idParameter, quantityParameter, priceParameter, cost_priceParameter, cost_price_dateParameter);
        }
    
        public virtual int manageinvoice(string check, Nullable<long> inv_id, Nullable<int> inv_no, Nullable<long> reference_sale_inv_id, Nullable<int> cost_center_id, Nullable<System.DateTime> inv_date, Nullable<System.DateTime> tran_date, Nullable<int> stock_id, Nullable<int> cust_id, Nullable<int> inv_kind_id, Nullable<int> payment_id, Nullable<int> order_type, Nullable<int> order_status, Nullable<int> delivery_id, Nullable<System.DateTime> delivery_start_time, Nullable<System.DateTime> delivery_end_time, Nullable<int> table_id, Nullable<int> table_members_count, Nullable<int> treasury_id, Nullable<int> sale_rep_id, Nullable<decimal> paid, Nullable<decimal> cust_resceipient, Nullable<int> installment_count, Nullable<decimal> installment_value, Nullable<int> installment_method, Nullable<int> discount_kind, Nullable<decimal> discount, Nullable<decimal> discount2, Nullable<decimal> addition, Nullable<decimal> sales_tax, Nullable<decimal> service, string notes, Nullable<int> inv_det_id, Nullable<long> pro_id, string pro_serial, Nullable<System.DateTime> pro_expirey, Nullable<int> unit_id, Nullable<decimal> quantity, Nullable<decimal> price, Nullable<decimal> cost_price, Nullable<decimal> quantity_out, Nullable<decimal> pro_discount, Nullable<decimal> vAT, string pro_notes, string user_name)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var inv_idParameter = inv_id.HasValue ?
                new ObjectParameter("inv_id", inv_id) :
                new ObjectParameter("inv_id", typeof(long));
    
            var inv_noParameter = inv_no.HasValue ?
                new ObjectParameter("inv_no", inv_no) :
                new ObjectParameter("inv_no", typeof(int));
    
            var reference_sale_inv_idParameter = reference_sale_inv_id.HasValue ?
                new ObjectParameter("reference_sale_inv_id", reference_sale_inv_id) :
                new ObjectParameter("reference_sale_inv_id", typeof(long));
    
            var cost_center_idParameter = cost_center_id.HasValue ?
                new ObjectParameter("cost_center_id", cost_center_id) :
                new ObjectParameter("cost_center_id", typeof(int));
    
            var inv_dateParameter = inv_date.HasValue ?
                new ObjectParameter("inv_date", inv_date) :
                new ObjectParameter("inv_date", typeof(System.DateTime));
    
            var tran_dateParameter = tran_date.HasValue ?
                new ObjectParameter("tran_date", tran_date) :
                new ObjectParameter("tran_date", typeof(System.DateTime));
    
            var stock_idParameter = stock_id.HasValue ?
                new ObjectParameter("stock_id", stock_id) :
                new ObjectParameter("stock_id", typeof(int));
    
            var cust_idParameter = cust_id.HasValue ?
                new ObjectParameter("cust_id", cust_id) :
                new ObjectParameter("cust_id", typeof(int));
    
            var inv_kind_idParameter = inv_kind_id.HasValue ?
                new ObjectParameter("inv_kind_id", inv_kind_id) :
                new ObjectParameter("inv_kind_id", typeof(int));
    
            var payment_idParameter = payment_id.HasValue ?
                new ObjectParameter("payment_id", payment_id) :
                new ObjectParameter("payment_id", typeof(int));
    
            var order_typeParameter = order_type.HasValue ?
                new ObjectParameter("order_type", order_type) :
                new ObjectParameter("order_type", typeof(int));
    
            var order_statusParameter = order_status.HasValue ?
                new ObjectParameter("order_status", order_status) :
                new ObjectParameter("order_status", typeof(int));
    
            var delivery_idParameter = delivery_id.HasValue ?
                new ObjectParameter("delivery_id", delivery_id) :
                new ObjectParameter("delivery_id", typeof(int));
    
            var delivery_start_timeParameter = delivery_start_time.HasValue ?
                new ObjectParameter("delivery_start_time", delivery_start_time) :
                new ObjectParameter("delivery_start_time", typeof(System.DateTime));
    
            var delivery_end_timeParameter = delivery_end_time.HasValue ?
                new ObjectParameter("delivery_end_time", delivery_end_time) :
                new ObjectParameter("delivery_end_time", typeof(System.DateTime));
    
            var table_idParameter = table_id.HasValue ?
                new ObjectParameter("table_id", table_id) :
                new ObjectParameter("table_id", typeof(int));
    
            var table_members_countParameter = table_members_count.HasValue ?
                new ObjectParameter("table_members_count", table_members_count) :
                new ObjectParameter("table_members_count", typeof(int));
    
            var treasury_idParameter = treasury_id.HasValue ?
                new ObjectParameter("treasury_id", treasury_id) :
                new ObjectParameter("treasury_id", typeof(int));
    
            var sale_rep_idParameter = sale_rep_id.HasValue ?
                new ObjectParameter("sale_rep_id", sale_rep_id) :
                new ObjectParameter("sale_rep_id", typeof(int));
    
            var paidParameter = paid.HasValue ?
                new ObjectParameter("paid", paid) :
                new ObjectParameter("paid", typeof(decimal));
    
            var cust_resceipientParameter = cust_resceipient.HasValue ?
                new ObjectParameter("cust_resceipient", cust_resceipient) :
                new ObjectParameter("cust_resceipient", typeof(decimal));
    
            var installment_countParameter = installment_count.HasValue ?
                new ObjectParameter("installment_count", installment_count) :
                new ObjectParameter("installment_count", typeof(int));
    
            var installment_valueParameter = installment_value.HasValue ?
                new ObjectParameter("installment_value", installment_value) :
                new ObjectParameter("installment_value", typeof(decimal));
    
            var installment_methodParameter = installment_method.HasValue ?
                new ObjectParameter("installment_method", installment_method) :
                new ObjectParameter("installment_method", typeof(int));
    
            var discount_kindParameter = discount_kind.HasValue ?
                new ObjectParameter("discount_kind", discount_kind) :
                new ObjectParameter("discount_kind", typeof(int));
    
            var discountParameter = discount.HasValue ?
                new ObjectParameter("discount", discount) :
                new ObjectParameter("discount", typeof(decimal));
    
            var discount2Parameter = discount2.HasValue ?
                new ObjectParameter("discount2", discount2) :
                new ObjectParameter("discount2", typeof(decimal));
    
            var additionParameter = addition.HasValue ?
                new ObjectParameter("addition", addition) :
                new ObjectParameter("addition", typeof(decimal));
    
            var sales_taxParameter = sales_tax.HasValue ?
                new ObjectParameter("sales_tax", sales_tax) :
                new ObjectParameter("sales_tax", typeof(decimal));
    
            var serviceParameter = service.HasValue ?
                new ObjectParameter("service", service) :
                new ObjectParameter("service", typeof(decimal));
    
            var notesParameter = notes != null ?
                new ObjectParameter("notes", notes) :
                new ObjectParameter("notes", typeof(string));
    
            var inv_det_idParameter = inv_det_id.HasValue ?
                new ObjectParameter("inv_det_id", inv_det_id) :
                new ObjectParameter("inv_det_id", typeof(int));
    
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            var pro_serialParameter = pro_serial != null ?
                new ObjectParameter("pro_serial", pro_serial) :
                new ObjectParameter("pro_serial", typeof(string));
    
            var pro_expireyParameter = pro_expirey.HasValue ?
                new ObjectParameter("pro_expirey", pro_expirey) :
                new ObjectParameter("pro_expirey", typeof(System.DateTime));
    
            var unit_idParameter = unit_id.HasValue ?
                new ObjectParameter("unit_id", unit_id) :
                new ObjectParameter("unit_id", typeof(int));
    
            var quantityParameter = quantity.HasValue ?
                new ObjectParameter("quantity", quantity) :
                new ObjectParameter("quantity", typeof(decimal));
    
            var priceParameter = price.HasValue ?
                new ObjectParameter("price", price) :
                new ObjectParameter("price", typeof(decimal));
    
            var cost_priceParameter = cost_price.HasValue ?
                new ObjectParameter("cost_price", cost_price) :
                new ObjectParameter("cost_price", typeof(decimal));
    
            var quantity_outParameter = quantity_out.HasValue ?
                new ObjectParameter("quantity_out", quantity_out) :
                new ObjectParameter("quantity_out", typeof(decimal));
    
            var pro_discountParameter = pro_discount.HasValue ?
                new ObjectParameter("pro_discount", pro_discount) :
                new ObjectParameter("pro_discount", typeof(decimal));
    
            var vATParameter = vAT.HasValue ?
                new ObjectParameter("VAT", vAT) :
                new ObjectParameter("VAT", typeof(decimal));
    
            var pro_notesParameter = pro_notes != null ?
                new ObjectParameter("pro_notes", pro_notes) :
                new ObjectParameter("pro_notes", typeof(string));
    
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("manageinvoice", checkParameter, inv_idParameter, inv_noParameter, reference_sale_inv_idParameter, cost_center_idParameter, inv_dateParameter, tran_dateParameter, stock_idParameter, cust_idParameter, inv_kind_idParameter, payment_idParameter, order_typeParameter, order_statusParameter, delivery_idParameter, delivery_start_timeParameter, delivery_end_timeParameter, table_idParameter, table_members_countParameter, treasury_idParameter, sale_rep_idParameter, paidParameter, cust_resceipientParameter, installment_countParameter, installment_valueParameter, installment_methodParameter, discount_kindParameter, discountParameter, discount2Parameter, additionParameter, sales_taxParameter, serviceParameter, notesParameter, inv_det_idParameter, pro_idParameter, pro_serialParameter, pro_expireyParameter, unit_idParameter, quantityParameter, priceParameter, cost_priceParameter, quantity_outParameter, pro_discountParameter, vATParameter, pro_notesParameter, user_nameParameter);
        }
    
        public virtual int ManageInvoiceDetail(string check, Nullable<int> inv_id, Nullable<int> inv_det_id, Nullable<long> pro_id, Nullable<int> unit_id, Nullable<int> stock_id, Nullable<decimal> quantity, Nullable<decimal> price, Nullable<decimal> pro_discount, string pro_notes)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var inv_idParameter = inv_id.HasValue ?
                new ObjectParameter("inv_id", inv_id) :
                new ObjectParameter("inv_id", typeof(int));
    
            var inv_det_idParameter = inv_det_id.HasValue ?
                new ObjectParameter("inv_det_id", inv_det_id) :
                new ObjectParameter("inv_det_id", typeof(int));
    
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            var unit_idParameter = unit_id.HasValue ?
                new ObjectParameter("unit_id", unit_id) :
                new ObjectParameter("unit_id", typeof(int));
    
            var stock_idParameter = stock_id.HasValue ?
                new ObjectParameter("stock_id", stock_id) :
                new ObjectParameter("stock_id", typeof(int));
    
            var quantityParameter = quantity.HasValue ?
                new ObjectParameter("quantity", quantity) :
                new ObjectParameter("quantity", typeof(decimal));
    
            var priceParameter = price.HasValue ?
                new ObjectParameter("price", price) :
                new ObjectParameter("price", typeof(decimal));
    
            var pro_discountParameter = pro_discount.HasValue ?
                new ObjectParameter("pro_discount", pro_discount) :
                new ObjectParameter("pro_discount", typeof(decimal));
    
            var pro_notesParameter = pro_notes != null ?
                new ObjectParameter("pro_notes", pro_notes) :
                new ObjectParameter("pro_notes", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("ManageInvoiceDetail", checkParameter, inv_idParameter, inv_det_idParameter, pro_idParameter, unit_idParameter, stock_idParameter, quantityParameter, priceParameter, pro_discountParameter, pro_notesParameter);
        }
    
        public virtual int ManageInvoiceHeader(string check, Nullable<int> inv_id, Nullable<int> inv_no, Nullable<System.DateTime> inv_date, Nullable<System.DateTime> tran_date, Nullable<int> stock_id, Nullable<int> cust_id, Nullable<int> inv_kind_id, Nullable<int> payment_id, Nullable<int> treasury_id, Nullable<int> sale_rep_id, Nullable<int> discount_kind, Nullable<decimal> discount, Nullable<decimal> discount2, Nullable<decimal> addition, Nullable<decimal> sales_tax, string notes, Nullable<decimal> paid, string user_name)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var inv_idParameter = inv_id.HasValue ?
                new ObjectParameter("inv_id", inv_id) :
                new ObjectParameter("inv_id", typeof(int));
    
            var inv_noParameter = inv_no.HasValue ?
                new ObjectParameter("inv_no", inv_no) :
                new ObjectParameter("inv_no", typeof(int));
    
            var inv_dateParameter = inv_date.HasValue ?
                new ObjectParameter("inv_date", inv_date) :
                new ObjectParameter("inv_date", typeof(System.DateTime));
    
            var tran_dateParameter = tran_date.HasValue ?
                new ObjectParameter("tran_date", tran_date) :
                new ObjectParameter("tran_date", typeof(System.DateTime));
    
            var stock_idParameter = stock_id.HasValue ?
                new ObjectParameter("stock_id", stock_id) :
                new ObjectParameter("stock_id", typeof(int));
    
            var cust_idParameter = cust_id.HasValue ?
                new ObjectParameter("cust_id", cust_id) :
                new ObjectParameter("cust_id", typeof(int));
    
            var inv_kind_idParameter = inv_kind_id.HasValue ?
                new ObjectParameter("inv_kind_id", inv_kind_id) :
                new ObjectParameter("inv_kind_id", typeof(int));
    
            var payment_idParameter = payment_id.HasValue ?
                new ObjectParameter("payment_id", payment_id) :
                new ObjectParameter("payment_id", typeof(int));
    
            var treasury_idParameter = treasury_id.HasValue ?
                new ObjectParameter("treasury_id", treasury_id) :
                new ObjectParameter("treasury_id", typeof(int));
    
            var sale_rep_idParameter = sale_rep_id.HasValue ?
                new ObjectParameter("sale_rep_id", sale_rep_id) :
                new ObjectParameter("sale_rep_id", typeof(int));
    
            var discount_kindParameter = discount_kind.HasValue ?
                new ObjectParameter("discount_kind", discount_kind) :
                new ObjectParameter("discount_kind", typeof(int));
    
            var discountParameter = discount.HasValue ?
                new ObjectParameter("discount", discount) :
                new ObjectParameter("discount", typeof(decimal));
    
            var discount2Parameter = discount2.HasValue ?
                new ObjectParameter("discount2", discount2) :
                new ObjectParameter("discount2", typeof(decimal));
    
            var additionParameter = addition.HasValue ?
                new ObjectParameter("addition", addition) :
                new ObjectParameter("addition", typeof(decimal));
    
            var sales_taxParameter = sales_tax.HasValue ?
                new ObjectParameter("sales_tax", sales_tax) :
                new ObjectParameter("sales_tax", typeof(decimal));
    
            var notesParameter = notes != null ?
                new ObjectParameter("notes", notes) :
                new ObjectParameter("notes", typeof(string));
    
            var paidParameter = paid.HasValue ?
                new ObjectParameter("paid", paid) :
                new ObjectParameter("paid", typeof(decimal));
    
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("ManageInvoiceHeader", checkParameter, inv_idParameter, inv_noParameter, inv_dateParameter, tran_dateParameter, stock_idParameter, cust_idParameter, inv_kind_idParameter, payment_idParameter, treasury_idParameter, sale_rep_idParameter, discount_kindParameter, discountParameter, discount2Parameter, additionParameter, sales_taxParameter, notesParameter, paidParameter, user_nameParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> ManageJobs(string check, Nullable<int> job_id, string job_name, string notes, string user_name)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var job_idParameter = job_id.HasValue ?
                new ObjectParameter("job_id", job_id) :
                new ObjectParameter("job_id", typeof(int));
    
            var job_nameParameter = job_name != null ?
                new ObjectParameter("job_name", job_name) :
                new ObjectParameter("job_name", typeof(string));
    
            var notesParameter = notes != null ?
                new ObjectParameter("notes", notes) :
                new ObjectParameter("notes", typeof(string));
    
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("ManageJobs", checkParameter, job_idParameter, job_nameParameter, notesParameter, user_nameParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> ManageKitchen(string check, Nullable<int> kitchen_id, string kitchen_name, string printer_name)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var kitchen_idParameter = kitchen_id.HasValue ?
                new ObjectParameter("kitchen_id", kitchen_id) :
                new ObjectParameter("kitchen_id", typeof(int));
    
            var kitchen_nameParameter = kitchen_name != null ?
                new ObjectParameter("kitchen_name", kitchen_name) :
                new ObjectParameter("kitchen_name", typeof(string));
    
            var printer_nameParameter = printer_name != null ?
                new ObjectParameter("printer_name", printer_name) :
                new ObjectParameter("printer_name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("ManageKitchen", checkParameter, kitchen_idParameter, kitchen_nameParameter, printer_nameParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> ManageKitchenProducts(string check, Nullable<int> kitchen_pro_id, Nullable<int> kitchen_id, Nullable<int> pro_id, string pro_name)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var kitchen_pro_idParameter = kitchen_pro_id.HasValue ?
                new ObjectParameter("kitchen_pro_id", kitchen_pro_id) :
                new ObjectParameter("kitchen_pro_id", typeof(int));
    
            var kitchen_idParameter = kitchen_id.HasValue ?
                new ObjectParameter("kitchen_id", kitchen_id) :
                new ObjectParameter("kitchen_id", typeof(int));
    
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(int));
    
            var pro_nameParameter = pro_name != null ?
                new ObjectParameter("pro_name", pro_name) :
                new ObjectParameter("pro_name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("ManageKitchenProducts", checkParameter, kitchen_pro_idParameter, kitchen_idParameter, pro_idParameter, pro_nameParameter);
        }
    
        public virtual int ManageMaintenance(string check, Nullable<int> maintenance_id, Nullable<int> cost_center_id, Nullable<int> cust_id, string cust_name, string cust_phone, string cust_mobile, string cust_city, string cust_area, string cust_address, Nullable<int> cust_status, Nullable<System.DateTime> com_date, Nullable<System.DateTime> go_date, Nullable<System.DateTime> draw_date, Nullable<System.DateTime> delivery_date, Nullable<System.DateTime> maintenance_entry_date, string item_category, string item, string item_serial, string item_color, string item_accessories, string damage_category, string damage, string customer_complaint, Nullable<decimal> maintenance_limit, Nullable<decimal> maintenance_indexation, Nullable<decimal> go_cost, Nullable<decimal> draw_cost, Nullable<decimal> delivery_cost, Nullable<decimal> other_cost, Nullable<int> treas_id, Nullable<int> treas_Expense_per_id, Nullable<int> expense_id, Nullable<int> treas_revenue_per_id, Nullable<int> revenue_id, Nullable<int> visit_eng_id, string maintenance_eng_id, Nullable<decimal> maintenance_eng_ratio, Nullable<int> workshop_id, Nullable<bool> is_paid, Nullable<int> maintenance_status, Nullable<bool> is_fixed, string visit_eng_report, string maintenance_eng_report, string maintenance_report, string user_name)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var maintenance_idParameter = maintenance_id.HasValue ?
                new ObjectParameter("maintenance_id", maintenance_id) :
                new ObjectParameter("maintenance_id", typeof(int));
    
            var cost_center_idParameter = cost_center_id.HasValue ?
                new ObjectParameter("cost_center_id", cost_center_id) :
                new ObjectParameter("cost_center_id", typeof(int));
    
            var cust_idParameter = cust_id.HasValue ?
                new ObjectParameter("cust_id", cust_id) :
                new ObjectParameter("cust_id", typeof(int));
    
            var cust_nameParameter = cust_name != null ?
                new ObjectParameter("cust_name", cust_name) :
                new ObjectParameter("cust_name", typeof(string));
    
            var cust_phoneParameter = cust_phone != null ?
                new ObjectParameter("cust_phone", cust_phone) :
                new ObjectParameter("cust_phone", typeof(string));
    
            var cust_mobileParameter = cust_mobile != null ?
                new ObjectParameter("cust_mobile", cust_mobile) :
                new ObjectParameter("cust_mobile", typeof(string));
    
            var cust_cityParameter = cust_city != null ?
                new ObjectParameter("cust_city", cust_city) :
                new ObjectParameter("cust_city", typeof(string));
    
            var cust_areaParameter = cust_area != null ?
                new ObjectParameter("cust_area", cust_area) :
                new ObjectParameter("cust_area", typeof(string));
    
            var cust_addressParameter = cust_address != null ?
                new ObjectParameter("cust_address", cust_address) :
                new ObjectParameter("cust_address", typeof(string));
    
            var cust_statusParameter = cust_status.HasValue ?
                new ObjectParameter("cust_status", cust_status) :
                new ObjectParameter("cust_status", typeof(int));
    
            var com_dateParameter = com_date.HasValue ?
                new ObjectParameter("com_date", com_date) :
                new ObjectParameter("com_date", typeof(System.DateTime));
    
            var go_dateParameter = go_date.HasValue ?
                new ObjectParameter("go_date", go_date) :
                new ObjectParameter("go_date", typeof(System.DateTime));
    
            var draw_dateParameter = draw_date.HasValue ?
                new ObjectParameter("draw_date", draw_date) :
                new ObjectParameter("draw_date", typeof(System.DateTime));
    
            var delivery_dateParameter = delivery_date.HasValue ?
                new ObjectParameter("delivery_date", delivery_date) :
                new ObjectParameter("delivery_date", typeof(System.DateTime));
    
            var maintenance_entry_dateParameter = maintenance_entry_date.HasValue ?
                new ObjectParameter("maintenance_entry_date", maintenance_entry_date) :
                new ObjectParameter("maintenance_entry_date", typeof(System.DateTime));
    
            var item_categoryParameter = item_category != null ?
                new ObjectParameter("item_category", item_category) :
                new ObjectParameter("item_category", typeof(string));
    
            var itemParameter = item != null ?
                new ObjectParameter("item", item) :
                new ObjectParameter("item", typeof(string));
    
            var item_serialParameter = item_serial != null ?
                new ObjectParameter("item_serial", item_serial) :
                new ObjectParameter("item_serial", typeof(string));
    
            var item_colorParameter = item_color != null ?
                new ObjectParameter("item_color", item_color) :
                new ObjectParameter("item_color", typeof(string));
    
            var item_accessoriesParameter = item_accessories != null ?
                new ObjectParameter("item_accessories", item_accessories) :
                new ObjectParameter("item_accessories", typeof(string));
    
            var damage_categoryParameter = damage_category != null ?
                new ObjectParameter("damage_category", damage_category) :
                new ObjectParameter("damage_category", typeof(string));
    
            var damageParameter = damage != null ?
                new ObjectParameter("damage", damage) :
                new ObjectParameter("damage", typeof(string));
    
            var customer_complaintParameter = customer_complaint != null ?
                new ObjectParameter("customer_complaint", customer_complaint) :
                new ObjectParameter("customer_complaint", typeof(string));
    
            var maintenance_limitParameter = maintenance_limit.HasValue ?
                new ObjectParameter("maintenance_limit", maintenance_limit) :
                new ObjectParameter("maintenance_limit", typeof(decimal));
    
            var maintenance_indexationParameter = maintenance_indexation.HasValue ?
                new ObjectParameter("maintenance_indexation", maintenance_indexation) :
                new ObjectParameter("maintenance_indexation", typeof(decimal));
    
            var go_costParameter = go_cost.HasValue ?
                new ObjectParameter("go_cost", go_cost) :
                new ObjectParameter("go_cost", typeof(decimal));
    
            var draw_costParameter = draw_cost.HasValue ?
                new ObjectParameter("draw_cost", draw_cost) :
                new ObjectParameter("draw_cost", typeof(decimal));
    
            var delivery_costParameter = delivery_cost.HasValue ?
                new ObjectParameter("delivery_cost", delivery_cost) :
                new ObjectParameter("delivery_cost", typeof(decimal));
    
            var other_costParameter = other_cost.HasValue ?
                new ObjectParameter("other_cost", other_cost) :
                new ObjectParameter("other_cost", typeof(decimal));
    
            var treas_idParameter = treas_id.HasValue ?
                new ObjectParameter("treas_id", treas_id) :
                new ObjectParameter("treas_id", typeof(int));
    
            var treas_Expense_per_idParameter = treas_Expense_per_id.HasValue ?
                new ObjectParameter("treas_Expense_per_id", treas_Expense_per_id) :
                new ObjectParameter("treas_Expense_per_id", typeof(int));
    
            var expense_idParameter = expense_id.HasValue ?
                new ObjectParameter("expense_id", expense_id) :
                new ObjectParameter("expense_id", typeof(int));
    
            var treas_revenue_per_idParameter = treas_revenue_per_id.HasValue ?
                new ObjectParameter("treas_revenue_per_id", treas_revenue_per_id) :
                new ObjectParameter("treas_revenue_per_id", typeof(int));
    
            var revenue_idParameter = revenue_id.HasValue ?
                new ObjectParameter("revenue_id", revenue_id) :
                new ObjectParameter("revenue_id", typeof(int));
    
            var visit_eng_idParameter = visit_eng_id.HasValue ?
                new ObjectParameter("visit_eng_id", visit_eng_id) :
                new ObjectParameter("visit_eng_id", typeof(int));
    
            var maintenance_eng_idParameter = maintenance_eng_id != null ?
                new ObjectParameter("maintenance_eng_id", maintenance_eng_id) :
                new ObjectParameter("maintenance_eng_id", typeof(string));
    
            var maintenance_eng_ratioParameter = maintenance_eng_ratio.HasValue ?
                new ObjectParameter("maintenance_eng_ratio", maintenance_eng_ratio) :
                new ObjectParameter("maintenance_eng_ratio", typeof(decimal));
    
            var workshop_idParameter = workshop_id.HasValue ?
                new ObjectParameter("workshop_id", workshop_id) :
                new ObjectParameter("workshop_id", typeof(int));
    
            var is_paidParameter = is_paid.HasValue ?
                new ObjectParameter("is_paid", is_paid) :
                new ObjectParameter("is_paid", typeof(bool));
    
            var maintenance_statusParameter = maintenance_status.HasValue ?
                new ObjectParameter("maintenance_status", maintenance_status) :
                new ObjectParameter("maintenance_status", typeof(int));
    
            var is_fixedParameter = is_fixed.HasValue ?
                new ObjectParameter("is_fixed", is_fixed) :
                new ObjectParameter("is_fixed", typeof(bool));
    
            var visit_eng_reportParameter = visit_eng_report != null ?
                new ObjectParameter("visit_eng_report", visit_eng_report) :
                new ObjectParameter("visit_eng_report", typeof(string));
    
            var maintenance_eng_reportParameter = maintenance_eng_report != null ?
                new ObjectParameter("maintenance_eng_report", maintenance_eng_report) :
                new ObjectParameter("maintenance_eng_report", typeof(string));
    
            var maintenance_reportParameter = maintenance_report != null ?
                new ObjectParameter("maintenance_report", maintenance_report) :
                new ObjectParameter("maintenance_report", typeof(string));
    
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("ManageMaintenance", checkParameter, maintenance_idParameter, cost_center_idParameter, cust_idParameter, cust_nameParameter, cust_phoneParameter, cust_mobileParameter, cust_cityParameter, cust_areaParameter, cust_addressParameter, cust_statusParameter, com_dateParameter, go_dateParameter, draw_dateParameter, delivery_dateParameter, maintenance_entry_dateParameter, item_categoryParameter, itemParameter, item_serialParameter, item_colorParameter, item_accessoriesParameter, damage_categoryParameter, damageParameter, customer_complaintParameter, maintenance_limitParameter, maintenance_indexationParameter, go_costParameter, draw_costParameter, delivery_costParameter, other_costParameter, treas_idParameter, treas_Expense_per_idParameter, expense_idParameter, treas_revenue_per_idParameter, revenue_idParameter, visit_eng_idParameter, maintenance_eng_idParameter, maintenance_eng_ratioParameter, workshop_idParameter, is_paidParameter, maintenance_statusParameter, is_fixedParameter, visit_eng_reportParameter, maintenance_eng_reportParameter, maintenance_reportParameter, user_nameParameter);
        }
    
        public virtual int ManageMaintenanceEngineers(string check, Nullable<int> eng_id, string eng_name, string address, Nullable<bool> is_active)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var eng_idParameter = eng_id.HasValue ?
                new ObjectParameter("eng_id", eng_id) :
                new ObjectParameter("eng_id", typeof(int));
    
            var eng_nameParameter = eng_name != null ?
                new ObjectParameter("eng_name", eng_name) :
                new ObjectParameter("eng_name", typeof(string));
    
            var addressParameter = address != null ?
                new ObjectParameter("address", address) :
                new ObjectParameter("address", typeof(string));
    
            var is_activeParameter = is_active.HasValue ?
                new ObjectParameter("is_active", is_active) :
                new ObjectParameter("is_active", typeof(bool));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("ManageMaintenanceEngineers", checkParameter, eng_idParameter, eng_nameParameter, addressParameter, is_activeParameter);
        }
    
        public virtual int ManageMaintenanceWorkShops(string check, Nullable<int> workshop_id, string workshop_name, string workshop_address, Nullable<bool> is_active)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var workshop_idParameter = workshop_id.HasValue ?
                new ObjectParameter("workshop_id", workshop_id) :
                new ObjectParameter("workshop_id", typeof(int));
    
            var workshop_nameParameter = workshop_name != null ?
                new ObjectParameter("workshop_name", workshop_name) :
                new ObjectParameter("workshop_name", typeof(string));
    
            var workshop_addressParameter = workshop_address != null ?
                new ObjectParameter("workshop_address", workshop_address) :
                new ObjectParameter("workshop_address", typeof(string));
    
            var is_activeParameter = is_active.HasValue ?
                new ObjectParameter("is_active", is_active) :
                new ObjectParameter("is_active", typeof(bool));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("ManageMaintenanceWorkShops", checkParameter, workshop_idParameter, workshop_nameParameter, workshop_addressParameter, is_activeParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> ManageManagement(string check, Nullable<int> management_id, string management_name, string notes, string user_name)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var management_idParameter = management_id.HasValue ?
                new ObjectParameter("management_id", management_id) :
                new ObjectParameter("management_id", typeof(int));
    
            var management_nameParameter = management_name != null ?
                new ObjectParameter("management_name", management_name) :
                new ObjectParameter("management_name", typeof(string));
    
            var notesParameter = notes != null ?
                new ObjectParameter("notes", notes) :
                new ObjectParameter("notes", typeof(string));
    
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("ManageManagement", checkParameter, management_idParameter, management_nameParameter, notesParameter, user_nameParameter);
        }
    
        public virtual ObjectResult<ManageManufactureOrderExpenses_Result> ManageManufactureOrderExpenses(string check, Nullable<int> manufacture_order_id, Nullable<int> manufacture_order_expense_id, Nullable<int> expense_id, Nullable<decimal> expense_value, string expense_enter_method)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var manufacture_order_idParameter = manufacture_order_id.HasValue ?
                new ObjectParameter("manufacture_order_id", manufacture_order_id) :
                new ObjectParameter("manufacture_order_id", typeof(int));
    
            var manufacture_order_expense_idParameter = manufacture_order_expense_id.HasValue ?
                new ObjectParameter("manufacture_order_expense_id", manufacture_order_expense_id) :
                new ObjectParameter("manufacture_order_expense_id", typeof(int));
    
            var expense_idParameter = expense_id.HasValue ?
                new ObjectParameter("expense_id", expense_id) :
                new ObjectParameter("expense_id", typeof(int));
    
            var expense_valueParameter = expense_value.HasValue ?
                new ObjectParameter("expense_value", expense_value) :
                new ObjectParameter("expense_value", typeof(decimal));
    
            var expense_enter_methodParameter = expense_enter_method != null ?
                new ObjectParameter("expense_enter_method", expense_enter_method) :
                new ObjectParameter("expense_enter_method", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<ManageManufactureOrderExpenses_Result>("ManageManufactureOrderExpenses", checkParameter, manufacture_order_idParameter, manufacture_order_expense_idParameter, expense_idParameter, expense_valueParameter, expense_enter_methodParameter);
        }
    
        public virtual int ManageManufactureOrderFinishedDetails(string check, Nullable<int> manufacture_order_id, Nullable<int> manufacture_order_finished_detail_id, Nullable<long> pro_id, string pro_serial, Nullable<System.DateTime> pro_expirey, Nullable<int> batch_no, Nullable<int> unit_id, Nullable<int> stock_id, Nullable<decimal> default_quantity, Nullable<decimal> damaged_quantity, Nullable<decimal> quantity, Nullable<decimal> price, Nullable<decimal> expenses, Nullable<decimal> external_manf_cost, Nullable<decimal> total_cost, Nullable<decimal> unit_cost, Nullable<decimal> run_hours, Nullable<decimal> hour_value)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var manufacture_order_idParameter = manufacture_order_id.HasValue ?
                new ObjectParameter("manufacture_order_id", manufacture_order_id) :
                new ObjectParameter("manufacture_order_id", typeof(int));
    
            var manufacture_order_finished_detail_idParameter = manufacture_order_finished_detail_id.HasValue ?
                new ObjectParameter("manufacture_order_finished_detail_id", manufacture_order_finished_detail_id) :
                new ObjectParameter("manufacture_order_finished_detail_id", typeof(int));
    
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            var pro_serialParameter = pro_serial != null ?
                new ObjectParameter("pro_serial", pro_serial) :
                new ObjectParameter("pro_serial", typeof(string));
    
            var pro_expireyParameter = pro_expirey.HasValue ?
                new ObjectParameter("pro_expirey", pro_expirey) :
                new ObjectParameter("pro_expirey", typeof(System.DateTime));
    
            var batch_noParameter = batch_no.HasValue ?
                new ObjectParameter("batch_no", batch_no) :
                new ObjectParameter("batch_no", typeof(int));
    
            var unit_idParameter = unit_id.HasValue ?
                new ObjectParameter("unit_id", unit_id) :
                new ObjectParameter("unit_id", typeof(int));
    
            var stock_idParameter = stock_id.HasValue ?
                new ObjectParameter("stock_id", stock_id) :
                new ObjectParameter("stock_id", typeof(int));
    
            var default_quantityParameter = default_quantity.HasValue ?
                new ObjectParameter("default_quantity", default_quantity) :
                new ObjectParameter("default_quantity", typeof(decimal));
    
            var damaged_quantityParameter = damaged_quantity.HasValue ?
                new ObjectParameter("damaged_quantity", damaged_quantity) :
                new ObjectParameter("damaged_quantity", typeof(decimal));
    
            var quantityParameter = quantity.HasValue ?
                new ObjectParameter("quantity", quantity) :
                new ObjectParameter("quantity", typeof(decimal));
    
            var priceParameter = price.HasValue ?
                new ObjectParameter("price", price) :
                new ObjectParameter("price", typeof(decimal));
    
            var expensesParameter = expenses.HasValue ?
                new ObjectParameter("expenses", expenses) :
                new ObjectParameter("expenses", typeof(decimal));
    
            var external_manf_costParameter = external_manf_cost.HasValue ?
                new ObjectParameter("external_manf_cost", external_manf_cost) :
                new ObjectParameter("external_manf_cost", typeof(decimal));
    
            var total_costParameter = total_cost.HasValue ?
                new ObjectParameter("total_cost", total_cost) :
                new ObjectParameter("total_cost", typeof(decimal));
    
            var unit_costParameter = unit_cost.HasValue ?
                new ObjectParameter("unit_cost", unit_cost) :
                new ObjectParameter("unit_cost", typeof(decimal));
    
            var run_hoursParameter = run_hours.HasValue ?
                new ObjectParameter("run_hours", run_hours) :
                new ObjectParameter("run_hours", typeof(decimal));
    
            var hour_valueParameter = hour_value.HasValue ?
                new ObjectParameter("hour_value", hour_value) :
                new ObjectParameter("hour_value", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("ManageManufactureOrderFinishedDetails", checkParameter, manufacture_order_idParameter, manufacture_order_finished_detail_idParameter, pro_idParameter, pro_serialParameter, pro_expireyParameter, batch_noParameter, unit_idParameter, stock_idParameter, default_quantityParameter, damaged_quantityParameter, quantityParameter, priceParameter, expensesParameter, external_manf_costParameter, total_costParameter, unit_costParameter, run_hoursParameter, hour_valueParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> ManageManufactureOrderHeader(string check, Nullable<int> manufacture_order_id, Nullable<int> manufacture_order_type_id, Nullable<int> cost_center_id, Nullable<int> vend_id, Nullable<System.DateTime> manufacture_date, string notes, string user_name)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var manufacture_order_idParameter = manufacture_order_id.HasValue ?
                new ObjectParameter("manufacture_order_id", manufacture_order_id) :
                new ObjectParameter("manufacture_order_id", typeof(int));
    
            var manufacture_order_type_idParameter = manufacture_order_type_id.HasValue ?
                new ObjectParameter("manufacture_order_type_id", manufacture_order_type_id) :
                new ObjectParameter("manufacture_order_type_id", typeof(int));
    
            var cost_center_idParameter = cost_center_id.HasValue ?
                new ObjectParameter("cost_center_id", cost_center_id) :
                new ObjectParameter("cost_center_id", typeof(int));
    
            var vend_idParameter = vend_id.HasValue ?
                new ObjectParameter("vend_id", vend_id) :
                new ObjectParameter("vend_id", typeof(int));
    
            var manufacture_dateParameter = manufacture_date.HasValue ?
                new ObjectParameter("manufacture_date", manufacture_date) :
                new ObjectParameter("manufacture_date", typeof(System.DateTime));
    
            var notesParameter = notes != null ?
                new ObjectParameter("notes", notes) :
                new ObjectParameter("notes", typeof(string));
    
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("ManageManufactureOrderHeader", checkParameter, manufacture_order_idParameter, manufacture_order_type_idParameter, cost_center_idParameter, vend_idParameter, manufacture_dateParameter, notesParameter, user_nameParameter);
        }
    
        public virtual int ManageManufactureOrderRawMaerialsDetails(string check, Nullable<int> manufacture_order_id, Nullable<int> manufacture_order_raw_detail_id, Nullable<long> pro_id, Nullable<int> unit_id, Nullable<int> stock_id, Nullable<decimal> component_quantity, Nullable<decimal> quantity, Nullable<decimal> price, Nullable<long> finished_pro_id)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var manufacture_order_idParameter = manufacture_order_id.HasValue ?
                new ObjectParameter("manufacture_order_id", manufacture_order_id) :
                new ObjectParameter("manufacture_order_id", typeof(int));
    
            var manufacture_order_raw_detail_idParameter = manufacture_order_raw_detail_id.HasValue ?
                new ObjectParameter("manufacture_order_raw_detail_id", manufacture_order_raw_detail_id) :
                new ObjectParameter("manufacture_order_raw_detail_id", typeof(int));
    
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            var unit_idParameter = unit_id.HasValue ?
                new ObjectParameter("unit_id", unit_id) :
                new ObjectParameter("unit_id", typeof(int));
    
            var stock_idParameter = stock_id.HasValue ?
                new ObjectParameter("stock_id", stock_id) :
                new ObjectParameter("stock_id", typeof(int));
    
            var component_quantityParameter = component_quantity.HasValue ?
                new ObjectParameter("component_quantity", component_quantity) :
                new ObjectParameter("component_quantity", typeof(decimal));
    
            var quantityParameter = quantity.HasValue ?
                new ObjectParameter("quantity", quantity) :
                new ObjectParameter("quantity", typeof(decimal));
    
            var priceParameter = price.HasValue ?
                new ObjectParameter("price", price) :
                new ObjectParameter("price", typeof(decimal));
    
            var finished_pro_idParameter = finished_pro_id.HasValue ?
                new ObjectParameter("finished_pro_id", finished_pro_id) :
                new ObjectParameter("finished_pro_id", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("ManageManufactureOrderRawMaerialsDetails", checkParameter, manufacture_order_idParameter, manufacture_order_raw_detail_idParameter, pro_idParameter, unit_idParameter, stock_idParameter, component_quantityParameter, quantityParameter, priceParameter, finished_pro_idParameter);
        }
    
        public virtual int ManagePapers(string check, Nullable<int> paper_id, string paper_no, string paper_name, Nullable<int> cust_id, Nullable<int> paper_type, Nullable<int> bank_id, Nullable<int> branch_id, Nullable<System.DateTime> edit_date, Nullable<System.DateTime> due_date, Nullable<int> paper_status, byte[] paper_image, Nullable<decimal> value, string notes, string user_name)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var paper_idParameter = paper_id.HasValue ?
                new ObjectParameter("paper_id", paper_id) :
                new ObjectParameter("paper_id", typeof(int));
    
            var paper_noParameter = paper_no != null ?
                new ObjectParameter("paper_no", paper_no) :
                new ObjectParameter("paper_no", typeof(string));
    
            var paper_nameParameter = paper_name != null ?
                new ObjectParameter("paper_name", paper_name) :
                new ObjectParameter("paper_name", typeof(string));
    
            var cust_idParameter = cust_id.HasValue ?
                new ObjectParameter("cust_id", cust_id) :
                new ObjectParameter("cust_id", typeof(int));
    
            var paper_typeParameter = paper_type.HasValue ?
                new ObjectParameter("paper_type", paper_type) :
                new ObjectParameter("paper_type", typeof(int));
    
            var bank_idParameter = bank_id.HasValue ?
                new ObjectParameter("bank_id", bank_id) :
                new ObjectParameter("bank_id", typeof(int));
    
            var branch_idParameter = branch_id.HasValue ?
                new ObjectParameter("branch_id", branch_id) :
                new ObjectParameter("branch_id", typeof(int));
    
            var edit_dateParameter = edit_date.HasValue ?
                new ObjectParameter("edit_date", edit_date) :
                new ObjectParameter("edit_date", typeof(System.DateTime));
    
            var due_dateParameter = due_date.HasValue ?
                new ObjectParameter("due_date", due_date) :
                new ObjectParameter("due_date", typeof(System.DateTime));
    
            var paper_statusParameter = paper_status.HasValue ?
                new ObjectParameter("paper_status", paper_status) :
                new ObjectParameter("paper_status", typeof(int));
    
            var paper_imageParameter = paper_image != null ?
                new ObjectParameter("paper_image", paper_image) :
                new ObjectParameter("paper_image", typeof(byte[]));
    
            var valueParameter = value.HasValue ?
                new ObjectParameter("value", value) :
                new ObjectParameter("value", typeof(decimal));
    
            var notesParameter = notes != null ?
                new ObjectParameter("notes", notes) :
                new ObjectParameter("notes", typeof(string));
    
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("ManagePapers", checkParameter, paper_idParameter, paper_noParameter, paper_nameParameter, cust_idParameter, paper_typeParameter, bank_idParameter, branch_idParameter, edit_dateParameter, due_dateParameter, paper_statusParameter, paper_imageParameter, valueParameter, notesParameter, user_nameParameter);
        }
    
        public virtual int manageproduct(string check, Nullable<long> pro_id, string pro_id2, Nullable<int> pro_kind, string pro_name, Nullable<int> cat_id, byte[] img, string manfacturing_country, string store_place, Nullable<decimal> vat, Nullable<decimal> request_limit, Nullable<int> default_stock, Nullable<int> unit_id, Nullable<int> unit_kind, Nullable<decimal> unit_count, Nullable<decimal> pur_price, Nullable<decimal> wholesale_Price, Nullable<decimal> half_wholesale_price, Nullable<decimal> retail_price, Nullable<decimal> lowest_price, Nullable<decimal> highest_price, string discount, Nullable<bool> default_purchase_unit, Nullable<bool> default_sale_unit, string international_barcode, string system_barcode, Nullable<bool> is_active, string notes, string user_name)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            var pro_id2Parameter = pro_id2 != null ?
                new ObjectParameter("pro_id2", pro_id2) :
                new ObjectParameter("pro_id2", typeof(string));
    
            var pro_kindParameter = pro_kind.HasValue ?
                new ObjectParameter("pro_kind", pro_kind) :
                new ObjectParameter("pro_kind", typeof(int));
    
            var pro_nameParameter = pro_name != null ?
                new ObjectParameter("pro_name", pro_name) :
                new ObjectParameter("pro_name", typeof(string));
    
            var cat_idParameter = cat_id.HasValue ?
                new ObjectParameter("cat_id", cat_id) :
                new ObjectParameter("cat_id", typeof(int));
    
            var imgParameter = img != null ?
                new ObjectParameter("img", img) :
                new ObjectParameter("img", typeof(byte[]));
    
            var manfacturing_countryParameter = manfacturing_country != null ?
                new ObjectParameter("manfacturing_country", manfacturing_country) :
                new ObjectParameter("manfacturing_country", typeof(string));
    
            var store_placeParameter = store_place != null ?
                new ObjectParameter("store_place", store_place) :
                new ObjectParameter("store_place", typeof(string));
    
            var vatParameter = vat.HasValue ?
                new ObjectParameter("vat", vat) :
                new ObjectParameter("vat", typeof(decimal));
    
            var request_limitParameter = request_limit.HasValue ?
                new ObjectParameter("request_limit", request_limit) :
                new ObjectParameter("request_limit", typeof(decimal));
    
            var default_stockParameter = default_stock.HasValue ?
                new ObjectParameter("default_stock", default_stock) :
                new ObjectParameter("default_stock", typeof(int));
    
            var unit_idParameter = unit_id.HasValue ?
                new ObjectParameter("unit_id", unit_id) :
                new ObjectParameter("unit_id", typeof(int));
    
            var unit_kindParameter = unit_kind.HasValue ?
                new ObjectParameter("unit_kind", unit_kind) :
                new ObjectParameter("unit_kind", typeof(int));
    
            var unit_countParameter = unit_count.HasValue ?
                new ObjectParameter("unit_count", unit_count) :
                new ObjectParameter("unit_count", typeof(decimal));
    
            var pur_priceParameter = pur_price.HasValue ?
                new ObjectParameter("pur_price", pur_price) :
                new ObjectParameter("pur_price", typeof(decimal));
    
            var wholesale_PriceParameter = wholesale_Price.HasValue ?
                new ObjectParameter("wholesale_Price", wholesale_Price) :
                new ObjectParameter("wholesale_Price", typeof(decimal));
    
            var half_wholesale_priceParameter = half_wholesale_price.HasValue ?
                new ObjectParameter("half_wholesale_price", half_wholesale_price) :
                new ObjectParameter("half_wholesale_price", typeof(decimal));
    
            var retail_priceParameter = retail_price.HasValue ?
                new ObjectParameter("retail_price", retail_price) :
                new ObjectParameter("retail_price", typeof(decimal));
    
            var lowest_priceParameter = lowest_price.HasValue ?
                new ObjectParameter("lowest_price", lowest_price) :
                new ObjectParameter("lowest_price", typeof(decimal));
    
            var highest_priceParameter = highest_price.HasValue ?
                new ObjectParameter("highest_price", highest_price) :
                new ObjectParameter("highest_price", typeof(decimal));
    
            var discountParameter = discount != null ?
                new ObjectParameter("discount", discount) :
                new ObjectParameter("discount", typeof(string));
    
            var default_purchase_unitParameter = default_purchase_unit.HasValue ?
                new ObjectParameter("default_purchase_unit", default_purchase_unit) :
                new ObjectParameter("default_purchase_unit", typeof(bool));
    
            var default_sale_unitParameter = default_sale_unit.HasValue ?
                new ObjectParameter("default_sale_unit", default_sale_unit) :
                new ObjectParameter("default_sale_unit", typeof(bool));
    
            var international_barcodeParameter = international_barcode != null ?
                new ObjectParameter("international_barcode", international_barcode) :
                new ObjectParameter("international_barcode", typeof(string));
    
            var system_barcodeParameter = system_barcode != null ?
                new ObjectParameter("system_barcode", system_barcode) :
                new ObjectParameter("system_barcode", typeof(string));
    
            var is_activeParameter = is_active.HasValue ?
                new ObjectParameter("is_active", is_active) :
                new ObjectParameter("is_active", typeof(bool));
    
            var notesParameter = notes != null ?
                new ObjectParameter("notes", notes) :
                new ObjectParameter("notes", typeof(string));
    
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("manageproduct", checkParameter, pro_idParameter, pro_id2Parameter, pro_kindParameter, pro_nameParameter, cat_idParameter, imgParameter, manfacturing_countryParameter, store_placeParameter, vatParameter, request_limitParameter, default_stockParameter, unit_idParameter, unit_kindParameter, unit_countParameter, pur_priceParameter, wholesale_PriceParameter, half_wholesale_priceParameter, retail_priceParameter, lowest_priceParameter, highest_priceParameter, discountParameter, default_purchase_unitParameter, default_sale_unitParameter, international_barcodeParameter, system_barcodeParameter, is_activeParameter, notesParameter, user_nameParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> manageproductinitialbalance(string check, Nullable<int> init_bal_id, Nullable<int> stock_id, string notes, Nullable<int> pro_id, string pro_serial, Nullable<System.DateTime> pro_expirey, Nullable<int> unit_id, Nullable<decimal> quantity, Nullable<decimal> price)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var init_bal_idParameter = init_bal_id.HasValue ?
                new ObjectParameter("init_bal_id", init_bal_id) :
                new ObjectParameter("init_bal_id", typeof(int));
    
            var stock_idParameter = stock_id.HasValue ?
                new ObjectParameter("stock_id", stock_id) :
                new ObjectParameter("stock_id", typeof(int));
    
            var notesParameter = notes != null ?
                new ObjectParameter("notes", notes) :
                new ObjectParameter("notes", typeof(string));
    
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(int));
    
            var pro_serialParameter = pro_serial != null ?
                new ObjectParameter("pro_serial", pro_serial) :
                new ObjectParameter("pro_serial", typeof(string));
    
            var pro_expireyParameter = pro_expirey.HasValue ?
                new ObjectParameter("pro_expirey", pro_expirey) :
                new ObjectParameter("pro_expirey", typeof(System.DateTime));
    
            var unit_idParameter = unit_id.HasValue ?
                new ObjectParameter("unit_id", unit_id) :
                new ObjectParameter("unit_id", typeof(int));
    
            var quantityParameter = quantity.HasValue ?
                new ObjectParameter("quantity", quantity) :
                new ObjectParameter("quantity", typeof(decimal));
    
            var priceParameter = price.HasValue ?
                new ObjectParameter("price", price) :
                new ObjectParameter("price", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("manageproductinitialbalance", checkParameter, init_bal_idParameter, stock_idParameter, notesParameter, pro_idParameter, pro_serialParameter, pro_expireyParameter, unit_idParameter, quantityParameter, priceParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> ManageProductPartitioningHeader(string check, Nullable<int> pro_partitioning_id, string pro_partitioning_no, Nullable<int> cost_center_id, Nullable<int> stock_export_id, Nullable<int> stock_import_id, Nullable<System.DateTime> partitioning_date, Nullable<System.DateTime> date_from, Nullable<System.DateTime> date_to, string notes)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var pro_partitioning_idParameter = pro_partitioning_id.HasValue ?
                new ObjectParameter("pro_partitioning_id", pro_partitioning_id) :
                new ObjectParameter("pro_partitioning_id", typeof(int));
    
            var pro_partitioning_noParameter = pro_partitioning_no != null ?
                new ObjectParameter("pro_partitioning_no", pro_partitioning_no) :
                new ObjectParameter("pro_partitioning_no", typeof(string));
    
            var cost_center_idParameter = cost_center_id.HasValue ?
                new ObjectParameter("cost_center_id", cost_center_id) :
                new ObjectParameter("cost_center_id", typeof(int));
    
            var stock_export_idParameter = stock_export_id.HasValue ?
                new ObjectParameter("stock_export_id", stock_export_id) :
                new ObjectParameter("stock_export_id", typeof(int));
    
            var stock_import_idParameter = stock_import_id.HasValue ?
                new ObjectParameter("stock_import_id", stock_import_id) :
                new ObjectParameter("stock_import_id", typeof(int));
    
            var partitioning_dateParameter = partitioning_date.HasValue ?
                new ObjectParameter("partitioning_date", partitioning_date) :
                new ObjectParameter("partitioning_date", typeof(System.DateTime));
    
            var date_fromParameter = date_from.HasValue ?
                new ObjectParameter("date_from", date_from) :
                new ObjectParameter("date_from", typeof(System.DateTime));
    
            var date_toParameter = date_to.HasValue ?
                new ObjectParameter("date_to", date_to) :
                new ObjectParameter("date_to", typeof(System.DateTime));
    
            var notesParameter = notes != null ?
                new ObjectParameter("notes", notes) :
                new ObjectParameter("notes", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("ManageProductPartitioningHeader", checkParameter, pro_partitioning_idParameter, pro_partitioning_noParameter, cost_center_idParameter, stock_export_idParameter, stock_import_idParameter, partitioning_dateParameter, date_fromParameter, date_toParameter, notesParameter);
        }
    
        public virtual int ManageProductPartitioningInputs(string check, Nullable<int> pro_partitioning_id, Nullable<int> pro_partitioning_input_id, Nullable<long> pro_id, string pro_batch_no, string pro_serial, Nullable<System.DateTime> pro_expirey, Nullable<int> unit_id, Nullable<decimal> quantity, Nullable<decimal> price)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var pro_partitioning_idParameter = pro_partitioning_id.HasValue ?
                new ObjectParameter("pro_partitioning_id", pro_partitioning_id) :
                new ObjectParameter("pro_partitioning_id", typeof(int));
    
            var pro_partitioning_input_idParameter = pro_partitioning_input_id.HasValue ?
                new ObjectParameter("pro_partitioning_input_id", pro_partitioning_input_id) :
                new ObjectParameter("pro_partitioning_input_id", typeof(int));
    
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            var pro_batch_noParameter = pro_batch_no != null ?
                new ObjectParameter("pro_batch_no", pro_batch_no) :
                new ObjectParameter("pro_batch_no", typeof(string));
    
            var pro_serialParameter = pro_serial != null ?
                new ObjectParameter("pro_serial", pro_serial) :
                new ObjectParameter("pro_serial", typeof(string));
    
            var pro_expireyParameter = pro_expirey.HasValue ?
                new ObjectParameter("pro_expirey", pro_expirey) :
                new ObjectParameter("pro_expirey", typeof(System.DateTime));
    
            var unit_idParameter = unit_id.HasValue ?
                new ObjectParameter("unit_id", unit_id) :
                new ObjectParameter("unit_id", typeof(int));
    
            var quantityParameter = quantity.HasValue ?
                new ObjectParameter("quantity", quantity) :
                new ObjectParameter("quantity", typeof(decimal));
    
            var priceParameter = price.HasValue ?
                new ObjectParameter("price", price) :
                new ObjectParameter("price", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("ManageProductPartitioningInputs", checkParameter, pro_partitioning_idParameter, pro_partitioning_input_idParameter, pro_idParameter, pro_batch_noParameter, pro_serialParameter, pro_expireyParameter, unit_idParameter, quantityParameter, priceParameter);
        }
    
        public virtual int ManageProductPartitioningOutputs(string check, Nullable<int> pro_partitioning_id, Nullable<int> pro_partitioning_output_id, Nullable<long> pro_id, string pro_batch_no, string pro_serial, Nullable<System.DateTime> pro_expirey, Nullable<int> unit_id, Nullable<decimal> quantity, Nullable<decimal> price)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var pro_partitioning_idParameter = pro_partitioning_id.HasValue ?
                new ObjectParameter("pro_partitioning_id", pro_partitioning_id) :
                new ObjectParameter("pro_partitioning_id", typeof(int));
    
            var pro_partitioning_output_idParameter = pro_partitioning_output_id.HasValue ?
                new ObjectParameter("pro_partitioning_output_id", pro_partitioning_output_id) :
                new ObjectParameter("pro_partitioning_output_id", typeof(int));
    
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            var pro_batch_noParameter = pro_batch_no != null ?
                new ObjectParameter("pro_batch_no", pro_batch_no) :
                new ObjectParameter("pro_batch_no", typeof(string));
    
            var pro_serialParameter = pro_serial != null ?
                new ObjectParameter("pro_serial", pro_serial) :
                new ObjectParameter("pro_serial", typeof(string));
    
            var pro_expireyParameter = pro_expirey.HasValue ?
                new ObjectParameter("pro_expirey", pro_expirey) :
                new ObjectParameter("pro_expirey", typeof(System.DateTime));
    
            var unit_idParameter = unit_id.HasValue ?
                new ObjectParameter("unit_id", unit_id) :
                new ObjectParameter("unit_id", typeof(int));
    
            var quantityParameter = quantity.HasValue ?
                new ObjectParameter("quantity", quantity) :
                new ObjectParameter("quantity", typeof(decimal));
    
            var priceParameter = price.HasValue ?
                new ObjectParameter("price", price) :
                new ObjectParameter("price", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("ManageProductPartitioningOutputs", checkParameter, pro_partitioning_idParameter, pro_partitioning_output_idParameter, pro_idParameter, pro_batch_noParameter, pro_serialParameter, pro_expireyParameter, unit_idParameter, quantityParameter, priceParameter);
        }
    
        public virtual int ManageProductsCollected(string check, Nullable<int> pro_raw_det_id, Nullable<long> pro_id, Nullable<long> pro_raw_id, Nullable<int> unit_id, Nullable<decimal> quantity, Nullable<decimal> price)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var pro_raw_det_idParameter = pro_raw_det_id.HasValue ?
                new ObjectParameter("pro_raw_det_id", pro_raw_det_id) :
                new ObjectParameter("pro_raw_det_id", typeof(int));
    
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            var pro_raw_idParameter = pro_raw_id.HasValue ?
                new ObjectParameter("pro_raw_id", pro_raw_id) :
                new ObjectParameter("pro_raw_id", typeof(long));
    
            var unit_idParameter = unit_id.HasValue ?
                new ObjectParameter("unit_id", unit_id) :
                new ObjectParameter("unit_id", typeof(int));
    
            var quantityParameter = quantity.HasValue ?
                new ObjectParameter("quantity", quantity) :
                new ObjectParameter("quantity", typeof(decimal));
    
            var priceParameter = price.HasValue ?
                new ObjectParameter("price", price) :
                new ObjectParameter("price", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("ManageProductsCollected", checkParameter, pro_raw_det_idParameter, pro_idParameter, pro_raw_idParameter, unit_idParameter, quantityParameter, priceParameter);
        }
    
        public virtual int ManageProductUnit(string check, Nullable<long> pro_id, Nullable<int> unit_id, Nullable<int> unit_kind, Nullable<decimal> unit_count, Nullable<decimal> wholesale_price, Nullable<decimal> pur_price, Nullable<decimal> retail_price)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            var unit_idParameter = unit_id.HasValue ?
                new ObjectParameter("unit_id", unit_id) :
                new ObjectParameter("unit_id", typeof(int));
    
            var unit_kindParameter = unit_kind.HasValue ?
                new ObjectParameter("unit_kind", unit_kind) :
                new ObjectParameter("unit_kind", typeof(int));
    
            var unit_countParameter = unit_count.HasValue ?
                new ObjectParameter("unit_count", unit_count) :
                new ObjectParameter("unit_count", typeof(decimal));
    
            var wholesale_priceParameter = wholesale_price.HasValue ?
                new ObjectParameter("wholesale_price", wholesale_price) :
                new ObjectParameter("wholesale_price", typeof(decimal));
    
            var pur_priceParameter = pur_price.HasValue ?
                new ObjectParameter("pur_price", pur_price) :
                new ObjectParameter("pur_price", typeof(decimal));
    
            var retail_priceParameter = retail_price.HasValue ?
                new ObjectParameter("retail_price", retail_price) :
                new ObjectParameter("retail_price", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("ManageProductUnit", checkParameter, pro_idParameter, unit_idParameter, unit_kindParameter, unit_countParameter, wholesale_priceParameter, pur_priceParameter, retail_priceParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> ManageRevenuesItems(string check, Nullable<int> revenue_id, Nullable<int> parent_id, string revenue_name, string notes, string user_name)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var revenue_idParameter = revenue_id.HasValue ?
                new ObjectParameter("revenue_id", revenue_id) :
                new ObjectParameter("revenue_id", typeof(int));
    
            var parent_idParameter = parent_id.HasValue ?
                new ObjectParameter("parent_id", parent_id) :
                new ObjectParameter("parent_id", typeof(int));
    
            var revenue_nameParameter = revenue_name != null ?
                new ObjectParameter("revenue_name", revenue_name) :
                new ObjectParameter("revenue_name", typeof(string));
    
            var notesParameter = notes != null ?
                new ObjectParameter("notes", notes) :
                new ObjectParameter("notes", typeof(string));
    
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("ManageRevenuesItems", checkParameter, revenue_idParameter, parent_idParameter, revenue_nameParameter, notesParameter, user_nameParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> ManageSalaryItems(string check, Nullable<int> salary_item_id, string salary_item_name, Nullable<int> salary_item_type_id, Nullable<decimal> salary_item_value, Nullable<bool> is_activity, string notes, string user_name)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var salary_item_idParameter = salary_item_id.HasValue ?
                new ObjectParameter("salary_item_id", salary_item_id) :
                new ObjectParameter("salary_item_id", typeof(int));
    
            var salary_item_nameParameter = salary_item_name != null ?
                new ObjectParameter("salary_item_name", salary_item_name) :
                new ObjectParameter("salary_item_name", typeof(string));
    
            var salary_item_type_idParameter = salary_item_type_id.HasValue ?
                new ObjectParameter("salary_item_type_id", salary_item_type_id) :
                new ObjectParameter("salary_item_type_id", typeof(int));
    
            var salary_item_valueParameter = salary_item_value.HasValue ?
                new ObjectParameter("salary_item_value", salary_item_value) :
                new ObjectParameter("salary_item_value", typeof(decimal));
    
            var is_activityParameter = is_activity.HasValue ?
                new ObjectParameter("is_activity", is_activity) :
                new ObjectParameter("is_activity", typeof(bool));
    
            var notesParameter = notes != null ?
                new ObjectParameter("notes", notes) :
                new ObjectParameter("notes", typeof(string));
    
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("ManageSalaryItems", checkParameter, salary_item_idParameter, salary_item_nameParameter, salary_item_type_idParameter, salary_item_valueParameter, is_activityParameter, notesParameter, user_nameParameter);
        }
    
        public virtual int ManageSalePriceChangeAlert(string check, Nullable<int> tran_id, Nullable<int> inv_id, Nullable<long> pro_id, Nullable<decimal> pro_price, Nullable<decimal> sell_price, string user_name, Nullable<bool> seen)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var tran_idParameter = tran_id.HasValue ?
                new ObjectParameter("tran_id", tran_id) :
                new ObjectParameter("tran_id", typeof(int));
    
            var inv_idParameter = inv_id.HasValue ?
                new ObjectParameter("inv_id", inv_id) :
                new ObjectParameter("inv_id", typeof(int));
    
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            var pro_priceParameter = pro_price.HasValue ?
                new ObjectParameter("pro_price", pro_price) :
                new ObjectParameter("pro_price", typeof(decimal));
    
            var sell_priceParameter = sell_price.HasValue ?
                new ObjectParameter("sell_price", sell_price) :
                new ObjectParameter("sell_price", typeof(decimal));
    
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            var seenParameter = seen.HasValue ?
                new ObjectParameter("seen", seen) :
                new ObjectParameter("seen", typeof(bool));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("ManageSalePriceChangeAlert", checkParameter, tran_idParameter, inv_idParameter, pro_idParameter, pro_priceParameter, sell_priceParameter, user_nameParameter, seenParameter);
        }
    
        public virtual int managesalesrepresentative(string check, Nullable<int> sales_rep_id, string sales_rep_name, Nullable<long> national_id, string addess, string mobile, string phone, string notes, string user_name)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var sales_rep_idParameter = sales_rep_id.HasValue ?
                new ObjectParameter("sales_rep_id", sales_rep_id) :
                new ObjectParameter("sales_rep_id", typeof(int));
    
            var sales_rep_nameParameter = sales_rep_name != null ?
                new ObjectParameter("sales_rep_name", sales_rep_name) :
                new ObjectParameter("sales_rep_name", typeof(string));
    
            var national_idParameter = national_id.HasValue ?
                new ObjectParameter("national_id", national_id) :
                new ObjectParameter("national_id", typeof(long));
    
            var addessParameter = addess != null ?
                new ObjectParameter("addess", addess) :
                new ObjectParameter("addess", typeof(string));
    
            var mobileParameter = mobile != null ?
                new ObjectParameter("mobile", mobile) :
                new ObjectParameter("mobile", typeof(string));
    
            var phoneParameter = phone != null ?
                new ObjectParameter("phone", phone) :
                new ObjectParameter("phone", typeof(string));
    
            var notesParameter = notes != null ?
                new ObjectParameter("notes", notes) :
                new ObjectParameter("notes", typeof(string));
    
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("managesalesrepresentative", checkParameter, sales_rep_idParameter, sales_rep_nameParameter, national_idParameter, addessParameter, mobileParameter, phoneParameter, notesParameter, user_nameParameter);
        }
    
        public virtual int managestock(string check, Nullable<int> stock_id, Nullable<int> cost_center_id, string stock_name, string stock_emp, Nullable<bool> active, Nullable<bool> default_stock, string location, string user_name)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var stock_idParameter = stock_id.HasValue ?
                new ObjectParameter("stock_id", stock_id) :
                new ObjectParameter("stock_id", typeof(int));
    
            var cost_center_idParameter = cost_center_id.HasValue ?
                new ObjectParameter("cost_center_id", cost_center_id) :
                new ObjectParameter("cost_center_id", typeof(int));
    
            var stock_nameParameter = stock_name != null ?
                new ObjectParameter("stock_name", stock_name) :
                new ObjectParameter("stock_name", typeof(string));
    
            var stock_empParameter = stock_emp != null ?
                new ObjectParameter("stock_emp", stock_emp) :
                new ObjectParameter("stock_emp", typeof(string));
    
            var activeParameter = active.HasValue ?
                new ObjectParameter("active", active) :
                new ObjectParameter("active", typeof(bool));
    
            var default_stockParameter = default_stock.HasValue ?
                new ObjectParameter("default_stock", default_stock) :
                new ObjectParameter("default_stock", typeof(bool));
    
            var locationParameter = location != null ?
                new ObjectParameter("location", location) :
                new ObjectParameter("location", typeof(string));
    
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("managestock", checkParameter, stock_idParameter, cost_center_idParameter, stock_nameParameter, stock_empParameter, activeParameter, default_stockParameter, locationParameter, user_nameParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> ManageTable(string check, Nullable<int> hall_table_id, Nullable<int> hall_id, string table_name, string notes)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var hall_table_idParameter = hall_table_id.HasValue ?
                new ObjectParameter("hall_table_id", hall_table_id) :
                new ObjectParameter("hall_table_id", typeof(int));
    
            var hall_idParameter = hall_id.HasValue ?
                new ObjectParameter("hall_id", hall_id) :
                new ObjectParameter("hall_id", typeof(int));
    
            var table_nameParameter = table_name != null ?
                new ObjectParameter("table_name", table_name) :
                new ObjectParameter("table_name", typeof(string));
    
            var notesParameter = notes != null ?
                new ObjectParameter("notes", notes) :
                new ObjectParameter("notes", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("ManageTable", checkParameter, hall_table_idParameter, hall_idParameter, table_nameParameter, notesParameter);
        }
    
        public virtual int managetreasury(string check, Nullable<int> treasury_id, Nullable<int> cost_center_id, string treasury_name, Nullable<System.DateTime> adding_date, Nullable<decimal> intial_balance, string location, string user_name)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var treasury_idParameter = treasury_id.HasValue ?
                new ObjectParameter("treasury_id", treasury_id) :
                new ObjectParameter("treasury_id", typeof(int));
    
            var cost_center_idParameter = cost_center_id.HasValue ?
                new ObjectParameter("cost_center_id", cost_center_id) :
                new ObjectParameter("cost_center_id", typeof(int));
    
            var treasury_nameParameter = treasury_name != null ?
                new ObjectParameter("treasury_name", treasury_name) :
                new ObjectParameter("treasury_name", typeof(string));
    
            var adding_dateParameter = adding_date.HasValue ?
                new ObjectParameter("adding_date", adding_date) :
                new ObjectParameter("adding_date", typeof(System.DateTime));
    
            var intial_balanceParameter = intial_balance.HasValue ?
                new ObjectParameter("intial_balance", intial_balance) :
                new ObjectParameter("intial_balance", typeof(decimal));
    
            var locationParameter = location != null ?
                new ObjectParameter("location", location) :
                new ObjectParameter("location", typeof(string));
    
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("managetreasury", checkParameter, treasury_idParameter, cost_center_idParameter, treasury_nameParameter, adding_dateParameter, intial_balanceParameter, locationParameter, user_nameParameter);
        }
    
        public virtual int ManageTreasuryPermission(string check, Nullable<int> per_id, Nullable<int> per_no, Nullable<int> per_kind, Nullable<System.DateTime> per_date, Nullable<int> cust_id, Nullable<int> expense_id, Nullable<bool> manufacture_expense, Nullable<int> revenue_id, Nullable<int> cost_center_id, Nullable<bool> isdiscount, Nullable<int> treas_id, Nullable<int> treas_from, Nullable<int> treas_to, Nullable<decimal> value, string notes, string user_name)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var per_idParameter = per_id.HasValue ?
                new ObjectParameter("per_id", per_id) :
                new ObjectParameter("per_id", typeof(int));
    
            var per_noParameter = per_no.HasValue ?
                new ObjectParameter("per_no", per_no) :
                new ObjectParameter("per_no", typeof(int));
    
            var per_kindParameter = per_kind.HasValue ?
                new ObjectParameter("per_kind", per_kind) :
                new ObjectParameter("per_kind", typeof(int));
    
            var per_dateParameter = per_date.HasValue ?
                new ObjectParameter("per_date", per_date) :
                new ObjectParameter("per_date", typeof(System.DateTime));
    
            var cust_idParameter = cust_id.HasValue ?
                new ObjectParameter("cust_id", cust_id) :
                new ObjectParameter("cust_id", typeof(int));
    
            var expense_idParameter = expense_id.HasValue ?
                new ObjectParameter("expense_id", expense_id) :
                new ObjectParameter("expense_id", typeof(int));
    
            var manufacture_expenseParameter = manufacture_expense.HasValue ?
                new ObjectParameter("manufacture_expense", manufacture_expense) :
                new ObjectParameter("manufacture_expense", typeof(bool));
    
            var revenue_idParameter = revenue_id.HasValue ?
                new ObjectParameter("revenue_id", revenue_id) :
                new ObjectParameter("revenue_id", typeof(int));
    
            var cost_center_idParameter = cost_center_id.HasValue ?
                new ObjectParameter("cost_center_id", cost_center_id) :
                new ObjectParameter("cost_center_id", typeof(int));
    
            var isdiscountParameter = isdiscount.HasValue ?
                new ObjectParameter("isdiscount", isdiscount) :
                new ObjectParameter("isdiscount", typeof(bool));
    
            var treas_idParameter = treas_id.HasValue ?
                new ObjectParameter("treas_id", treas_id) :
                new ObjectParameter("treas_id", typeof(int));
    
            var treas_fromParameter = treas_from.HasValue ?
                new ObjectParameter("treas_from", treas_from) :
                new ObjectParameter("treas_from", typeof(int));
    
            var treas_toParameter = treas_to.HasValue ?
                new ObjectParameter("treas_to", treas_to) :
                new ObjectParameter("treas_to", typeof(int));
    
            var valueParameter = value.HasValue ?
                new ObjectParameter("value", value) :
                new ObjectParameter("value", typeof(decimal));
    
            var notesParameter = notes != null ?
                new ObjectParameter("notes", notes) :
                new ObjectParameter("notes", typeof(string));
    
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("ManageTreasuryPermission", checkParameter, per_idParameter, per_noParameter, per_kindParameter, per_dateParameter, cust_idParameter, expense_idParameter, manufacture_expenseParameter, revenue_idParameter, cost_center_idParameter, isdiscountParameter, treas_idParameter, treas_fromParameter, treas_toParameter, valueParameter, notesParameter, user_nameParameter);
        }
    
        public virtual int ManageUnit(string check, Nullable<int> unit_id, string unit_name, string user_name)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var unit_idParameter = unit_id.HasValue ?
                new ObjectParameter("unit_id", unit_id) :
                new ObjectParameter("unit_id", typeof(int));
    
            var unit_nameParameter = unit_name != null ?
                new ObjectParameter("unit_name", unit_name) :
                new ObjectParameter("unit_name", typeof(string));
    
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("ManageUnit", checkParameter, unit_idParameter, unit_nameParameter, user_nameParameter);
        }
    
        public virtual int ManageUser(string check, string name, string password, string alter_password, string delete_password, Nullable<bool> isactive, string job, Nullable<int> cost_center_id, Nullable<int> treasury_id, string cashier_invoice_name)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var nameParameter = name != null ?
                new ObjectParameter("name", name) :
                new ObjectParameter("name", typeof(string));
    
            var passwordParameter = password != null ?
                new ObjectParameter("password", password) :
                new ObjectParameter("password", typeof(string));
    
            var alter_passwordParameter = alter_password != null ?
                new ObjectParameter("alter_password", alter_password) :
                new ObjectParameter("alter_password", typeof(string));
    
            var delete_passwordParameter = delete_password != null ?
                new ObjectParameter("delete_password", delete_password) :
                new ObjectParameter("delete_password", typeof(string));
    
            var isactiveParameter = isactive.HasValue ?
                new ObjectParameter("isactive", isactive) :
                new ObjectParameter("isactive", typeof(bool));
    
            var jobParameter = job != null ?
                new ObjectParameter("job", job) :
                new ObjectParameter("job", typeof(string));
    
            var cost_center_idParameter = cost_center_id.HasValue ?
                new ObjectParameter("cost_center_id", cost_center_id) :
                new ObjectParameter("cost_center_id", typeof(int));
    
            var treasury_idParameter = treasury_id.HasValue ?
                new ObjectParameter("treasury_id", treasury_id) :
                new ObjectParameter("treasury_id", typeof(int));
    
            var cashier_invoice_nameParameter = cashier_invoice_name != null ?
                new ObjectParameter("cashier_invoice_name", cashier_invoice_name) :
                new ObjectParameter("cashier_invoice_name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("ManageUser", checkParameter, nameParameter, passwordParameter, alter_passwordParameter, delete_passwordParameter, isactiveParameter, jobParameter, cost_center_idParameter, treasury_idParameter, cashier_invoice_nameParameter);
        }
    
        public virtual ObjectResult<Nullable<long>> ManageUserLog(string check, Nullable<int> user_log_id, string user_name, Nullable<int> action_type_id, Nullable<int> document_type_id, Nullable<long> document_id, Nullable<System.DateTime> action_date, Nullable<System.DateTime> date_from, Nullable<System.DateTime> date_to)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var user_log_idParameter = user_log_id.HasValue ?
                new ObjectParameter("user_log_id", user_log_id) :
                new ObjectParameter("user_log_id", typeof(int));
    
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            var action_type_idParameter = action_type_id.HasValue ?
                new ObjectParameter("action_type_id", action_type_id) :
                new ObjectParameter("action_type_id", typeof(int));
    
            var document_type_idParameter = document_type_id.HasValue ?
                new ObjectParameter("document_type_id", document_type_id) :
                new ObjectParameter("document_type_id", typeof(int));
    
            var document_idParameter = document_id.HasValue ?
                new ObjectParameter("document_id", document_id) :
                new ObjectParameter("document_id", typeof(long));
    
            var action_dateParameter = action_date.HasValue ?
                new ObjectParameter("action_date", action_date) :
                new ObjectParameter("action_date", typeof(System.DateTime));
    
            var date_fromParameter = date_from.HasValue ?
                new ObjectParameter("date_from", date_from) :
                new ObjectParameter("date_from", typeof(System.DateTime));
    
            var date_toParameter = date_to.HasValue ?
                new ObjectParameter("date_to", date_to) :
                new ObjectParameter("date_to", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<long>>("ManageUserLog", checkParameter, user_log_idParameter, user_nameParameter, action_type_idParameter, document_type_idParameter, document_idParameter, action_dateParameter, date_fromParameter, date_toParameter);
        }
    
        public virtual int ManageUserPermissions(string check, string user_name, string permission_name)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            var permission_nameParameter = permission_name != null ?
                new ObjectParameter("permission_name", permission_name) :
                new ObjectParameter("permission_name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("ManageUserPermissions", checkParameter, user_nameParameter, permission_nameParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> ManageUserWorkShift(string check, Nullable<int> shift_id, string user_name, Nullable<int> treasury_id, Nullable<int> close_teasury_id, Nullable<System.DateTime> shift_date, Nullable<bool> shift_status, Nullable<System.DateTime> start_date, Nullable<System.DateTime> end_date, Nullable<decimal> previous_balances, Nullable<decimal> sales, Nullable<decimal> network_sales, Nullable<decimal> other_sales, Nullable<decimal> collected_installments, Nullable<decimal> sales_return, Nullable<decimal> purchases, Nullable<decimal> purchases_return, Nullable<decimal> expenses, Nullable<decimal> cash_deposit_voucher, Nullable<decimal> payment_vouchers, Nullable<decimal> receipt_vouchers, Nullable<decimal> remittance_vouchers_in, Nullable<decimal> remittance_vouchers_out, Nullable<decimal> cash_withdrawals, Nullable<decimal> cash_remaining, Nullable<decimal> net_cash_drawer, Nullable<System.DateTime> date_from, Nullable<System.DateTime> date_to)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var shift_idParameter = shift_id.HasValue ?
                new ObjectParameter("shift_id", shift_id) :
                new ObjectParameter("shift_id", typeof(int));
    
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            var treasury_idParameter = treasury_id.HasValue ?
                new ObjectParameter("treasury_id", treasury_id) :
                new ObjectParameter("treasury_id", typeof(int));
    
            var close_teasury_idParameter = close_teasury_id.HasValue ?
                new ObjectParameter("close_teasury_id", close_teasury_id) :
                new ObjectParameter("close_teasury_id", typeof(int));
    
            var shift_dateParameter = shift_date.HasValue ?
                new ObjectParameter("shift_date", shift_date) :
                new ObjectParameter("shift_date", typeof(System.DateTime));
    
            var shift_statusParameter = shift_status.HasValue ?
                new ObjectParameter("shift_status", shift_status) :
                new ObjectParameter("shift_status", typeof(bool));
    
            var start_dateParameter = start_date.HasValue ?
                new ObjectParameter("start_date", start_date) :
                new ObjectParameter("start_date", typeof(System.DateTime));
    
            var end_dateParameter = end_date.HasValue ?
                new ObjectParameter("end_date", end_date) :
                new ObjectParameter("end_date", typeof(System.DateTime));
    
            var previous_balancesParameter = previous_balances.HasValue ?
                new ObjectParameter("previous_balances", previous_balances) :
                new ObjectParameter("previous_balances", typeof(decimal));
    
            var salesParameter = sales.HasValue ?
                new ObjectParameter("sales", sales) :
                new ObjectParameter("sales", typeof(decimal));
    
            var network_salesParameter = network_sales.HasValue ?
                new ObjectParameter("network_sales", network_sales) :
                new ObjectParameter("network_sales", typeof(decimal));
    
            var other_salesParameter = other_sales.HasValue ?
                new ObjectParameter("other_sales", other_sales) :
                new ObjectParameter("other_sales", typeof(decimal));
    
            var collected_installmentsParameter = collected_installments.HasValue ?
                new ObjectParameter("collected_installments", collected_installments) :
                new ObjectParameter("collected_installments", typeof(decimal));
    
            var sales_returnParameter = sales_return.HasValue ?
                new ObjectParameter("sales_return", sales_return) :
                new ObjectParameter("sales_return", typeof(decimal));
    
            var purchasesParameter = purchases.HasValue ?
                new ObjectParameter("purchases", purchases) :
                new ObjectParameter("purchases", typeof(decimal));
    
            var purchases_returnParameter = purchases_return.HasValue ?
                new ObjectParameter("purchases_return", purchases_return) :
                new ObjectParameter("purchases_return", typeof(decimal));
    
            var expensesParameter = expenses.HasValue ?
                new ObjectParameter("expenses", expenses) :
                new ObjectParameter("expenses", typeof(decimal));
    
            var cash_deposit_voucherParameter = cash_deposit_voucher.HasValue ?
                new ObjectParameter("cash_deposit_voucher", cash_deposit_voucher) :
                new ObjectParameter("cash_deposit_voucher", typeof(decimal));
    
            var payment_vouchersParameter = payment_vouchers.HasValue ?
                new ObjectParameter("payment_vouchers", payment_vouchers) :
                new ObjectParameter("payment_vouchers", typeof(decimal));
    
            var receipt_vouchersParameter = receipt_vouchers.HasValue ?
                new ObjectParameter("receipt_vouchers", receipt_vouchers) :
                new ObjectParameter("receipt_vouchers", typeof(decimal));
    
            var remittance_vouchers_inParameter = remittance_vouchers_in.HasValue ?
                new ObjectParameter("remittance_vouchers_in", remittance_vouchers_in) :
                new ObjectParameter("remittance_vouchers_in", typeof(decimal));
    
            var remittance_vouchers_outParameter = remittance_vouchers_out.HasValue ?
                new ObjectParameter("remittance_vouchers_out", remittance_vouchers_out) :
                new ObjectParameter("remittance_vouchers_out", typeof(decimal));
    
            var cash_withdrawalsParameter = cash_withdrawals.HasValue ?
                new ObjectParameter("cash_withdrawals", cash_withdrawals) :
                new ObjectParameter("cash_withdrawals", typeof(decimal));
    
            var cash_remainingParameter = cash_remaining.HasValue ?
                new ObjectParameter("cash_remaining", cash_remaining) :
                new ObjectParameter("cash_remaining", typeof(decimal));
    
            var net_cash_drawerParameter = net_cash_drawer.HasValue ?
                new ObjectParameter("net_cash_drawer", net_cash_drawer) :
                new ObjectParameter("net_cash_drawer", typeof(decimal));
    
            var date_fromParameter = date_from.HasValue ?
                new ObjectParameter("date_from", date_from) :
                new ObjectParameter("date_from", typeof(System.DateTime));
    
            var date_toParameter = date_to.HasValue ?
                new ObjectParameter("date_to", date_to) :
                new ObjectParameter("date_to", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("ManageUserWorkShift", checkParameter, shift_idParameter, user_nameParameter, treasury_idParameter, close_teasury_idParameter, shift_dateParameter, shift_statusParameter, start_dateParameter, end_dateParameter, previous_balancesParameter, salesParameter, network_salesParameter, other_salesParameter, collected_installmentsParameter, sales_returnParameter, purchasesParameter, purchases_returnParameter, expensesParameter, cash_deposit_voucherParameter, payment_vouchersParameter, receipt_vouchersParameter, remittance_vouchers_inParameter, remittance_vouchers_outParameter, cash_withdrawalsParameter, cash_remainingParameter, net_cash_drawerParameter, date_fromParameter, date_toParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> ManageWorkPeriod(string check, Nullable<int> work_period_id, string work_period_name, Nullable<System.TimeSpan> work_start_time, Nullable<System.TimeSpan> work_end_time, Nullable<decimal> late_ratio_allowance, Nullable<decimal> late_ratio, Nullable<decimal> addition_ratio_allowance, Nullable<decimal> addition_ratio, Nullable<bool> sat, Nullable<bool> sun, Nullable<bool> mon, Nullable<bool> tue, Nullable<bool> wed, Nullable<bool> thu, Nullable<bool> fri, string notes, string user_name)
        {
            var checkParameter = check != null ?
                new ObjectParameter("check", check) :
                new ObjectParameter("check", typeof(string));
    
            var work_period_idParameter = work_period_id.HasValue ?
                new ObjectParameter("work_period_id", work_period_id) :
                new ObjectParameter("work_period_id", typeof(int));
    
            var work_period_nameParameter = work_period_name != null ?
                new ObjectParameter("work_period_name", work_period_name) :
                new ObjectParameter("work_period_name", typeof(string));
    
            var work_start_timeParameter = work_start_time.HasValue ?
                new ObjectParameter("work_start_time", work_start_time) :
                new ObjectParameter("work_start_time", typeof(System.TimeSpan));
    
            var work_end_timeParameter = work_end_time.HasValue ?
                new ObjectParameter("work_end_time", work_end_time) :
                new ObjectParameter("work_end_time", typeof(System.TimeSpan));
    
            var late_ratio_allowanceParameter = late_ratio_allowance.HasValue ?
                new ObjectParameter("late_ratio_allowance", late_ratio_allowance) :
                new ObjectParameter("late_ratio_allowance", typeof(decimal));
    
            var late_ratioParameter = late_ratio.HasValue ?
                new ObjectParameter("late_ratio", late_ratio) :
                new ObjectParameter("late_ratio", typeof(decimal));
    
            var addition_ratio_allowanceParameter = addition_ratio_allowance.HasValue ?
                new ObjectParameter("addition_ratio_allowance", addition_ratio_allowance) :
                new ObjectParameter("addition_ratio_allowance", typeof(decimal));
    
            var addition_ratioParameter = addition_ratio.HasValue ?
                new ObjectParameter("addition_ratio", addition_ratio) :
                new ObjectParameter("addition_ratio", typeof(decimal));
    
            var satParameter = sat.HasValue ?
                new ObjectParameter("sat", sat) :
                new ObjectParameter("sat", typeof(bool));
    
            var sunParameter = sun.HasValue ?
                new ObjectParameter("sun", sun) :
                new ObjectParameter("sun", typeof(bool));
    
            var monParameter = mon.HasValue ?
                new ObjectParameter("mon", mon) :
                new ObjectParameter("mon", typeof(bool));
    
            var tueParameter = tue.HasValue ?
                new ObjectParameter("tue", tue) :
                new ObjectParameter("tue", typeof(bool));
    
            var wedParameter = wed.HasValue ?
                new ObjectParameter("wed", wed) :
                new ObjectParameter("wed", typeof(bool));
    
            var thuParameter = thu.HasValue ?
                new ObjectParameter("thu", thu) :
                new ObjectParameter("thu", typeof(bool));
    
            var friParameter = fri.HasValue ?
                new ObjectParameter("fri", fri) :
                new ObjectParameter("fri", typeof(bool));
    
            var notesParameter = notes != null ?
                new ObjectParameter("notes", notes) :
                new ObjectParameter("notes", typeof(string));
    
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("ManageWorkPeriod", checkParameter, work_period_idParameter, work_period_nameParameter, work_start_timeParameter, work_end_timeParameter, late_ratio_allowanceParameter, late_ratioParameter, addition_ratio_allowanceParameter, addition_ratioParameter, satParameter, sunParameter, monParameter, tueParameter, wedParameter, thuParameter, friParameter, notesParameter, user_nameParameter);
        }
    
        public virtual int MoveOrderToAnotherTable(Nullable<int> inv_id, Nullable<int> table_id)
        {
            var inv_idParameter = inv_id.HasValue ?
                new ObjectParameter("inv_id", inv_id) :
                new ObjectParameter("inv_id", typeof(int));
    
            var table_idParameter = table_id.HasValue ?
                new ObjectParameter("table_id", table_id) :
                new ObjectParameter("table_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("MoveOrderToAnotherTable", inv_idParameter, table_idParameter);
        }
    
        public virtual ObjectResult<PrintTreasuryPermission_Result> PrintTreasuryPermission(Nullable<int> per_id)
        {
            var per_idParameter = per_id.HasValue ?
                new ObjectParameter("per_id", per_id) :
                new ObjectParameter("per_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PrintTreasuryPermission_Result>("PrintTreasuryPermission", per_idParameter);
        }
    
        public virtual ObjectResult<PRTTreasuryExpenses_Result> PRTTreasuryExpenses(Nullable<int> per_kind, Nullable<int> cost_center_id, Nullable<int> expense_id, Nullable<int> treasury_id, string user_name, Nullable<System.DateTime> datefrom, Nullable<System.DateTime> dateto)
        {
            var per_kindParameter = per_kind.HasValue ?
                new ObjectParameter("per_kind", per_kind) :
                new ObjectParameter("per_kind", typeof(int));
    
            var cost_center_idParameter = cost_center_id.HasValue ?
                new ObjectParameter("cost_center_id", cost_center_id) :
                new ObjectParameter("cost_center_id", typeof(int));
    
            var expense_idParameter = expense_id.HasValue ?
                new ObjectParameter("expense_id", expense_id) :
                new ObjectParameter("expense_id", typeof(int));
    
            var treasury_idParameter = treasury_id.HasValue ?
                new ObjectParameter("treasury_id", treasury_id) :
                new ObjectParameter("treasury_id", typeof(int));
    
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            var datefromParameter = datefrom.HasValue ?
                new ObjectParameter("datefrom", datefrom) :
                new ObjectParameter("datefrom", typeof(System.DateTime));
    
            var datetoParameter = dateto.HasValue ?
                new ObjectParameter("dateto", dateto) :
                new ObjectParameter("dateto", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PRTTreasuryExpenses_Result>("PRTTreasuryExpenses", per_kindParameter, cost_center_idParameter, expense_idParameter, treasury_idParameter, user_nameParameter, datefromParameter, datetoParameter);
        }
    
        public virtual ObjectResult<RPTGetAllCustomersBalance_Result> RPTGetAllCustomersBalance(string cust_kind, Nullable<int> cust_id, Nullable<int> sales_rep_id, Nullable<System.DateTime> datefrom, Nullable<System.DateTime> dateto, Nullable<bool> zeroaccounts)
        {
            var cust_kindParameter = cust_kind != null ?
                new ObjectParameter("cust_kind", cust_kind) :
                new ObjectParameter("cust_kind", typeof(string));
    
            var cust_idParameter = cust_id.HasValue ?
                new ObjectParameter("cust_id", cust_id) :
                new ObjectParameter("cust_id", typeof(int));
    
            var sales_rep_idParameter = sales_rep_id.HasValue ?
                new ObjectParameter("sales_rep_id", sales_rep_id) :
                new ObjectParameter("sales_rep_id", typeof(int));
    
            var datefromParameter = datefrom.HasValue ?
                new ObjectParameter("datefrom", datefrom) :
                new ObjectParameter("datefrom", typeof(System.DateTime));
    
            var datetoParameter = dateto.HasValue ?
                new ObjectParameter("dateto", dateto) :
                new ObjectParameter("dateto", typeof(System.DateTime));
    
            var zeroaccountsParameter = zeroaccounts.HasValue ?
                new ObjectParameter("zeroaccounts", zeroaccounts) :
                new ObjectParameter("zeroaccounts", typeof(bool));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTGetAllCustomersBalance_Result>("RPTGetAllCustomersBalance", cust_kindParameter, cust_idParameter, sales_rep_idParameter, datefromParameter, datetoParameter, zeroaccountsParameter);
        }
    
        public virtual ObjectResult<RPTGetBankAccountBalance_Result> RPTGetBankAccountBalance(Nullable<int> account_id, Nullable<bool> zero_accounts, Nullable<System.DateTime> datefrom, Nullable<System.DateTime> dateto)
        {
            var account_idParameter = account_id.HasValue ?
                new ObjectParameter("account_id", account_id) :
                new ObjectParameter("account_id", typeof(int));
    
            var zero_accountsParameter = zero_accounts.HasValue ?
                new ObjectParameter("zero_accounts", zero_accounts) :
                new ObjectParameter("zero_accounts", typeof(bool));
    
            var datefromParameter = datefrom.HasValue ?
                new ObjectParameter("datefrom", datefrom) :
                new ObjectParameter("datefrom", typeof(System.DateTime));
    
            var datetoParameter = dateto.HasValue ?
                new ObjectParameter("dateto", dateto) :
                new ObjectParameter("dateto", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTGetBankAccountBalance_Result>("RPTGetBankAccountBalance", account_idParameter, zero_accountsParameter, datefromParameter, datetoParameter);
        }
    
        public virtual ObjectResult<RPTGetBankAccountMovement_Result> RPTGetBankAccountMovement(Nullable<int> bank_account_id, Nullable<System.DateTime> datefrom, Nullable<System.DateTime> dateto)
        {
            var bank_account_idParameter = bank_account_id.HasValue ?
                new ObjectParameter("bank_account_id", bank_account_id) :
                new ObjectParameter("bank_account_id", typeof(int));
    
            var datefromParameter = datefrom.HasValue ?
                new ObjectParameter("datefrom", datefrom) :
                new ObjectParameter("datefrom", typeof(System.DateTime));
    
            var datetoParameter = dateto.HasValue ?
                new ObjectParameter("dateto", dateto) :
                new ObjectParameter("dateto", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTGetBankAccountMovement_Result>("RPTGetBankAccountMovement", bank_account_idParameter, datefromParameter, datetoParameter);
        }
    
        public virtual ObjectResult<RPTGetCustBalanceMovement_Result> RPTGetCustBalanceMovement(Nullable<int> cust_id, Nullable<System.DateTime> datefrom, Nullable<System.DateTime> dateto)
        {
            var cust_idParameter = cust_id.HasValue ?
                new ObjectParameter("cust_id", cust_id) :
                new ObjectParameter("cust_id", typeof(int));
    
            var datefromParameter = datefrom.HasValue ?
                new ObjectParameter("datefrom", datefrom) :
                new ObjectParameter("datefrom", typeof(System.DateTime));
    
            var datetoParameter = dateto.HasValue ?
                new ObjectParameter("dateto", dateto) :
                new ObjectParameter("dateto", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTGetCustBalanceMovement_Result>("RPTGetCustBalanceMovement", cust_idParameter, datefromParameter, datetoParameter);
        }
    
        public virtual ObjectResult<RPTGetCustBalanceMovementDetail_Result> RPTGetCustBalanceMovementDetail(Nullable<int> cust_id, Nullable<System.DateTime> datefrom, Nullable<System.DateTime> dateto)
        {
            var cust_idParameter = cust_id.HasValue ?
                new ObjectParameter("cust_id", cust_id) :
                new ObjectParameter("cust_id", typeof(int));
    
            var datefromParameter = datefrom.HasValue ?
                new ObjectParameter("datefrom", datefrom) :
                new ObjectParameter("datefrom", typeof(System.DateTime));
    
            var datetoParameter = dateto.HasValue ?
                new ObjectParameter("dateto", dateto) :
                new ObjectParameter("dateto", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTGetCustBalanceMovementDetail_Result>("RPTGetCustBalanceMovementDetail", cust_idParameter, datefromParameter, datetoParameter);
        }
    
        public virtual ObjectResult<RPTGetCustomerBalance_Result> RPTGetCustomerBalance(Nullable<int> cust_id, Nullable<System.DateTime> dateto)
        {
            var cust_idParameter = cust_id.HasValue ?
                new ObjectParameter("cust_id", cust_id) :
                new ObjectParameter("cust_id", typeof(int));
    
            var datetoParameter = dateto.HasValue ?
                new ObjectParameter("dateto", dateto) :
                new ObjectParameter("dateto", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTGetCustomerBalance_Result>("RPTGetCustomerBalance", cust_idParameter, datetoParameter);
        }
    
        public virtual ObjectResult<RPTGetEmployeeABsenceDays_Result> RPTGetEmployeeABsenceDays(Nullable<int> employee_id, Nullable<System.DateTime> date_from, Nullable<System.DateTime> date_to)
        {
            var employee_idParameter = employee_id.HasValue ?
                new ObjectParameter("employee_id", employee_id) :
                new ObjectParameter("employee_id", typeof(int));
    
            var date_fromParameter = date_from.HasValue ?
                new ObjectParameter("date_from", date_from) :
                new ObjectParameter("date_from", typeof(System.DateTime));
    
            var date_toParameter = date_to.HasValue ?
                new ObjectParameter("date_to", date_to) :
                new ObjectParameter("date_to", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTGetEmployeeABsenceDays_Result>("RPTGetEmployeeABsenceDays", employee_idParameter, date_fromParameter, date_toParameter);
        }
    
        public virtual ObjectResult<RPTGetEmployeeAdditionsOnPeriod_Result> RPTGetEmployeeAdditionsOnPeriod(Nullable<int> employee_id, Nullable<System.DateTime> date_from, Nullable<System.DateTime> date_to)
        {
            var employee_idParameter = employee_id.HasValue ?
                new ObjectParameter("employee_id", employee_id) :
                new ObjectParameter("employee_id", typeof(int));
    
            var date_fromParameter = date_from.HasValue ?
                new ObjectParameter("date_from", date_from) :
                new ObjectParameter("date_from", typeof(System.DateTime));
    
            var date_toParameter = date_to.HasValue ?
                new ObjectParameter("date_to", date_to) :
                new ObjectParameter("date_to", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTGetEmployeeAdditionsOnPeriod_Result>("RPTGetEmployeeAdditionsOnPeriod", employee_idParameter, date_fromParameter, date_toParameter);
        }
    
        public virtual ObjectResult<RPTGetEmployeeCalenderLog_Result> RPTGetEmployeeCalenderLog(Nullable<int> employee_id, Nullable<System.DateTime> date_from, Nullable<System.DateTime> date_to, Nullable<int> vacation_type)
        {
            var employee_idParameter = employee_id.HasValue ?
                new ObjectParameter("employee_id", employee_id) :
                new ObjectParameter("employee_id", typeof(int));
    
            var date_fromParameter = date_from.HasValue ?
                new ObjectParameter("date_from", date_from) :
                new ObjectParameter("date_from", typeof(System.DateTime));
    
            var date_toParameter = date_to.HasValue ?
                new ObjectParameter("date_to", date_to) :
                new ObjectParameter("date_to", typeof(System.DateTime));
    
            var vacation_typeParameter = vacation_type.HasValue ?
                new ObjectParameter("vacation_type", vacation_type) :
                new ObjectParameter("vacation_type", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTGetEmployeeCalenderLog_Result>("RPTGetEmployeeCalenderLog", employee_idParameter, date_fromParameter, date_toParameter, vacation_typeParameter);
        }
    
        public virtual ObjectResult<RPTGetEmployeeDelaysOnPeriod_Result> RPTGetEmployeeDelaysOnPeriod(Nullable<int> employee_id, Nullable<System.DateTime> date_from, Nullable<System.DateTime> date_to)
        {
            var employee_idParameter = employee_id.HasValue ?
                new ObjectParameter("employee_id", employee_id) :
                new ObjectParameter("employee_id", typeof(int));
    
            var date_fromParameter = date_from.HasValue ?
                new ObjectParameter("date_from", date_from) :
                new ObjectParameter("date_from", typeof(System.DateTime));
    
            var date_toParameter = date_to.HasValue ?
                new ObjectParameter("date_to", date_to) :
                new ObjectParameter("date_to", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTGetEmployeeDelaysOnPeriod_Result>("RPTGetEmployeeDelaysOnPeriod", employee_idParameter, date_fromParameter, date_toParameter);
        }
    
        public virtual ObjectResult<RPTGetEmployeePayrollDetailsActivityItems_Result> RPTGetEmployeePayrollDetailsActivityItems(Nullable<int> payroll_id)
        {
            var payroll_idParameter = payroll_id.HasValue ?
                new ObjectParameter("payroll_id", payroll_id) :
                new ObjectParameter("payroll_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTGetEmployeePayrollDetailsActivityItems_Result>("RPTGetEmployeePayrollDetailsActivityItems", payroll_idParameter);
        }
    
        public virtual ObjectResult<RPTGetEmployeePayrollDetailsSalaryItems_Result> RPTGetEmployeePayrollDetailsSalaryItems(Nullable<int> payroll_id)
        {
            var payroll_idParameter = payroll_id.HasValue ?
                new ObjectParameter("payroll_id", payroll_id) :
                new ObjectParameter("payroll_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTGetEmployeePayrollDetailsSalaryItems_Result>("RPTGetEmployeePayrollDetailsSalaryItems", payroll_idParameter);
        }
    
        public virtual ObjectResult<RPTGetEmployeePayrollHeader_Result> RPTGetEmployeePayrollHeader(Nullable<int> payroll_id)
        {
            var payroll_idParameter = payroll_id.HasValue ?
                new ObjectParameter("payroll_id", payroll_id) :
                new ObjectParameter("payroll_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTGetEmployeePayrollHeader_Result>("RPTGetEmployeePayrollHeader", payroll_idParameter);
        }
    
        public virtual ObjectResult<RPTGetEmployeeSalaryOnPeriod_Result> RPTGetEmployeeSalaryOnPeriod(Nullable<int> employee_id, Nullable<System.DateTime> date_from, Nullable<System.DateTime> date_to)
        {
            var employee_idParameter = employee_id.HasValue ?
                new ObjectParameter("employee_id", employee_id) :
                new ObjectParameter("employee_id", typeof(int));
    
            var date_fromParameter = date_from.HasValue ?
                new ObjectParameter("date_from", date_from) :
                new ObjectParameter("date_from", typeof(System.DateTime));
    
            var date_toParameter = date_to.HasValue ?
                new ObjectParameter("date_to", date_to) :
                new ObjectParameter("date_to", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTGetEmployeeSalaryOnPeriod_Result>("RPTGetEmployeeSalaryOnPeriod", employee_idParameter, date_fromParameter, date_toParameter);
        }
    
        public virtual ObjectResult<RPTGetInvoiceDetailsMovement_Result> RPTGetInvoiceDetailsMovement(Nullable<int> kind, Nullable<System.DateTime> datefrom, Nullable<System.DateTime> dateto, Nullable<long> id, Nullable<int> no, Nullable<int> cust_id, Nullable<int> pay_Kind)
        {
            var kindParameter = kind.HasValue ?
                new ObjectParameter("kind", kind) :
                new ObjectParameter("kind", typeof(int));
    
            var datefromParameter = datefrom.HasValue ?
                new ObjectParameter("datefrom", datefrom) :
                new ObjectParameter("datefrom", typeof(System.DateTime));
    
            var datetoParameter = dateto.HasValue ?
                new ObjectParameter("dateto", dateto) :
                new ObjectParameter("dateto", typeof(System.DateTime));
    
            var idParameter = id.HasValue ?
                new ObjectParameter("id", id) :
                new ObjectParameter("id", typeof(long));
    
            var noParameter = no.HasValue ?
                new ObjectParameter("no", no) :
                new ObjectParameter("no", typeof(int));
    
            var cust_idParameter = cust_id.HasValue ?
                new ObjectParameter("cust_id", cust_id) :
                new ObjectParameter("cust_id", typeof(int));
    
            var pay_KindParameter = pay_Kind.HasValue ?
                new ObjectParameter("pay_Kind", pay_Kind) :
                new ObjectParameter("pay_Kind", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTGetInvoiceDetailsMovement_Result>("RPTGetInvoiceDetailsMovement", kindParameter, datefromParameter, datetoParameter, idParameter, noParameter, cust_idParameter, pay_KindParameter);
        }
    
        public virtual ObjectResult<RPTGetItemsSales_Result> RPTGetItemsSales(Nullable<int> stock_id, Nullable<int> cat_id, Nullable<long> pro_id, Nullable<int> cust_id, Nullable<int> sale_rep_id, Nullable<System.DateTime> datefrom, Nullable<System.DateTime> dateto)
        {
            var stock_idParameter = stock_id.HasValue ?
                new ObjectParameter("stock_id", stock_id) :
                new ObjectParameter("stock_id", typeof(int));
    
            var cat_idParameter = cat_id.HasValue ?
                new ObjectParameter("cat_id", cat_id) :
                new ObjectParameter("cat_id", typeof(int));
    
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            var cust_idParameter = cust_id.HasValue ?
                new ObjectParameter("cust_id", cust_id) :
                new ObjectParameter("cust_id", typeof(int));
    
            var sale_rep_idParameter = sale_rep_id.HasValue ?
                new ObjectParameter("sale_rep_id", sale_rep_id) :
                new ObjectParameter("sale_rep_id", typeof(int));
    
            var datefromParameter = datefrom.HasValue ?
                new ObjectParameter("datefrom", datefrom) :
                new ObjectParameter("datefrom", typeof(System.DateTime));
    
            var datetoParameter = dateto.HasValue ?
                new ObjectParameter("dateto", dateto) :
                new ObjectParameter("dateto", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTGetItemsSales_Result>("RPTGetItemsSales", stock_idParameter, cat_idParameter, pro_idParameter, cust_idParameter, sale_rep_idParameter, datefromParameter, datetoParameter);
        }
    
        public virtual ObjectResult<RPTGetLeastSellingItems_Result> RPTGetLeastSellingItems(Nullable<System.DateTime> datefrom, Nullable<System.DateTime> dateto)
        {
            var datefromParameter = datefrom.HasValue ?
                new ObjectParameter("datefrom", datefrom) :
                new ObjectParameter("datefrom", typeof(System.DateTime));
    
            var datetoParameter = dateto.HasValue ?
                new ObjectParameter("dateto", dateto) :
                new ObjectParameter("dateto", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTGetLeastSellingItems_Result>("RPTGetLeastSellingItems", datefromParameter, datetoParameter);
        }
    
        public virtual ObjectResult<RPTGetMostSellingItems_Result> RPTGetMostSellingItems(Nullable<System.DateTime> datefrom, Nullable<System.DateTime> dateto, Nullable<int> stock_id)
        {
            var datefromParameter = datefrom.HasValue ?
                new ObjectParameter("datefrom", datefrom) :
                new ObjectParameter("datefrom", typeof(System.DateTime));
    
            var datetoParameter = dateto.HasValue ?
                new ObjectParameter("dateto", dateto) :
                new ObjectParameter("dateto", typeof(System.DateTime));
    
            var stock_idParameter = stock_id.HasValue ?
                new ObjectParameter("stock_id", stock_id) :
                new ObjectParameter("stock_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTGetMostSellingItems_Result>("RPTGetMostSellingItems", datefromParameter, datetoParameter, stock_idParameter);
        }
    
        public virtual ObjectResult<RPTGetOrderKithenDetailsByInvID_Result> RPTGetOrderKithenDetailsByInvID(Nullable<long> inv_id)
        {
            var inv_idParameter = inv_id.HasValue ?
                new ObjectParameter("inv_id", inv_id) :
                new ObjectParameter("inv_id", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTGetOrderKithenDetailsByInvID_Result>("RPTGetOrderKithenDetailsByInvID", inv_idParameter);
        }
    
        public virtual ObjectResult<RPTGetOutStandingChecks_Result> RPTGetOutStandingChecks(string paper_no, Nullable<int> cust_id, Nullable<int> paper_type, Nullable<int> bank_id)
        {
            var paper_noParameter = paper_no != null ?
                new ObjectParameter("paper_no", paper_no) :
                new ObjectParameter("paper_no", typeof(string));
    
            var cust_idParameter = cust_id.HasValue ?
                new ObjectParameter("cust_id", cust_id) :
                new ObjectParameter("cust_id", typeof(int));
    
            var paper_typeParameter = paper_type.HasValue ?
                new ObjectParameter("paper_type", paper_type) :
                new ObjectParameter("paper_type", typeof(int));
    
            var bank_idParameter = bank_id.HasValue ?
                new ObjectParameter("bank_id", bank_id) :
                new ObjectParameter("bank_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTGetOutStandingChecks_Result>("RPTGetOutStandingChecks", paper_noParameter, cust_idParameter, paper_typeParameter, bank_idParameter);
        }
    
        public virtual ObjectResult<RPTGetPayableChecks_Result> RPTGetPayableChecks(string paper_no, Nullable<int> cust_id, Nullable<int> paper_type, Nullable<int> bank_id)
        {
            var paper_noParameter = paper_no != null ?
                new ObjectParameter("paper_no", paper_no) :
                new ObjectParameter("paper_no", typeof(string));
    
            var cust_idParameter = cust_id.HasValue ?
                new ObjectParameter("cust_id", cust_id) :
                new ObjectParameter("cust_id", typeof(int));
    
            var paper_typeParameter = paper_type.HasValue ?
                new ObjectParameter("paper_type", paper_type) :
                new ObjectParameter("paper_type", typeof(int));
    
            var bank_idParameter = bank_id.HasValue ?
                new ObjectParameter("bank_id", bank_id) :
                new ObjectParameter("bank_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTGetPayableChecks_Result>("RPTGetPayableChecks", paper_noParameter, cust_idParameter, paper_typeParameter, bank_idParameter);
        }
    
        public virtual ObjectResult<RPTGetProductsExpired_Result> RPTGetProductsExpired(Nullable<int> stock_id, Nullable<int> cat_id, Nullable<int> pro_id, Nullable<int> expirey_limit)
        {
            var stock_idParameter = stock_id.HasValue ?
                new ObjectParameter("stock_id", stock_id) :
                new ObjectParameter("stock_id", typeof(int));
    
            var cat_idParameter = cat_id.HasValue ?
                new ObjectParameter("cat_id", cat_id) :
                new ObjectParameter("cat_id", typeof(int));
    
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(int));
    
            var expirey_limitParameter = expirey_limit.HasValue ?
                new ObjectParameter("expirey_limit", expirey_limit) :
                new ObjectParameter("expirey_limit", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTGetProductsExpired_Result>("RPTGetProductsExpired", stock_idParameter, cat_idParameter, pro_idParameter, expirey_limitParameter);
        }
    
        public virtual ObjectResult<RPTGetProdUnderRequestLimit_Result> RPTGetProdUnderRequestLimit(Nullable<int> cat_id, Nullable<int> pro_id)
        {
            var cat_idParameter = cat_id.HasValue ?
                new ObjectParameter("cat_id", cat_id) :
                new ObjectParameter("cat_id", typeof(int));
    
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTGetProdUnderRequestLimit_Result>("RPTGetProdUnderRequestLimit", cat_idParameter, pro_idParameter);
        }
    
        public virtual ObjectResult<RPTGetProMovement_Result> RPTGetProMovement(Nullable<long> pro_id, Nullable<int> stock_id, Nullable<System.DateTime> datefrom, Nullable<System.DateTime> dateto)
        {
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            var stock_idParameter = stock_id.HasValue ?
                new ObjectParameter("stock_id", stock_id) :
                new ObjectParameter("stock_id", typeof(int));
    
            var datefromParameter = datefrom.HasValue ?
                new ObjectParameter("datefrom", datefrom) :
                new ObjectParameter("datefrom", typeof(System.DateTime));
    
            var datetoParameter = dateto.HasValue ?
                new ObjectParameter("dateto", dateto) :
                new ObjectParameter("dateto", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTGetProMovement_Result>("RPTGetProMovement", pro_idParameter, stock_idParameter, datefromParameter, datetoParameter);
        }
    
        public virtual ObjectResult<RPTGetProMovementExpirey_Result> RPTGetProMovementExpirey(Nullable<long> pro_id, string pro_expirey, Nullable<int> stock_id, Nullable<System.DateTime> datefrom, Nullable<System.DateTime> dateto)
        {
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            var pro_expireyParameter = pro_expirey != null ?
                new ObjectParameter("pro_expirey", pro_expirey) :
                new ObjectParameter("pro_expirey", typeof(string));
    
            var stock_idParameter = stock_id.HasValue ?
                new ObjectParameter("stock_id", stock_id) :
                new ObjectParameter("stock_id", typeof(int));
    
            var datefromParameter = datefrom.HasValue ?
                new ObjectParameter("datefrom", datefrom) :
                new ObjectParameter("datefrom", typeof(System.DateTime));
    
            var datetoParameter = dateto.HasValue ?
                new ObjectParameter("dateto", dateto) :
                new ObjectParameter("dateto", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTGetProMovementExpirey_Result>("RPTGetProMovementExpirey", pro_idParameter, pro_expireyParameter, stock_idParameter, datefromParameter, datetoParameter);
        }
    
        public virtual ObjectResult<RPTGetProMovementProfitFIFO_Result> RPTGetProMovementProfitFIFO(Nullable<long> pro_id, Nullable<int> stock_id, Nullable<System.DateTime> datefrom, Nullable<System.DateTime> dateto)
        {
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            var stock_idParameter = stock_id.HasValue ?
                new ObjectParameter("stock_id", stock_id) :
                new ObjectParameter("stock_id", typeof(int));
    
            var datefromParameter = datefrom.HasValue ?
                new ObjectParameter("datefrom", datefrom) :
                new ObjectParameter("datefrom", typeof(System.DateTime));
    
            var datetoParameter = dateto.HasValue ?
                new ObjectParameter("dateto", dateto) :
                new ObjectParameter("dateto", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTGetProMovementProfitFIFO_Result>("RPTGetProMovementProfitFIFO", pro_idParameter, stock_idParameter, datefromParameter, datetoParameter);
        }
    
        public virtual ObjectResult<RPTGetProMovementSerial_Result> RPTGetProMovementSerial(string pro_serial, Nullable<int> stock_id, Nullable<System.DateTime> datefrom, Nullable<System.DateTime> dateto)
        {
            var pro_serialParameter = pro_serial != null ?
                new ObjectParameter("pro_serial", pro_serial) :
                new ObjectParameter("pro_serial", typeof(string));
    
            var stock_idParameter = stock_id.HasValue ?
                new ObjectParameter("stock_id", stock_id) :
                new ObjectParameter("stock_id", typeof(int));
    
            var datefromParameter = datefrom.HasValue ?
                new ObjectParameter("datefrom", datefrom) :
                new ObjectParameter("datefrom", typeof(System.DateTime));
    
            var datetoParameter = dateto.HasValue ?
                new ObjectParameter("dateto", dateto) :
                new ObjectParameter("dateto", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTGetProMovementSerial_Result>("RPTGetProMovementSerial", pro_serialParameter, stock_idParameter, datefromParameter, datetoParameter);
        }
    
        public virtual ObjectResult<RPTGetRecessionItems_Result> RPTGetRecessionItems(Nullable<decimal> recession_rate, Nullable<System.DateTime> datefrom, Nullable<System.DateTime> dateto, Nullable<int> stock_id)
        {
            var recession_rateParameter = recession_rate.HasValue ?
                new ObjectParameter("recession_rate", recession_rate) :
                new ObjectParameter("recession_rate", typeof(decimal));
    
            var datefromParameter = datefrom.HasValue ?
                new ObjectParameter("datefrom", datefrom) :
                new ObjectParameter("datefrom", typeof(System.DateTime));
    
            var datetoParameter = dateto.HasValue ?
                new ObjectParameter("dateto", dateto) :
                new ObjectParameter("dateto", typeof(System.DateTime));
    
            var stock_idParameter = stock_id.HasValue ?
                new ObjectParameter("stock_id", stock_id) :
                new ObjectParameter("stock_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTGetRecessionItems_Result>("RPTGetRecessionItems", recession_rateParameter, datefromParameter, datetoParameter, stock_idParameter);
        }
    
        public virtual ObjectResult<RPTGetStockItemQty_Result> RPTGetStockItemQty(Nullable<int> price_type, Nullable<int> stock_id, Nullable<int> cat_id, Nullable<long> pro_id, string pro_name, Nullable<System.DateTime> datefrom, Nullable<System.DateTime> dateto, Nullable<bool> zero_accounts)
        {
            var price_typeParameter = price_type.HasValue ?
                new ObjectParameter("price_type", price_type) :
                new ObjectParameter("price_type", typeof(int));
    
            var stock_idParameter = stock_id.HasValue ?
                new ObjectParameter("stock_id", stock_id) :
                new ObjectParameter("stock_id", typeof(int));
    
            var cat_idParameter = cat_id.HasValue ?
                new ObjectParameter("cat_id", cat_id) :
                new ObjectParameter("cat_id", typeof(int));
    
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            var pro_nameParameter = pro_name != null ?
                new ObjectParameter("pro_name", pro_name) :
                new ObjectParameter("pro_name", typeof(string));
    
            var datefromParameter = datefrom.HasValue ?
                new ObjectParameter("datefrom", datefrom) :
                new ObjectParameter("datefrom", typeof(System.DateTime));
    
            var datetoParameter = dateto.HasValue ?
                new ObjectParameter("dateto", dateto) :
                new ObjectParameter("dateto", typeof(System.DateTime));
    
            var zero_accountsParameter = zero_accounts.HasValue ?
                new ObjectParameter("zero_accounts", zero_accounts) :
                new ObjectParameter("zero_accounts", typeof(bool));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTGetStockItemQty_Result>("RPTGetStockItemQty", price_typeParameter, stock_idParameter, cat_idParameter, pro_idParameter, pro_nameParameter, datefromParameter, datetoParameter, zero_accountsParameter);
        }
    
        public virtual int RPTGetStockItemQtySerial(Nullable<int> price_type, Nullable<int> stock_id, Nullable<int> cat_id, Nullable<long> pro_id, string pro_name, string pro_serial, Nullable<System.DateTime> datefrom, Nullable<System.DateTime> dateto, Nullable<bool> zero_accounts)
        {
            var price_typeParameter = price_type.HasValue ?
                new ObjectParameter("price_type", price_type) :
                new ObjectParameter("price_type", typeof(int));
    
            var stock_idParameter = stock_id.HasValue ?
                new ObjectParameter("stock_id", stock_id) :
                new ObjectParameter("stock_id", typeof(int));
    
            var cat_idParameter = cat_id.HasValue ?
                new ObjectParameter("cat_id", cat_id) :
                new ObjectParameter("cat_id", typeof(int));
    
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            var pro_nameParameter = pro_name != null ?
                new ObjectParameter("pro_name", pro_name) :
                new ObjectParameter("pro_name", typeof(string));
    
            var pro_serialParameter = pro_serial != null ?
                new ObjectParameter("pro_serial", pro_serial) :
                new ObjectParameter("pro_serial", typeof(string));
    
            var datefromParameter = datefrom.HasValue ?
                new ObjectParameter("datefrom", datefrom) :
                new ObjectParameter("datefrom", typeof(System.DateTime));
    
            var datetoParameter = dateto.HasValue ?
                new ObjectParameter("dateto", dateto) :
                new ObjectParameter("dateto", typeof(System.DateTime));
    
            var zero_accountsParameter = zero_accounts.HasValue ?
                new ObjectParameter("zero_accounts", zero_accounts) :
                new ObjectParameter("zero_accounts", typeof(bool));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("RPTGetStockItemQtySerial", price_typeParameter, stock_idParameter, cat_idParameter, pro_idParameter, pro_nameParameter, pro_serialParameter, datefromParameter, datetoParameter, zero_accountsParameter);
        }
    
        public virtual ObjectResult<RPTGetTreasuryBalance_Result> RPTGetTreasuryBalance(Nullable<int> treas_id, Nullable<System.DateTime> datefrom, Nullable<System.DateTime> dateto)
        {
            var treas_idParameter = treas_id.HasValue ?
                new ObjectParameter("treas_id", treas_id) :
                new ObjectParameter("treas_id", typeof(int));
    
            var datefromParameter = datefrom.HasValue ?
                new ObjectParameter("datefrom", datefrom) :
                new ObjectParameter("datefrom", typeof(System.DateTime));
    
            var datetoParameter = dateto.HasValue ?
                new ObjectParameter("dateto", dateto) :
                new ObjectParameter("dateto", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTGetTreasuryBalance_Result>("RPTGetTreasuryBalance", treas_idParameter, datefromParameter, datetoParameter);
        }
    
        public virtual ObjectResult<RPTGetTreasutyMovement_Result> RPTGetTreasutyMovement(Nullable<int> treas_id, Nullable<System.DateTime> datefrom, Nullable<System.DateTime> dateto)
        {
            var treas_idParameter = treas_id.HasValue ?
                new ObjectParameter("treas_id", treas_id) :
                new ObjectParameter("treas_id", typeof(int));
    
            var datefromParameter = datefrom.HasValue ?
                new ObjectParameter("datefrom", datefrom) :
                new ObjectParameter("datefrom", typeof(System.DateTime));
    
            var datetoParameter = dateto.HasValue ?
                new ObjectParameter("dateto", dateto) :
                new ObjectParameter("dateto", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTGetTreasutyMovement_Result>("RPTGetTreasutyMovement", treas_idParameter, datefromParameter, datetoParameter);
        }
    
        public virtual ObjectResult<RPTIncomeStatement_Result> RPTIncomeStatement(Nullable<System.DateTime> datefrom, Nullable<System.DateTime> dateto, Nullable<int> pricetype)
        {
            var datefromParameter = datefrom.HasValue ?
                new ObjectParameter("datefrom", datefrom) :
                new ObjectParameter("datefrom", typeof(System.DateTime));
    
            var datetoParameter = dateto.HasValue ?
                new ObjectParameter("dateto", dateto) :
                new ObjectParameter("dateto", typeof(System.DateTime));
    
            var pricetypeParameter = pricetype.HasValue ?
                new ObjectParameter("pricetype", pricetype) :
                new ObjectParameter("pricetype", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTIncomeStatement_Result>("RPTIncomeStatement", datefromParameter, datetoParameter, pricetypeParameter);
        }
    
        public virtual ObjectResult<RPTInvoiceMovement_Result> RPTInvoiceMovement(Nullable<int> kind, Nullable<System.DateTime> datefrom, Nullable<System.DateTime> dateto, Nullable<int> id, Nullable<int> no, Nullable<int> cust_id, Nullable<int> pay_Kind, Nullable<int> treasury_id)
        {
            var kindParameter = kind.HasValue ?
                new ObjectParameter("kind", kind) :
                new ObjectParameter("kind", typeof(int));
    
            var datefromParameter = datefrom.HasValue ?
                new ObjectParameter("datefrom", datefrom) :
                new ObjectParameter("datefrom", typeof(System.DateTime));
    
            var datetoParameter = dateto.HasValue ?
                new ObjectParameter("dateto", dateto) :
                new ObjectParameter("dateto", typeof(System.DateTime));
    
            var idParameter = id.HasValue ?
                new ObjectParameter("id", id) :
                new ObjectParameter("id", typeof(int));
    
            var noParameter = no.HasValue ?
                new ObjectParameter("no", no) :
                new ObjectParameter("no", typeof(int));
    
            var cust_idParameter = cust_id.HasValue ?
                new ObjectParameter("cust_id", cust_id) :
                new ObjectParameter("cust_id", typeof(int));
    
            var pay_KindParameter = pay_Kind.HasValue ?
                new ObjectParameter("pay_Kind", pay_Kind) :
                new ObjectParameter("pay_Kind", typeof(int));
    
            var treasury_idParameter = treasury_id.HasValue ?
                new ObjectParameter("treasury_id", treasury_id) :
                new ObjectParameter("treasury_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTInvoiceMovement_Result>("RPTInvoiceMovement", kindParameter, datefromParameter, datetoParameter, idParameter, noParameter, cust_idParameter, pay_KindParameter, treasury_idParameter);
        }
    
        public virtual ObjectResult<RPTInvoicePrint_Result> RPTInvoicePrint(Nullable<int> inv_id, string inv_print_footer_txt, Nullable<bool> print_previos_balance)
        {
            var inv_idParameter = inv_id.HasValue ?
                new ObjectParameter("inv_id", inv_id) :
                new ObjectParameter("inv_id", typeof(int));
    
            var inv_print_footer_txtParameter = inv_print_footer_txt != null ?
                new ObjectParameter("inv_print_footer_txt", inv_print_footer_txt) :
                new ObjectParameter("inv_print_footer_txt", typeof(string));
    
            var print_previos_balanceParameter = print_previos_balance.HasValue ?
                new ObjectParameter("print_previos_balance", print_previos_balance) :
                new ObjectParameter("print_previos_balance", typeof(bool));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTInvoicePrint_Result>("RPTInvoicePrint", inv_idParameter, inv_print_footer_txtParameter, print_previos_balanceParameter);
        }
    
        public virtual ObjectResult<RPTInvoiceProfit_Result> RPTInvoiceProfit(Nullable<int> inv_id, Nullable<int> inv_no, Nullable<int> cust_id, Nullable<int> price_type, Nullable<System.DateTime> date_from, Nullable<System.DateTime> date_to)
        {
            var inv_idParameter = inv_id.HasValue ?
                new ObjectParameter("inv_id", inv_id) :
                new ObjectParameter("inv_id", typeof(int));
    
            var inv_noParameter = inv_no.HasValue ?
                new ObjectParameter("inv_no", inv_no) :
                new ObjectParameter("inv_no", typeof(int));
    
            var cust_idParameter = cust_id.HasValue ?
                new ObjectParameter("cust_id", cust_id) :
                new ObjectParameter("cust_id", typeof(int));
    
            var price_typeParameter = price_type.HasValue ?
                new ObjectParameter("price_type", price_type) :
                new ObjectParameter("price_type", typeof(int));
    
            var date_fromParameter = date_from.HasValue ?
                new ObjectParameter("date_from", date_from) :
                new ObjectParameter("date_from", typeof(System.DateTime));
    
            var date_toParameter = date_to.HasValue ?
                new ObjectParameter("date_to", date_to) :
                new ObjectParameter("date_to", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTInvoiceProfit_Result>("RPTInvoiceProfit", inv_idParameter, inv_noParameter, cust_idParameter, price_typeParameter, date_fromParameter, date_toParameter);
        }
    
        public virtual ObjectResult<RPTInvoiceProfitFIFO_Result> RPTInvoiceProfitFIFO(Nullable<int> inv_id, Nullable<int> inv_no, Nullable<int> cust_id, Nullable<System.DateTime> date_from, Nullable<System.DateTime> date_to)
        {
            var inv_idParameter = inv_id.HasValue ?
                new ObjectParameter("inv_id", inv_id) :
                new ObjectParameter("inv_id", typeof(int));
    
            var inv_noParameter = inv_no.HasValue ?
                new ObjectParameter("inv_no", inv_no) :
                new ObjectParameter("inv_no", typeof(int));
    
            var cust_idParameter = cust_id.HasValue ?
                new ObjectParameter("cust_id", cust_id) :
                new ObjectParameter("cust_id", typeof(int));
    
            var date_fromParameter = date_from.HasValue ?
                new ObjectParameter("date_from", date_from) :
                new ObjectParameter("date_from", typeof(System.DateTime));
    
            var date_toParameter = date_to.HasValue ?
                new ObjectParameter("date_to", date_to) :
                new ObjectParameter("date_to", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTInvoiceProfitFIFO_Result>("RPTInvoiceProfitFIFO", inv_idParameter, inv_noParameter, cust_idParameter, date_fromParameter, date_toParameter);
        }
    
        public virtual ObjectResult<RPTItemList_Result> RPTItemList(Nullable<long> pro_id, Nullable<int> cat_id)
        {
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            var cat_idParameter = cat_id.HasValue ?
                new ObjectParameter("cat_id", cat_id) :
                new ObjectParameter("cat_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTItemList_Result>("RPTItemList", pro_idParameter, cat_idParameter);
        }
    
        public virtual ObjectResult<RPTItemProfit_Result> RPTItemProfit(Nullable<int> cat_id, Nullable<long> pro_id, Nullable<int> price_type)
        {
            var cat_idParameter = cat_id.HasValue ?
                new ObjectParameter("cat_id", cat_id) :
                new ObjectParameter("cat_id", typeof(int));
    
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            var price_typeParameter = price_type.HasValue ?
                new ObjectParameter("price_type", price_type) :
                new ObjectParameter("price_type", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTItemProfit_Result>("RPTItemProfit", cat_idParameter, pro_idParameter, price_typeParameter);
        }
    
        public virtual ObjectResult<RPTItemProfitFiFOPolicy_Result> RPTItemProfitFiFOPolicy(Nullable<long> pro_id, Nullable<int> cat_id, Nullable<System.DateTime> date_from, Nullable<System.DateTime> date_to)
        {
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            var cat_idParameter = cat_id.HasValue ?
                new ObjectParameter("cat_id", cat_id) :
                new ObjectParameter("cat_id", typeof(int));
    
            var date_fromParameter = date_from.HasValue ?
                new ObjectParameter("date_from", date_from) :
                new ObjectParameter("date_from", typeof(System.DateTime));
    
            var date_toParameter = date_to.HasValue ?
                new ObjectParameter("date_to", date_to) :
                new ObjectParameter("date_to", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTItemProfitFiFOPolicy_Result>("RPTItemProfitFiFOPolicy", pro_idParameter, cat_idParameter, date_fromParameter, date_toParameter);
        }
    
        public virtual ObjectResult<RPTItemsSummaryResultPerStock_Result> RPTItemsSummaryResultPerStock(Nullable<int> stockID, Nullable<int> categoryID, Nullable<int> proID, Nullable<System.DateTime> toDate)
        {
            var stockIDParameter = stockID.HasValue ?
                new ObjectParameter("StockID", stockID) :
                new ObjectParameter("StockID", typeof(int));
    
            var categoryIDParameter = categoryID.HasValue ?
                new ObjectParameter("CategoryID", categoryID) :
                new ObjectParameter("CategoryID", typeof(int));
    
            var proIDParameter = proID.HasValue ?
                new ObjectParameter("ProID", proID) :
                new ObjectParameter("ProID", typeof(int));
    
            var toDateParameter = toDate.HasValue ?
                new ObjectParameter("ToDate", toDate) :
                new ObjectParameter("ToDate", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTItemsSummaryResultPerStock_Result>("RPTItemsSummaryResultPerStock", stockIDParameter, categoryIDParameter, proIDParameter, toDateParameter);
        }
    
        public virtual ObjectResult<RPTMaintenanceDeliveryAlert_Result> RPTMaintenanceDeliveryAlert(Nullable<int> alertDate)
        {
            var alertDateParameter = alertDate.HasValue ?
                new ObjectParameter("AlertDate", alertDate) :
                new ObjectParameter("AlertDate", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTMaintenanceDeliveryAlert_Result>("RPTMaintenanceDeliveryAlert", alertDateParameter);
        }
    
        public virtual ObjectResult<RPTOverdueInstallments_Result> RPTOverdueInstallments()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTOverdueInstallments_Result>("RPTOverdueInstallments");
        }
    
        public virtual ObjectResult<RPTPricesList_Result> RPTPricesList(Nullable<long> pro_id, Nullable<int> cat_id, string price_type)
        {
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            var cat_idParameter = cat_id.HasValue ?
                new ObjectParameter("cat_id", cat_id) :
                new ObjectParameter("cat_id", typeof(int));
    
            var price_typeParameter = price_type != null ?
                new ObjectParameter("price_type", price_type) :
                new ObjectParameter("price_type", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTPricesList_Result>("RPTPricesList", pro_idParameter, cat_idParameter, price_typeParameter);
        }
    
        public virtual ObjectResult<RPTPurchaseAndSaleQuantityOfProduct_Result> RPTPurchaseAndSaleQuantityOfProduct(Nullable<int> cat_id, Nullable<long> pro_id, Nullable<int> cust_id, Nullable<int> sale_rep_id, Nullable<System.DateTime> datefrom, Nullable<System.DateTime> dateto)
        {
            var cat_idParameter = cat_id.HasValue ?
                new ObjectParameter("cat_id", cat_id) :
                new ObjectParameter("cat_id", typeof(int));
    
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            var cust_idParameter = cust_id.HasValue ?
                new ObjectParameter("cust_id", cust_id) :
                new ObjectParameter("cust_id", typeof(int));
    
            var sale_rep_idParameter = sale_rep_id.HasValue ?
                new ObjectParameter("sale_rep_id", sale_rep_id) :
                new ObjectParameter("sale_rep_id", typeof(int));
    
            var datefromParameter = datefrom.HasValue ?
                new ObjectParameter("datefrom", datefrom) :
                new ObjectParameter("datefrom", typeof(System.DateTime));
    
            var datetoParameter = dateto.HasValue ?
                new ObjectParameter("dateto", dateto) :
                new ObjectParameter("dateto", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTPurchaseAndSaleQuantityOfProduct_Result>("RPTPurchaseAndSaleQuantityOfProduct", cat_idParameter, pro_idParameter, cust_idParameter, sale_rep_idParameter, datefromParameter, datetoParameter);
        }
    
        public virtual ObjectResult<RPTPurchaseInvoiceMovement_Result> RPTPurchaseInvoiceMovement(Nullable<int> kind, Nullable<System.DateTime> datefrom, Nullable<System.DateTime> dateto, Nullable<int> id, Nullable<int> no, Nullable<int> cust_id, Nullable<int> sale_rep_id, Nullable<int> pay_Kind)
        {
            var kindParameter = kind.HasValue ?
                new ObjectParameter("kind", kind) :
                new ObjectParameter("kind", typeof(int));
    
            var datefromParameter = datefrom.HasValue ?
                new ObjectParameter("datefrom", datefrom) :
                new ObjectParameter("datefrom", typeof(System.DateTime));
    
            var datetoParameter = dateto.HasValue ?
                new ObjectParameter("dateto", dateto) :
                new ObjectParameter("dateto", typeof(System.DateTime));
    
            var idParameter = id.HasValue ?
                new ObjectParameter("id", id) :
                new ObjectParameter("id", typeof(int));
    
            var noParameter = no.HasValue ?
                new ObjectParameter("no", no) :
                new ObjectParameter("no", typeof(int));
    
            var cust_idParameter = cust_id.HasValue ?
                new ObjectParameter("cust_id", cust_id) :
                new ObjectParameter("cust_id", typeof(int));
    
            var sale_rep_idParameter = sale_rep_id.HasValue ?
                new ObjectParameter("sale_rep_id", sale_rep_id) :
                new ObjectParameter("sale_rep_id", typeof(int));
    
            var pay_KindParameter = pay_Kind.HasValue ?
                new ObjectParameter("pay_Kind", pay_Kind) :
                new ObjectParameter("pay_Kind", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTPurchaseInvoiceMovement_Result>("RPTPurchaseInvoiceMovement", kindParameter, datefromParameter, datetoParameter, idParameter, noParameter, cust_idParameter, sale_rep_idParameter, pay_KindParameter);
        }
    
        public virtual ObjectResult<RPTPurchaseQuantityOfProduct_Result> RPTPurchaseQuantityOfProduct(Nullable<int> cat_id, Nullable<long> pro_id, Nullable<int> cust_id, Nullable<int> sale_rep_id, Nullable<System.DateTime> datefrom, Nullable<System.DateTime> dateto)
        {
            var cat_idParameter = cat_id.HasValue ?
                new ObjectParameter("cat_id", cat_id) :
                new ObjectParameter("cat_id", typeof(int));
    
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            var cust_idParameter = cust_id.HasValue ?
                new ObjectParameter("cust_id", cust_id) :
                new ObjectParameter("cust_id", typeof(int));
    
            var sale_rep_idParameter = sale_rep_id.HasValue ?
                new ObjectParameter("sale_rep_id", sale_rep_id) :
                new ObjectParameter("sale_rep_id", typeof(int));
    
            var datefromParameter = datefrom.HasValue ?
                new ObjectParameter("datefrom", datefrom) :
                new ObjectParameter("datefrom", typeof(System.DateTime));
    
            var datetoParameter = dateto.HasValue ?
                new ObjectParameter("dateto", dateto) :
                new ObjectParameter("dateto", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTPurchaseQuantityOfProduct_Result>("RPTPurchaseQuantityOfProduct", cat_idParameter, pro_idParameter, cust_idParameter, sale_rep_idParameter, datefromParameter, datetoParameter);
        }
    
        public virtual ObjectResult<RPTRequiredInstallments_Result> RPTRequiredInstallments(Nullable<int> alertDate)
        {
            var alertDateParameter = alertDate.HasValue ?
                new ObjectParameter("AlertDate", alertDate) :
                new ObjectParameter("AlertDate", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTRequiredInstallments_Result>("RPTRequiredInstallments", alertDateParameter);
        }
    
        public virtual ObjectResult<RPTSalesInvoiceMovement_Result> RPTSalesInvoiceMovement(Nullable<int> kind, Nullable<System.DateTime> datefrom, Nullable<System.DateTime> dateto, Nullable<int> id, Nullable<int> no, Nullable<int> cust_id, Nullable<int> pay_Kind, Nullable<int> treasury_id)
        {
            var kindParameter = kind.HasValue ?
                new ObjectParameter("kind", kind) :
                new ObjectParameter("kind", typeof(int));
    
            var datefromParameter = datefrom.HasValue ?
                new ObjectParameter("datefrom", datefrom) :
                new ObjectParameter("datefrom", typeof(System.DateTime));
    
            var datetoParameter = dateto.HasValue ?
                new ObjectParameter("dateto", dateto) :
                new ObjectParameter("dateto", typeof(System.DateTime));
    
            var idParameter = id.HasValue ?
                new ObjectParameter("id", id) :
                new ObjectParameter("id", typeof(int));
    
            var noParameter = no.HasValue ?
                new ObjectParameter("no", no) :
                new ObjectParameter("no", typeof(int));
    
            var cust_idParameter = cust_id.HasValue ?
                new ObjectParameter("cust_id", cust_id) :
                new ObjectParameter("cust_id", typeof(int));
    
            var pay_KindParameter = pay_Kind.HasValue ?
                new ObjectParameter("pay_Kind", pay_Kind) :
                new ObjectParameter("pay_Kind", typeof(int));
    
            var treasury_idParameter = treasury_id.HasValue ?
                new ObjectParameter("treasury_id", treasury_id) :
                new ObjectParameter("treasury_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTSalesInvoiceMovement_Result>("RPTSalesInvoiceMovement", kindParameter, datefromParameter, datetoParameter, idParameter, noParameter, cust_idParameter, pay_KindParameter, treasury_idParameter);
        }
    
        public virtual ObjectResult<RPTSalesOfProduct_Result> RPTSalesOfProduct(Nullable<int> cat_id, Nullable<long> pro_id, Nullable<int> cust_id, Nullable<int> sale_rep_id, Nullable<int> treas_id, Nullable<System.DateTime> datefrom, Nullable<System.DateTime> dateto, string user_name)
        {
            var cat_idParameter = cat_id.HasValue ?
                new ObjectParameter("cat_id", cat_id) :
                new ObjectParameter("cat_id", typeof(int));
    
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            var cust_idParameter = cust_id.HasValue ?
                new ObjectParameter("cust_id", cust_id) :
                new ObjectParameter("cust_id", typeof(int));
    
            var sale_rep_idParameter = sale_rep_id.HasValue ?
                new ObjectParameter("sale_rep_id", sale_rep_id) :
                new ObjectParameter("sale_rep_id", typeof(int));
    
            var treas_idParameter = treas_id.HasValue ?
                new ObjectParameter("treas_id", treas_id) :
                new ObjectParameter("treas_id", typeof(int));
    
            var datefromParameter = datefrom.HasValue ?
                new ObjectParameter("datefrom", datefrom) :
                new ObjectParameter("datefrom", typeof(System.DateTime));
    
            var datetoParameter = dateto.HasValue ?
                new ObjectParameter("dateto", dateto) :
                new ObjectParameter("dateto", typeof(System.DateTime));
    
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTSalesOfProduct_Result>("RPTSalesOfProduct", cat_idParameter, pro_idParameter, cust_idParameter, sale_rep_idParameter, treas_idParameter, datefromParameter, datetoParameter, user_nameParameter);
        }
    
        public virtual ObjectResult<RPTSalesOfSalesRepresentative_Result> RPTSalesOfSalesRepresentative(Nullable<int> stock_id, Nullable<int> cat_id, Nullable<long> pro_id, Nullable<int> cust_id, Nullable<int> sale_rep_id, Nullable<int> treas_id, Nullable<System.DateTime> datefrom, Nullable<System.DateTime> dateto)
        {
            var stock_idParameter = stock_id.HasValue ?
                new ObjectParameter("stock_id", stock_id) :
                new ObjectParameter("stock_id", typeof(int));
    
            var cat_idParameter = cat_id.HasValue ?
                new ObjectParameter("cat_id", cat_id) :
                new ObjectParameter("cat_id", typeof(int));
    
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            var cust_idParameter = cust_id.HasValue ?
                new ObjectParameter("cust_id", cust_id) :
                new ObjectParameter("cust_id", typeof(int));
    
            var sale_rep_idParameter = sale_rep_id.HasValue ?
                new ObjectParameter("sale_rep_id", sale_rep_id) :
                new ObjectParameter("sale_rep_id", typeof(int));
    
            var treas_idParameter = treas_id.HasValue ?
                new ObjectParameter("treas_id", treas_id) :
                new ObjectParameter("treas_id", typeof(int));
    
            var datefromParameter = datefrom.HasValue ?
                new ObjectParameter("datefrom", datefrom) :
                new ObjectParameter("datefrom", typeof(System.DateTime));
    
            var datetoParameter = dateto.HasValue ?
                new ObjectParameter("dateto", dateto) :
                new ObjectParameter("dateto", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTSalesOfSalesRepresentative_Result>("RPTSalesOfSalesRepresentative", stock_idParameter, cat_idParameter, pro_idParameter, cust_idParameter, sale_rep_idParameter, treas_idParameter, datefromParameter, datetoParameter);
        }
    
        public virtual ObjectResult<RPTSalesOfSalesRepresentativeByCat_Result> RPTSalesOfSalesRepresentativeByCat(Nullable<int> sale_rep_id, Nullable<int> cat_id, Nullable<int> stock_id, Nullable<int> cust_id, Nullable<System.DateTime> datefrom, Nullable<System.DateTime> dateto)
        {
            var sale_rep_idParameter = sale_rep_id.HasValue ?
                new ObjectParameter("sale_rep_id", sale_rep_id) :
                new ObjectParameter("sale_rep_id", typeof(int));
    
            var cat_idParameter = cat_id.HasValue ?
                new ObjectParameter("cat_id", cat_id) :
                new ObjectParameter("cat_id", typeof(int));
    
            var stock_idParameter = stock_id.HasValue ?
                new ObjectParameter("stock_id", stock_id) :
                new ObjectParameter("stock_id", typeof(int));
    
            var cust_idParameter = cust_id.HasValue ?
                new ObjectParameter("cust_id", cust_id) :
                new ObjectParameter("cust_id", typeof(int));
    
            var datefromParameter = datefrom.HasValue ?
                new ObjectParameter("datefrom", datefrom) :
                new ObjectParameter("datefrom", typeof(System.DateTime));
    
            var datetoParameter = dateto.HasValue ?
                new ObjectParameter("dateto", dateto) :
                new ObjectParameter("dateto", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTSalesOfSalesRepresentativeByCat_Result>("RPTSalesOfSalesRepresentativeByCat", sale_rep_idParameter, cat_idParameter, stock_idParameter, cust_idParameter, datefromParameter, datetoParameter);
        }
    
        public virtual ObjectResult<RPTSalesOfSalesRepresentativeQuantity_Result> RPTSalesOfSalesRepresentativeQuantity(Nullable<int> stock_id, Nullable<long> pro_id, Nullable<int> cust_id, Nullable<int> sale_rep_id, Nullable<System.DateTime> datefrom, Nullable<System.DateTime> dateto)
        {
            var stock_idParameter = stock_id.HasValue ?
                new ObjectParameter("stock_id", stock_id) :
                new ObjectParameter("stock_id", typeof(int));
    
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            var cust_idParameter = cust_id.HasValue ?
                new ObjectParameter("cust_id", cust_id) :
                new ObjectParameter("cust_id", typeof(int));
    
            var sale_rep_idParameter = sale_rep_id.HasValue ?
                new ObjectParameter("sale_rep_id", sale_rep_id) :
                new ObjectParameter("sale_rep_id", typeof(int));
    
            var datefromParameter = datefrom.HasValue ?
                new ObjectParameter("datefrom", datefrom) :
                new ObjectParameter("datefrom", typeof(System.DateTime));
    
            var datetoParameter = dateto.HasValue ?
                new ObjectParameter("dateto", dateto) :
                new ObjectParameter("dateto", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTSalesOfSalesRepresentativeQuantity_Result>("RPTSalesOfSalesRepresentativeQuantity", stock_idParameter, pro_idParameter, cust_idParameter, sale_rep_idParameter, datefromParameter, datetoParameter);
        }
    
        public virtual ObjectResult<RPTSalesQuantityOfProduct_Result> RPTSalesQuantityOfProduct(Nullable<int> cat_id, Nullable<long> pro_id, Nullable<int> cust_id, Nullable<int> sale_rep_id, Nullable<System.DateTime> datefrom, Nullable<System.DateTime> dateto)
        {
            var cat_idParameter = cat_id.HasValue ?
                new ObjectParameter("cat_id", cat_id) :
                new ObjectParameter("cat_id", typeof(int));
    
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            var cust_idParameter = cust_id.HasValue ?
                new ObjectParameter("cust_id", cust_id) :
                new ObjectParameter("cust_id", typeof(int));
    
            var sale_rep_idParameter = sale_rep_id.HasValue ?
                new ObjectParameter("sale_rep_id", sale_rep_id) :
                new ObjectParameter("sale_rep_id", typeof(int));
    
            var datefromParameter = datefrom.HasValue ?
                new ObjectParameter("datefrom", datefrom) :
                new ObjectParameter("datefrom", typeof(System.DateTime));
    
            var datetoParameter = dateto.HasValue ?
                new ObjectParameter("dateto", dateto) :
                new ObjectParameter("dateto", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTSalesQuantityOfProduct_Result>("RPTSalesQuantityOfProduct", cat_idParameter, pro_idParameter, cust_idParameter, sale_rep_idParameter, datefromParameter, datetoParameter);
        }
    
        public virtual ObjectResult<RPTSalesRepSalesOfCat_Result> RPTSalesRepSalesOfCat(Nullable<int> sale_rep_id, Nullable<int> cat_id, Nullable<int> stock_id, Nullable<int> cust_id, Nullable<System.DateTime> datefrom, Nullable<System.DateTime> dateto)
        {
            var sale_rep_idParameter = sale_rep_id.HasValue ?
                new ObjectParameter("sale_rep_id", sale_rep_id) :
                new ObjectParameter("sale_rep_id", typeof(int));
    
            var cat_idParameter = cat_id.HasValue ?
                new ObjectParameter("cat_id", cat_id) :
                new ObjectParameter("cat_id", typeof(int));
    
            var stock_idParameter = stock_id.HasValue ?
                new ObjectParameter("stock_id", stock_id) :
                new ObjectParameter("stock_id", typeof(int));
    
            var cust_idParameter = cust_id.HasValue ?
                new ObjectParameter("cust_id", cust_id) :
                new ObjectParameter("cust_id", typeof(int));
    
            var datefromParameter = datefrom.HasValue ?
                new ObjectParameter("datefrom", datefrom) :
                new ObjectParameter("datefrom", typeof(System.DateTime));
    
            var datetoParameter = dateto.HasValue ?
                new ObjectParameter("dateto", dateto) :
                new ObjectParameter("dateto", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTSalesRepSalesOfCat_Result>("RPTSalesRepSalesOfCat", sale_rep_idParameter, cat_idParameter, stock_idParameter, cust_idParameter, datefromParameter, datetoParameter);
        }
    
        public virtual ObjectResult<RptSalesTotals_Result> RptSalesTotals(string user_name, Nullable<int> treasury_id, Nullable<System.DateTime> start_date, Nullable<System.DateTime> end_date)
        {
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            var treasury_idParameter = treasury_id.HasValue ?
                new ObjectParameter("treasury_id", treasury_id) :
                new ObjectParameter("treasury_id", typeof(int));
    
            var start_dateParameter = start_date.HasValue ?
                new ObjectParameter("start_date", start_date) :
                new ObjectParameter("start_date", typeof(System.DateTime));
    
            var end_dateParameter = end_date.HasValue ?
                new ObjectParameter("end_date", end_date) :
                new ObjectParameter("end_date", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RptSalesTotals_Result>("RptSalesTotals", user_nameParameter, treasury_idParameter, start_dateParameter, end_dateParameter);
        }
    
        public virtual ObjectResult<RPTStockPermissionPrint_Result> RPTStockPermissionPrint(Nullable<int> per_id)
        {
            var per_idParameter = per_id.HasValue ?
                new ObjectParameter("per_id", per_id) :
                new ObjectParameter("per_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTStockPermissionPrint_Result>("RPTStockPermissionPrint", per_idParameter);
        }
    
        public virtual ObjectResult<RPTUserWorkShiftPrint_Result> RPTUserWorkShiftPrint(Nullable<int> shift_id)
        {
            var shift_idParameter = shift_id.HasValue ?
                new ObjectParameter("shift_id", shift_id) :
                new ObjectParameter("shift_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RPTUserWorkShiftPrint_Result>("RPTUserWorkShiftPrint", shift_idParameter);
        }
    
        public virtual ObjectResult<SearchBankActions_Result> SearchBankActions(Nullable<int> action_type, string receipt_no, Nullable<int> account_id, Nullable<System.DateTime> datefrom, Nullable<System.DateTime> dateto)
        {
            var action_typeParameter = action_type.HasValue ?
                new ObjectParameter("action_type", action_type) :
                new ObjectParameter("action_type", typeof(int));
    
            var receipt_noParameter = receipt_no != null ?
                new ObjectParameter("receipt_no", receipt_no) :
                new ObjectParameter("receipt_no", typeof(string));
    
            var account_idParameter = account_id.HasValue ?
                new ObjectParameter("account_id", account_id) :
                new ObjectParameter("account_id", typeof(int));
    
            var datefromParameter = datefrom.HasValue ?
                new ObjectParameter("datefrom", datefrom) :
                new ObjectParameter("datefrom", typeof(System.DateTime));
    
            var datetoParameter = dateto.HasValue ?
                new ObjectParameter("dateto", dateto) :
                new ObjectParameter("dateto", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<SearchBankActions_Result>("SearchBankActions", action_typeParameter, receipt_noParameter, account_idParameter, datefromParameter, datetoParameter);
        }
    
        public virtual ObjectResult<searchCategory_Result> searchCategory(string cat_name)
        {
            var cat_nameParameter = cat_name != null ?
                new ObjectParameter("cat_name", cat_name) :
                new ObjectParameter("cat_name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<searchCategory_Result>("searchCategory", cat_nameParameter);
        }
    
        public virtual ObjectResult<SearchCustomerOrVendors_Result> SearchCustomerOrVendors(string cust_name, Nullable<int> sales_rep_id, string cust_kind, string city, string phone, string mobile_no, string address)
        {
            var cust_nameParameter = cust_name != null ?
                new ObjectParameter("cust_name", cust_name) :
                new ObjectParameter("cust_name", typeof(string));
    
            var sales_rep_idParameter = sales_rep_id.HasValue ?
                new ObjectParameter("sales_rep_id", sales_rep_id) :
                new ObjectParameter("sales_rep_id", typeof(int));
    
            var cust_kindParameter = cust_kind != null ?
                new ObjectParameter("cust_kind", cust_kind) :
                new ObjectParameter("cust_kind", typeof(string));
    
            var cityParameter = city != null ?
                new ObjectParameter("city", city) :
                new ObjectParameter("city", typeof(string));
    
            var phoneParameter = phone != null ?
                new ObjectParameter("phone", phone) :
                new ObjectParameter("phone", typeof(string));
    
            var mobile_noParameter = mobile_no != null ?
                new ObjectParameter("mobile_no", mobile_no) :
                new ObjectParameter("mobile_no", typeof(string));
    
            var addressParameter = address != null ?
                new ObjectParameter("address", address) :
                new ObjectParameter("address", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<SearchCustomerOrVendors_Result>("SearchCustomerOrVendors", cust_nameParameter, sales_rep_idParameter, cust_kindParameter, cityParameter, phoneParameter, mobile_noParameter, addressParameter);
        }
    
        public virtual ObjectResult<SearchInProducts_Result> SearchInProducts(string pro_name, Nullable<long> pro_id, string pro_id2, Nullable<int> cat_id)
        {
            var pro_nameParameter = pro_name != null ?
                new ObjectParameter("pro_name", pro_name) :
                new ObjectParameter("pro_name", typeof(string));
    
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            var pro_id2Parameter = pro_id2 != null ?
                new ObjectParameter("pro_id2", pro_id2) :
                new ObjectParameter("pro_id2", typeof(string));
    
            var cat_idParameter = cat_id.HasValue ?
                new ObjectParameter("cat_id", cat_id) :
                new ObjectParameter("cat_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<SearchInProducts_Result>("SearchInProducts", pro_nameParameter, pro_idParameter, pro_id2Parameter, cat_idParameter);
        }
    
        public virtual ObjectResult<SearchInstallments_Result> SearchInstallments(string cust_name, Nullable<System.DateTime> date_from, Nullable<System.DateTime> date_to)
        {
            var cust_nameParameter = cust_name != null ?
                new ObjectParameter("cust_name", cust_name) :
                new ObjectParameter("cust_name", typeof(string));
    
            var date_fromParameter = date_from.HasValue ?
                new ObjectParameter("date_from", date_from) :
                new ObjectParameter("date_from", typeof(System.DateTime));
    
            var date_toParameter = date_to.HasValue ?
                new ObjectParameter("date_to", date_to) :
                new ObjectParameter("date_to", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<SearchInstallments_Result>("SearchInstallments", cust_nameParameter, date_fromParameter, date_toParameter);
        }
    
        public virtual ObjectResult<SearchInventoryPermission_Result> SearchInventoryPermission(Nullable<int> per_kind, Nullable<int> per_id, Nullable<int> per_no, Nullable<int> stock_id, Nullable<int> stock_id_from, Nullable<int> stock_id_to, Nullable<System.DateTime> datefrom, Nullable<System.DateTime> dateto)
        {
            var per_kindParameter = per_kind.HasValue ?
                new ObjectParameter("per_kind", per_kind) :
                new ObjectParameter("per_kind", typeof(int));
    
            var per_idParameter = per_id.HasValue ?
                new ObjectParameter("per_id", per_id) :
                new ObjectParameter("per_id", typeof(int));
    
            var per_noParameter = per_no.HasValue ?
                new ObjectParameter("per_no", per_no) :
                new ObjectParameter("per_no", typeof(int));
    
            var stock_idParameter = stock_id.HasValue ?
                new ObjectParameter("stock_id", stock_id) :
                new ObjectParameter("stock_id", typeof(int));
    
            var stock_id_fromParameter = stock_id_from.HasValue ?
                new ObjectParameter("stock_id_from", stock_id_from) :
                new ObjectParameter("stock_id_from", typeof(int));
    
            var stock_id_toParameter = stock_id_to.HasValue ?
                new ObjectParameter("stock_id_to", stock_id_to) :
                new ObjectParameter("stock_id_to", typeof(int));
    
            var datefromParameter = datefrom.HasValue ?
                new ObjectParameter("datefrom", datefrom) :
                new ObjectParameter("datefrom", typeof(System.DateTime));
    
            var datetoParameter = dateto.HasValue ?
                new ObjectParameter("dateto", dateto) :
                new ObjectParameter("dateto", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<SearchInventoryPermission_Result>("SearchInventoryPermission", per_kindParameter, per_idParameter, per_noParameter, stock_idParameter, stock_id_fromParameter, stock_id_toParameter, datefromParameter, datetoParameter);
        }
    
        public virtual ObjectResult<SearchInventoryPermissionRelocate_Result> SearchInventoryPermissionRelocate(Nullable<int> per_kind, Nullable<int> per_id, Nullable<int> per_no, Nullable<int> stock_id, Nullable<int> stock_id_from, Nullable<int> stock_id_to, Nullable<System.DateTime> datefrom, Nullable<System.DateTime> dateto)
        {
            var per_kindParameter = per_kind.HasValue ?
                new ObjectParameter("per_kind", per_kind) :
                new ObjectParameter("per_kind", typeof(int));
    
            var per_idParameter = per_id.HasValue ?
                new ObjectParameter("per_id", per_id) :
                new ObjectParameter("per_id", typeof(int));
    
            var per_noParameter = per_no.HasValue ?
                new ObjectParameter("per_no", per_no) :
                new ObjectParameter("per_no", typeof(int));
    
            var stock_idParameter = stock_id.HasValue ?
                new ObjectParameter("stock_id", stock_id) :
                new ObjectParameter("stock_id", typeof(int));
    
            var stock_id_fromParameter = stock_id_from.HasValue ?
                new ObjectParameter("stock_id_from", stock_id_from) :
                new ObjectParameter("stock_id_from", typeof(int));
    
            var stock_id_toParameter = stock_id_to.HasValue ?
                new ObjectParameter("stock_id_to", stock_id_to) :
                new ObjectParameter("stock_id_to", typeof(int));
    
            var datefromParameter = datefrom.HasValue ?
                new ObjectParameter("datefrom", datefrom) :
                new ObjectParameter("datefrom", typeof(System.DateTime));
    
            var datetoParameter = dateto.HasValue ?
                new ObjectParameter("dateto", dateto) :
                new ObjectParameter("dateto", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<SearchInventoryPermissionRelocate_Result>("SearchInventoryPermissionRelocate", per_kindParameter, per_idParameter, per_noParameter, stock_idParameter, stock_id_fromParameter, stock_id_toParameter, datefromParameter, datetoParameter);
        }
    
        public virtual ObjectResult<searchinvoices_Result> searchinvoices(Nullable<int> kind, Nullable<int> cost_center_id, string user_name, Nullable<System.DateTime> datefrom, Nullable<System.DateTime> dateto, Nullable<int> id, Nullable<int> no, Nullable<int> cust_id, Nullable<int> treasury_id)
        {
            var kindParameter = kind.HasValue ?
                new ObjectParameter("kind", kind) :
                new ObjectParameter("kind", typeof(int));
    
            var cost_center_idParameter = cost_center_id.HasValue ?
                new ObjectParameter("cost_center_id", cost_center_id) :
                new ObjectParameter("cost_center_id", typeof(int));
    
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            var datefromParameter = datefrom.HasValue ?
                new ObjectParameter("datefrom", datefrom) :
                new ObjectParameter("datefrom", typeof(System.DateTime));
    
            var datetoParameter = dateto.HasValue ?
                new ObjectParameter("dateto", dateto) :
                new ObjectParameter("dateto", typeof(System.DateTime));
    
            var idParameter = id.HasValue ?
                new ObjectParameter("id", id) :
                new ObjectParameter("id", typeof(int));
    
            var noParameter = no.HasValue ?
                new ObjectParameter("no", no) :
                new ObjectParameter("no", typeof(int));
    
            var cust_idParameter = cust_id.HasValue ?
                new ObjectParameter("cust_id", cust_id) :
                new ObjectParameter("cust_id", typeof(int));
    
            var treasury_idParameter = treasury_id.HasValue ?
                new ObjectParameter("treasury_id", treasury_id) :
                new ObjectParameter("treasury_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<searchinvoices_Result>("searchinvoices", kindParameter, cost_center_idParameter, user_nameParameter, datefromParameter, datetoParameter, idParameter, noParameter, cust_idParameter, treasury_idParameter);
        }
    
        public virtual ObjectResult<SearchMaintenance_Result> SearchMaintenance(Nullable<int> maintenance_id, Nullable<int> cost_center_id, string customer_name, string mobile, string area, string item_cat, string item, Nullable<int> visit_eng, Nullable<int> maintenance_eng, Nullable<int> maintenance_workshop, Nullable<bool> is_fixed, Nullable<bool> is_paid, Nullable<int> maintenance_status, Nullable<System.DateTime> com_date, Nullable<System.DateTime> go_date, Nullable<System.DateTime> draw_date, Nullable<System.DateTime> delivery_date)
        {
            var maintenance_idParameter = maintenance_id.HasValue ?
                new ObjectParameter("maintenance_id", maintenance_id) :
                new ObjectParameter("maintenance_id", typeof(int));
    
            var cost_center_idParameter = cost_center_id.HasValue ?
                new ObjectParameter("cost_center_id", cost_center_id) :
                new ObjectParameter("cost_center_id", typeof(int));
    
            var customer_nameParameter = customer_name != null ?
                new ObjectParameter("customer_name", customer_name) :
                new ObjectParameter("customer_name", typeof(string));
    
            var mobileParameter = mobile != null ?
                new ObjectParameter("mobile", mobile) :
                new ObjectParameter("mobile", typeof(string));
    
            var areaParameter = area != null ?
                new ObjectParameter("area", area) :
                new ObjectParameter("area", typeof(string));
    
            var item_catParameter = item_cat != null ?
                new ObjectParameter("item_cat", item_cat) :
                new ObjectParameter("item_cat", typeof(string));
    
            var itemParameter = item != null ?
                new ObjectParameter("item", item) :
                new ObjectParameter("item", typeof(string));
    
            var visit_engParameter = visit_eng.HasValue ?
                new ObjectParameter("visit_eng", visit_eng) :
                new ObjectParameter("visit_eng", typeof(int));
    
            var maintenance_engParameter = maintenance_eng.HasValue ?
                new ObjectParameter("maintenance_eng", maintenance_eng) :
                new ObjectParameter("maintenance_eng", typeof(int));
    
            var maintenance_workshopParameter = maintenance_workshop.HasValue ?
                new ObjectParameter("maintenance_workshop", maintenance_workshop) :
                new ObjectParameter("maintenance_workshop", typeof(int));
    
            var is_fixedParameter = is_fixed.HasValue ?
                new ObjectParameter("is_fixed", is_fixed) :
                new ObjectParameter("is_fixed", typeof(bool));
    
            var is_paidParameter = is_paid.HasValue ?
                new ObjectParameter("is_paid", is_paid) :
                new ObjectParameter("is_paid", typeof(bool));
    
            var maintenance_statusParameter = maintenance_status.HasValue ?
                new ObjectParameter("maintenance_status", maintenance_status) :
                new ObjectParameter("maintenance_status", typeof(int));
    
            var com_dateParameter = com_date.HasValue ?
                new ObjectParameter("com_date", com_date) :
                new ObjectParameter("com_date", typeof(System.DateTime));
    
            var go_dateParameter = go_date.HasValue ?
                new ObjectParameter("go_date", go_date) :
                new ObjectParameter("go_date", typeof(System.DateTime));
    
            var draw_dateParameter = draw_date.HasValue ?
                new ObjectParameter("draw_date", draw_date) :
                new ObjectParameter("draw_date", typeof(System.DateTime));
    
            var delivery_dateParameter = delivery_date.HasValue ?
                new ObjectParameter("delivery_date", delivery_date) :
                new ObjectParameter("delivery_date", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<SearchMaintenance_Result>("SearchMaintenance", maintenance_idParameter, cost_center_idParameter, customer_nameParameter, mobileParameter, areaParameter, item_catParameter, itemParameter, visit_engParameter, maintenance_engParameter, maintenance_workshopParameter, is_fixedParameter, is_paidParameter, maintenance_statusParameter, com_dateParameter, go_dateParameter, draw_dateParameter, delivery_dateParameter);
        }
    
        public virtual ObjectResult<SearchMaintenanceEngineer_Result> SearchMaintenanceEngineer(string eng_name)
        {
            var eng_nameParameter = eng_name != null ?
                new ObjectParameter("eng_name", eng_name) :
                new ObjectParameter("eng_name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<SearchMaintenanceEngineer_Result>("SearchMaintenanceEngineer", eng_nameParameter);
        }
    
        public virtual ObjectResult<SearchMaintenanceWorkshop_Result> SearchMaintenanceWorkshop(string workshop_name)
        {
            var workshop_nameParameter = workshop_name != null ?
                new ObjectParameter("workshop_name", workshop_name) :
                new ObjectParameter("workshop_name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<SearchMaintenanceWorkshop_Result>("SearchMaintenanceWorkshop", workshop_nameParameter);
        }
    
        public virtual ObjectResult<SearchManfactureOrders_Result> SearchManfactureOrders(Nullable<int> manufacture_order_id, Nullable<int> manufacture_order_type_id, Nullable<System.DateTime> datefrom, Nullable<System.DateTime> dateto)
        {
            var manufacture_order_idParameter = manufacture_order_id.HasValue ?
                new ObjectParameter("manufacture_order_id", manufacture_order_id) :
                new ObjectParameter("manufacture_order_id", typeof(int));
    
            var manufacture_order_type_idParameter = manufacture_order_type_id.HasValue ?
                new ObjectParameter("manufacture_order_type_id", manufacture_order_type_id) :
                new ObjectParameter("manufacture_order_type_id", typeof(int));
    
            var datefromParameter = datefrom.HasValue ?
                new ObjectParameter("datefrom", datefrom) :
                new ObjectParameter("datefrom", typeof(System.DateTime));
    
            var datetoParameter = dateto.HasValue ?
                new ObjectParameter("dateto", dateto) :
                new ObjectParameter("dateto", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<SearchManfactureOrders_Result>("SearchManfactureOrders", manufacture_order_idParameter, manufacture_order_type_idParameter, datefromParameter, datetoParameter);
        }
    
        public virtual ObjectResult<SearchPapers_Result> SearchPapers(string paper_no, Nullable<int> cust_id, Nullable<int> paper_type, Nullable<int> bank_id, Nullable<int> paper_status)
        {
            var paper_noParameter = paper_no != null ?
                new ObjectParameter("paper_no", paper_no) :
                new ObjectParameter("paper_no", typeof(string));
    
            var cust_idParameter = cust_id.HasValue ?
                new ObjectParameter("cust_id", cust_id) :
                new ObjectParameter("cust_id", typeof(int));
    
            var paper_typeParameter = paper_type.HasValue ?
                new ObjectParameter("paper_type", paper_type) :
                new ObjectParameter("paper_type", typeof(int));
    
            var bank_idParameter = bank_id.HasValue ?
                new ObjectParameter("bank_id", bank_id) :
                new ObjectParameter("bank_id", typeof(int));
    
            var paper_statusParameter = paper_status.HasValue ?
                new ObjectParameter("paper_status", paper_status) :
                new ObjectParameter("paper_status", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<SearchPapers_Result>("SearchPapers", paper_noParameter, cust_idParameter, paper_typeParameter, bank_idParameter, paper_statusParameter);
        }
    
        public virtual ObjectResult<SearchProductInInvocie_Result> SearchProductInInvocie(string pro_name, Nullable<long> pro_id, string pro_id2, Nullable<int> cat_id, Nullable<int> stock_id)
        {
            var pro_nameParameter = pro_name != null ?
                new ObjectParameter("pro_name", pro_name) :
                new ObjectParameter("pro_name", typeof(string));
    
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            var pro_id2Parameter = pro_id2 != null ?
                new ObjectParameter("pro_id2", pro_id2) :
                new ObjectParameter("pro_id2", typeof(string));
    
            var cat_idParameter = cat_id.HasValue ?
                new ObjectParameter("cat_id", cat_id) :
                new ObjectParameter("cat_id", typeof(int));
    
            var stock_idParameter = stock_id.HasValue ?
                new ObjectParameter("stock_id", stock_id) :
                new ObjectParameter("stock_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<SearchProductInInvocie_Result>("SearchProductInInvocie", pro_nameParameter, pro_idParameter, pro_id2Parameter, cat_idParameter, stock_idParameter);
        }
    
        public virtual ObjectResult<SearchProductsInInvoiceDetails_Result> SearchProductsInInvoiceDetails(Nullable<long> inv_id, string pro_name)
        {
            var inv_idParameter = inv_id.HasValue ?
                new ObjectParameter("inv_id", inv_id) :
                new ObjectParameter("inv_id", typeof(long));
    
            var pro_nameParameter = pro_name != null ?
                new ObjectParameter("pro_name", pro_name) :
                new ObjectParameter("pro_name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<SearchProductsInInvoiceDetails_Result>("SearchProductsInInvoiceDetails", inv_idParameter, pro_nameParameter);
        }
    
        public virtual ObjectResult<SearchRestaurantOrders_Result> SearchRestaurantOrders(Nullable<int> inv_kind, string user_name, Nullable<int> delivery_id, Nullable<int> inv_id, Nullable<decimal> paid, Nullable<int> order_status, Nullable<int> order_type, Nullable<System.DateTime> date_from, Nullable<System.DateTime> date_to)
        {
            var inv_kindParameter = inv_kind.HasValue ?
                new ObjectParameter("inv_kind", inv_kind) :
                new ObjectParameter("inv_kind", typeof(int));
    
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            var delivery_idParameter = delivery_id.HasValue ?
                new ObjectParameter("delivery_id", delivery_id) :
                new ObjectParameter("delivery_id", typeof(int));
    
            var inv_idParameter = inv_id.HasValue ?
                new ObjectParameter("inv_id", inv_id) :
                new ObjectParameter("inv_id", typeof(int));
    
            var paidParameter = paid.HasValue ?
                new ObjectParameter("paid", paid) :
                new ObjectParameter("paid", typeof(decimal));
    
            var order_statusParameter = order_status.HasValue ?
                new ObjectParameter("order_status", order_status) :
                new ObjectParameter("order_status", typeof(int));
    
            var order_typeParameter = order_type.HasValue ?
                new ObjectParameter("order_type", order_type) :
                new ObjectParameter("order_type", typeof(int));
    
            var date_fromParameter = date_from.HasValue ?
                new ObjectParameter("date_from", date_from) :
                new ObjectParameter("date_from", typeof(System.DateTime));
    
            var date_toParameter = date_to.HasValue ?
                new ObjectParameter("date_to", date_to) :
                new ObjectParameter("date_to", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<SearchRestaurantOrders_Result>("SearchRestaurantOrders", inv_kindParameter, user_nameParameter, delivery_idParameter, inv_idParameter, paidParameter, order_statusParameter, order_typeParameter, date_fromParameter, date_toParameter);
        }
    
        public virtual int SearchRevenues(Nullable<int> cost_center_id, string user_name, string revenue_name, Nullable<int> treas_id, Nullable<System.DateTime> datefrom, Nullable<System.DateTime> dateto)
        {
            var cost_center_idParameter = cost_center_id.HasValue ?
                new ObjectParameter("cost_center_id", cost_center_id) :
                new ObjectParameter("cost_center_id", typeof(int));
    
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            var revenue_nameParameter = revenue_name != null ?
                new ObjectParameter("revenue_name", revenue_name) :
                new ObjectParameter("revenue_name", typeof(string));
    
            var treas_idParameter = treas_id.HasValue ?
                new ObjectParameter("treas_id", treas_id) :
                new ObjectParameter("treas_id", typeof(int));
    
            var datefromParameter = datefrom.HasValue ?
                new ObjectParameter("datefrom", datefrom) :
                new ObjectParameter("datefrom", typeof(System.DateTime));
    
            var datetoParameter = dateto.HasValue ?
                new ObjectParameter("dateto", dateto) :
                new ObjectParameter("dateto", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SearchRevenues", cost_center_idParameter, user_nameParameter, revenue_nameParameter, treas_idParameter, datefromParameter, datetoParameter);
        }
    
        public virtual ObjectResult<SearchSalesRepresentative_Result> SearchSalesRepresentative(string name)
        {
            var nameParameter = name != null ?
                new ObjectParameter("name", name) :
                new ObjectParameter("name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<SearchSalesRepresentative_Result>("SearchSalesRepresentative", nameParameter);
        }
    
        public virtual ObjectResult<SearchStock_Result> SearchStock(string stk_name)
        {
            var stk_nameParameter = stk_name != null ?
                new ObjectParameter("stk_name", stk_name) :
                new ObjectParameter("stk_name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<SearchStock_Result>("SearchStock", stk_nameParameter);
        }
    
        public virtual ObjectResult<SearchTreasuryExpenses_Result> SearchTreasuryExpenses(Nullable<int> per_kind, Nullable<int> cost_center_id, Nullable<int> expense_id, Nullable<int> treasury_id, string user_name, Nullable<System.DateTime> datefrom, Nullable<System.DateTime> dateto)
        {
            var per_kindParameter = per_kind.HasValue ?
                new ObjectParameter("per_kind", per_kind) :
                new ObjectParameter("per_kind", typeof(int));
    
            var cost_center_idParameter = cost_center_id.HasValue ?
                new ObjectParameter("cost_center_id", cost_center_id) :
                new ObjectParameter("cost_center_id", typeof(int));
    
            var expense_idParameter = expense_id.HasValue ?
                new ObjectParameter("expense_id", expense_id) :
                new ObjectParameter("expense_id", typeof(int));
    
            var treasury_idParameter = treasury_id.HasValue ?
                new ObjectParameter("treasury_id", treasury_id) :
                new ObjectParameter("treasury_id", typeof(int));
    
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            var datefromParameter = datefrom.HasValue ?
                new ObjectParameter("datefrom", datefrom) :
                new ObjectParameter("datefrom", typeof(System.DateTime));
    
            var datetoParameter = dateto.HasValue ?
                new ObjectParameter("dateto", dateto) :
                new ObjectParameter("dateto", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<SearchTreasuryExpenses_Result>("SearchTreasuryExpenses", per_kindParameter, cost_center_idParameter, expense_idParameter, treasury_idParameter, user_nameParameter, datefromParameter, datetoParameter);
        }
    
        public virtual ObjectResult<SearchTreasuryPermissionByKind_Result> SearchTreasuryPermissionByKind(Nullable<int> per_kind, Nullable<int> per_id, Nullable<int> per_no, Nullable<int> cust_id, Nullable<System.DateTime> datefrom, Nullable<System.DateTime> dateto)
        {
            var per_kindParameter = per_kind.HasValue ?
                new ObjectParameter("per_kind", per_kind) :
                new ObjectParameter("per_kind", typeof(int));
    
            var per_idParameter = per_id.HasValue ?
                new ObjectParameter("per_id", per_id) :
                new ObjectParameter("per_id", typeof(int));
    
            var per_noParameter = per_no.HasValue ?
                new ObjectParameter("per_no", per_no) :
                new ObjectParameter("per_no", typeof(int));
    
            var cust_idParameter = cust_id.HasValue ?
                new ObjectParameter("cust_id", cust_id) :
                new ObjectParameter("cust_id", typeof(int));
    
            var datefromParameter = datefrom.HasValue ?
                new ObjectParameter("datefrom", datefrom) :
                new ObjectParameter("datefrom", typeof(System.DateTime));
    
            var datetoParameter = dateto.HasValue ?
                new ObjectParameter("dateto", dateto) :
                new ObjectParameter("dateto", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<SearchTreasuryPermissionByKind_Result>("SearchTreasuryPermissionByKind", per_kindParameter, per_idParameter, per_noParameter, cust_idParameter, datefromParameter, datetoParameter);
        }
    
        public virtual ObjectResult<SearchTreasuryRevenue_Result> SearchTreasuryRevenue(Nullable<int> per_kind, Nullable<int> cost_center_id, Nullable<int> revenue_id, Nullable<int> treasury_id, string user_name, Nullable<System.DateTime> datefrom, Nullable<System.DateTime> dateto)
        {
            var per_kindParameter = per_kind.HasValue ?
                new ObjectParameter("per_kind", per_kind) :
                new ObjectParameter("per_kind", typeof(int));
    
            var cost_center_idParameter = cost_center_id.HasValue ?
                new ObjectParameter("cost_center_id", cost_center_id) :
                new ObjectParameter("cost_center_id", typeof(int));
    
            var revenue_idParameter = revenue_id.HasValue ?
                new ObjectParameter("revenue_id", revenue_id) :
                new ObjectParameter("revenue_id", typeof(int));
    
            var treasury_idParameter = treasury_id.HasValue ?
                new ObjectParameter("treasury_id", treasury_id) :
                new ObjectParameter("treasury_id", typeof(int));
    
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            var datefromParameter = datefrom.HasValue ?
                new ObjectParameter("datefrom", datefrom) :
                new ObjectParameter("datefrom", typeof(System.DateTime));
    
            var datetoParameter = dateto.HasValue ?
                new ObjectParameter("dateto", dateto) :
                new ObjectParameter("dateto", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<SearchTreasuryRevenue_Result>("SearchTreasuryRevenue", per_kindParameter, cost_center_idParameter, revenue_idParameter, treasury_idParameter, user_nameParameter, datefromParameter, datetoParameter);
        }
    
        public virtual ObjectResult<SearchTreasuryWithdrawAndDepositPermission_Result> SearchTreasuryWithdrawAndDepositPermission(Nullable<int> per_kind, Nullable<int> per_id, Nullable<int> per_no, Nullable<System.DateTime> datefrom, Nullable<System.DateTime> dateto)
        {
            var per_kindParameter = per_kind.HasValue ?
                new ObjectParameter("per_kind", per_kind) :
                new ObjectParameter("per_kind", typeof(int));
    
            var per_idParameter = per_id.HasValue ?
                new ObjectParameter("per_id", per_id) :
                new ObjectParameter("per_id", typeof(int));
    
            var per_noParameter = per_no.HasValue ?
                new ObjectParameter("per_no", per_no) :
                new ObjectParameter("per_no", typeof(int));
    
            var datefromParameter = datefrom.HasValue ?
                new ObjectParameter("datefrom", datefrom) :
                new ObjectParameter("datefrom", typeof(System.DateTime));
    
            var datetoParameter = dateto.HasValue ?
                new ObjectParameter("dateto", dateto) :
                new ObjectParameter("dateto", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<SearchTreasuryWithdrawAndDepositPermission_Result>("SearchTreasuryWithdrawAndDepositPermission", per_kindParameter, per_idParameter, per_noParameter, datefromParameter, datetoParameter);
        }
    
        public virtual ObjectResult<SearchUser_Result> SearchUser(string user_name)
        {
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<SearchUser_Result>("SearchUser", user_nameParameter);
        }
    
        public virtual ObjectResult<SearchUserLog_Result> SearchUserLog(string user_name, Nullable<int> action_type_id, Nullable<int> document_type_id, Nullable<System.DateTime> date_from, Nullable<System.DateTime> date_to, Nullable<bool> seen)
        {
            var user_nameParameter = user_name != null ?
                new ObjectParameter("user_name", user_name) :
                new ObjectParameter("user_name", typeof(string));
    
            var action_type_idParameter = action_type_id.HasValue ?
                new ObjectParameter("action_type_id", action_type_id) :
                new ObjectParameter("action_type_id", typeof(int));
    
            var document_type_idParameter = document_type_id.HasValue ?
                new ObjectParameter("document_type_id", document_type_id) :
                new ObjectParameter("document_type_id", typeof(int));
    
            var date_fromParameter = date_from.HasValue ?
                new ObjectParameter("date_from", date_from) :
                new ObjectParameter("date_from", typeof(System.DateTime));
    
            var date_toParameter = date_to.HasValue ?
                new ObjectParameter("date_to", date_to) :
                new ObjectParameter("date_to", typeof(System.DateTime));
    
            var seenParameter = seen.HasValue ?
                new ObjectParameter("seen", seen) :
                new ObjectParameter("seen", typeof(bool));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<SearchUserLog_Result>("SearchUserLog", user_nameParameter, action_type_idParameter, document_type_idParameter, date_fromParameter, date_toParameter, seenParameter);
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_00e73576_c584_46fd_af37_9aea22315f67()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_00e73576_c584_46fd_af37_9aea22315f67");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_01c3c250_1fa9_4967_9e58_e0108486c691()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_01c3c250_1fa9_4967_9e58_e0108486c691");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_022027e8_dd3d_4df0_aedc_fa16c49ebc81()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_022027e8_dd3d_4df0_aedc_fa16c49ebc81");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_064c3a97_f8f9_4962_98c0_2acaa5822525()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_064c3a97_f8f9_4962_98c0_2acaa5822525");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_06df31ed_46b4_4d08_8fb1_2c3f7d1acf32()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_06df31ed_46b4_4d08_8fb1_2c3f7d1acf32");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_0a9a896d_35b4_4c95_95ba_520fc72fadc4()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_0a9a896d_35b4_4c95_95ba_520fc72fadc4");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_0b1b03dd_4f2e_4e72_a3cf_37df994fdcb1()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_0b1b03dd_4f2e_4e72_a3cf_37df994fdcb1");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_0b4d9459_bf6a_44bd_9f4e_27952a80589f()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_0b4d9459_bf6a_44bd_9f4e_27952a80589f");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_0c35150b_e73c_42c2_9975_806627937059()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_0c35150b_e73c_42c2_9975_806627937059");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_0cd3f853_59ec_4a06_b84f_0f1922308151()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_0cd3f853_59ec_4a06_b84f_0f1922308151");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_0cffe659_7e93_4fba_875c_159b4a8e4c58()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_0cffe659_7e93_4fba_875c_159b4a8e4c58");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_0d63371d_763a_4cb0_a541_58d3476cea6e()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_0d63371d_763a_4cb0_a541_58d3476cea6e");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_0da53148_bc6c_4757_b476_d276140528a5()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_0da53148_bc6c_4757_b476_d276140528a5");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_0dafc863_0550_4a6e_a774_304c731454d0()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_0dafc863_0550_4a6e_a774_304c731454d0");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_0fe46099_ebb3_4d63_919a_4e06757989d9()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_0fe46099_ebb3_4d63_919a_4e06757989d9");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_1055e188_f92d_4028_9e45_19d54ea6045b()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_1055e188_f92d_4028_9e45_19d54ea6045b");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_132452a9_c309_4b02_a21f_89932f9a0e69()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_132452a9_c309_4b02_a21f_89932f9a0e69");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_14db3e50_cc19_42db_88b8_58be4419c8c8()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_14db3e50_cc19_42db_88b8_58be4419c8c8");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_14e3fa19_0039_4987_b01d_ab1718068da6()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_14e3fa19_0039_4987_b01d_ab1718068da6");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_16748edd_5394_41ae_95d8_8757c86741fc()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_16748edd_5394_41ae_95d8_8757c86741fc");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_1c457cd4_df2d_432d_9a33_f44f077742a4()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_1c457cd4_df2d_432d_9a33_f44f077742a4");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_1fb038f7_3948_49ef_ae71_d696b7b93466()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_1fb038f7_3948_49ef_ae71_d696b7b93466");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_20ec29dc_856c_4713_9dd8_c235d58827be()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_20ec29dc_856c_4713_9dd8_c235d58827be");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_2405a303_4ee5_4e88_85ee_28eb6f257270()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_2405a303_4ee5_4e88_85ee_28eb6f257270");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_24077c59_531e_4a66_af8a_8afd0fd5f577()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_24077c59_531e_4a66_af8a_8afd0fd5f577");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_2706d723_c0de_4b53_8013_a28f7413674c()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_2706d723_c0de_4b53_8013_a28f7413674c");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_28aef554_b27f_42d6_b850_d9a48532b45c()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_28aef554_b27f_42d6_b850_d9a48532b45c");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_327fe9ae_765b_42ff_921a_ac5bd5eacd98()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_327fe9ae_765b_42ff_921a_ac5bd5eacd98");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_3335f174_befc_444c_8ca1_acfb01aff9b2()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_3335f174_befc_444c_8ca1_acfb01aff9b2");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_344fed6a_b484_4810_abb8_5a53e6cc3ffa()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_344fed6a_b484_4810_abb8_5a53e6cc3ffa");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_369f6981_7f43_4536_bec7_f92487abd8ce()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_369f6981_7f43_4536_bec7_f92487abd8ce");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_379992c0_42b1_4107_92e9_9b425e5f988a()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_379992c0_42b1_4107_92e9_9b425e5f988a");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_3923cd60_d4d5_45c2_aa25_6c9fcc81f879()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_3923cd60_d4d5_45c2_aa25_6c9fcc81f879");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_39f74db2_3bb6_4796_9d9d_167ddf16e914()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_39f74db2_3bb6_4796_9d9d_167ddf16e914");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_3f6c853d_ae5a_43cc_a5e0_8719a40d96ae()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_3f6c853d_ae5a_43cc_a5e0_8719a40d96ae");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_403a9eaf_ec21_4565_9bcd_7721f2253e7c()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_403a9eaf_ec21_4565_9bcd_7721f2253e7c");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_424c7210_7fea_4475_9d21_fabb6afcfa1d()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_424c7210_7fea_4475_9d21_fabb6afcfa1d");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_433d758d_e1f5_4eb3_82e6_72910a1e48c6()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_433d758d_e1f5_4eb3_82e6_72910a1e48c6");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_46ce53f1_f3cb_466b_9582_5f27318c471c()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_46ce53f1_f3cb_466b_9582_5f27318c471c");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_47158adc_ed4e_429b_8bea_0ad58c39bf81()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_47158adc_ed4e_429b_8bea_0ad58c39bf81");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_47ca22d2_8cf3_49d7_9b7a_ef7703d6a19a()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_47ca22d2_8cf3_49d7_9b7a_ef7703d6a19a");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_56ffaae7_006f_46ed_8e16_ed41c7f8c0e4()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_56ffaae7_006f_46ed_8e16_ed41c7f8c0e4");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_58a580d6_9356_49d1_a554_967975740def()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_58a580d6_9356_49d1_a554_967975740def");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_594b858d_eef4_4d08_b6c9_0d1c192df834()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_594b858d_eef4_4d08_b6c9_0d1c192df834");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_5a208bf6_dd0c_4ff3_b03f_29f51f784233()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_5a208bf6_dd0c_4ff3_b03f_29f51f784233");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_5b4c5901_fd94_43c9_8ae1_96d4f18d9eb8()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_5b4c5901_fd94_43c9_8ae1_96d4f18d9eb8");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_5d64e91c_4d67_49a8_998c_5f28293a4641()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_5d64e91c_4d67_49a8_998c_5f28293a4641");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_612c2cc3_9ad4_4c72_a430_9b769e8e75ab()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_612c2cc3_9ad4_4c72_a430_9b769e8e75ab");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_66a4241a_886f_419e_a6f5_d5b5ba50d125()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_66a4241a_886f_419e_a6f5_d5b5ba50d125");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_684b9826_df82_4911_94f2_e90b2883e28d()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_684b9826_df82_4911_94f2_e90b2883e28d");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_6e2402e5_97c2_4f09_8f9a_dc5fe12b3a03()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_6e2402e5_97c2_4f09_8f9a_dc5fe12b3a03");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_6f17534d_a56d_480a_a004_5d1a9f0b78e7()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_6f17534d_a56d_480a_a004_5d1a9f0b78e7");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_6f46c620_90b2_4a52_955a_3a0aec18296b()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_6f46c620_90b2_4a52_955a_3a0aec18296b");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_6f8c2d11_291e_42cc_a01d_4e537cdbbbe2()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_6f8c2d11_291e_42cc_a01d_4e537cdbbbe2");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_72710d59_52f0_4312_99fc_12872e254c92()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_72710d59_52f0_4312_99fc_12872e254c92");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_73d03c0c_50b3_4849_b085_139a97156295()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_73d03c0c_50b3_4849_b085_139a97156295");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_757d0cf6_17ee_4673_9603_1eaa230d2624()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_757d0cf6_17ee_4673_9603_1eaa230d2624");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_759272eb_32e0_4c90_a2f0_5f0f1ab35f0e()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_759272eb_32e0_4c90_a2f0_5f0f1ab35f0e");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_773bcc19_9a3a_4527_976e_1f21b8633219()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_773bcc19_9a3a_4527_976e_1f21b8633219");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_78576d0d_4538_4d31_9984_fb3994296e15()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_78576d0d_4538_4d31_9984_fb3994296e15");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_7a32bd51_36c5_40f4_a654_cb9e905c8fee()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_7a32bd51_36c5_40f4_a654_cb9e905c8fee");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_807c4dea_d2b4_4431_9688_82c1f93d7051()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_807c4dea_d2b4_4431_9688_82c1f93d7051");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_809460d2_983f_4c87_afd0_7e90bd9b95ac()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_809460d2_983f_4c87_afd0_7e90bd9b95ac");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_894cb513_78ab_4a12_8653_65a423b01ea3()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_894cb513_78ab_4a12_8653_65a423b01ea3");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_8cb91518_5ee2_40b2_8679_ffbdc7ad6c69()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_8cb91518_5ee2_40b2_8679_ffbdc7ad6c69");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_8fed8422_bc72_4769_83f2_5acd87f78dd8()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_8fed8422_bc72_4769_83f2_5acd87f78dd8");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_92a1273b_a89b_468e_bff0_bdfe25992a9f()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_92a1273b_a89b_468e_bff0_bdfe25992a9f");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_933c5333_af98_401f_9d24_461ce09df64e()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_933c5333_af98_401f_9d24_461ce09df64e");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_94f28f52_dd62_4a08_94b6_2896939b84d9()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_94f28f52_dd62_4a08_94b6_2896939b84d9");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_9b23ef7d_a8e8_450e_b602_de5d67d49a4f()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_9b23ef7d_a8e8_450e_b602_de5d67d49a4f");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_9bebfa87_5120_452b_bc81_0fa99e26cafa()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_9bebfa87_5120_452b_bc81_0fa99e26cafa");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_9d3051c7_2f98_485a_86e1_46135ad4e272()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_9d3051c7_2f98_485a_86e1_46135ad4e272");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_a4b17ef8_80af_443f_8268_32559ff7b04c()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_a4b17ef8_80af_443f_8268_32559ff7b04c");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_a723b902_9586_4c2e_9e6f_9de77aa2136c()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_a723b902_9586_4c2e_9e6f_9de77aa2136c");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_a925d04a_9b6b_4919_8932_9458e8f43f83()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_a925d04a_9b6b_4919_8932_9458e8f43f83");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_aa7fc024_0cb1_48db_bc7f_b03ab1f4bf52()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_aa7fc024_0cb1_48db_bc7f_b03ab1f4bf52");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_b1d7d5a8_02c9_404c_b9f9_bed1219ba34d()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_b1d7d5a8_02c9_404c_b9f9_bed1219ba34d");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_b4164817_c21c_49c5_a250_50cf17be5df5()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_b4164817_c21c_49c5_a250_50cf17be5df5");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_b63375e1_df8b_43ab_9a58_87d27599b930()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_b63375e1_df8b_43ab_9a58_87d27599b930");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_b66e2656_dbe5_42a0_a1ac_5a4abdc01be4()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_b66e2656_dbe5_42a0_a1ac_5a4abdc01be4");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_b7153100_2538_468e_afc8_7838fe702ae7()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_b7153100_2538_468e_afc8_7838fe702ae7");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_bbbcecc7_219c_4151_bb22_e04c89c24a05()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_bbbcecc7_219c_4151_bb22_e04c89c24a05");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_c4b9ec4d_24b0_4a0a_b82e_e733ffa700c8()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_c4b9ec4d_24b0_4a0a_b82e_e733ffa700c8");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_c4fe487f_146e_4647_9946_78f79b4f5efb()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_c4fe487f_146e_4647_9946_78f79b4f5efb");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_c58cb944_eb6e_46e2_b0be_8644337d851c()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_c58cb944_eb6e_46e2_b0be_8644337d851c");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_c6625c56_7f7d_48d8_a01d_09b85109d580()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_c6625c56_7f7d_48d8_a01d_09b85109d580");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_c6c454a1_8e72_4a8a_a022_b1f14a0e4d25()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_c6c454a1_8e72_4a8a_a022_b1f14a0e4d25");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_cd6b1ec8_3485_43ac_8be4_489123d2e481()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_cd6b1ec8_3485_43ac_8be4_489123d2e481");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_cf76beb0_0180_4bf8_b0fc_1d5546516f0f()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_cf76beb0_0180_4bf8_b0fc_1d5546516f0f");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_d13549e9_5282_4c12_94da_aec36c184bb8()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_d13549e9_5282_4c12_94da_aec36c184bb8");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_d245b423_1cf5_47a5_9e04_139843282646()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_d245b423_1cf5_47a5_9e04_139843282646");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_d6fc1835_5b42_4d8c_a421_7b3ddbe69845()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_d6fc1835_5b42_4d8c_a421_7b3ddbe69845");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_da32d619_f886_4e21_ae18_65dc87f890ec()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_da32d619_f886_4e21_ae18_65dc87f890ec");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_db16f6b6_fb4b_4cb0_9938_fdad5211d1fb()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_db16f6b6_fb4b_4cb0_9938_fdad5211d1fb");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_ddb88deb_6b62_4cf2_8659_f98ad96a8cee()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_ddb88deb_6b62_4cf2_8659_f98ad96a8cee");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_def12932_4e4d_4f3e_bc2e_b5cbae3b5915()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_def12932_4e4d_4f3e_bc2e_b5cbae3b5915");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_df07ab19_b25d_4da6_ae49_bba909e6d6ab()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_df07ab19_b25d_4da6_ae49_bba909e6d6ab");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_dfef6d14_7261_42aa_adb4_1413da173e69()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_dfef6d14_7261_42aa_adb4_1413da173e69");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_e0445873_94cb_48f7_a0f1_0fbcf126ac10()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_e0445873_94cb_48f7_a0f1_0fbcf126ac10");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_e231fe7f_4761_4a61_8d6a_5f4e8cd11d1d()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_e231fe7f_4761_4a61_8d6a_5f4e8cd11d1d");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_e7fcad70_ffa5_47c0_beaa_9f3a2b1928ae()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_e7fcad70_ffa5_47c0_beaa_9f3a2b1928ae");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_e8b2f1ad_97ca_4074_8a97_b1705e1cd16e()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_e8b2f1ad_97ca_4074_8a97_b1705e1cd16e");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_e9c0a638_2666_441b_8aa9_d713ce3fc867()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_e9c0a638_2666_441b_8aa9_d713ce3fc867");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_ef5c5ac2_6248_489e_9278_36441d27f712()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_ef5c5ac2_6248_489e_9278_36441d27f712");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_efa806c8_7d92_45a1_a4f2_be00682b66a5()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_efa806c8_7d92_45a1_a4f2_be00682b66a5");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_f0bc2efc_1728_47e1_9f66_60922c2852fb()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_f0bc2efc_1728_47e1_9f66_60922c2852fb");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_f19f4082_388c_4265_b481_29c877331e34()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_f19f4082_388c_4265_b481_29c877331e34");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_f3ac502c_bf49_45a2_bcda_5198e5c7413b()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_f3ac502c_bf49_45a2_bcda_5198e5c7413b");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_f840aa2f_32a2_4451_bd22_db3646cfaeb2()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_f840aa2f_32a2_4451_bd22_db3646cfaeb2");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_f8cb1150_3c81_42ca_868a_df9264d34e2b()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_f8cb1150_3c81_42ca_868a_df9264d34e2b");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_f92ed4aa_2b34_49ba_93c1_ec6f40946372()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_f92ed4aa_2b34_49ba_93c1_ec6f40946372");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_f9b59e83_a24e_4bb7_88d5_9e9dd6ba5c43()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_f9b59e83_a24e_4bb7_88d5_9e9dd6ba5c43");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_fb79f5b1_9937_4e8a_b1af_e9cc31f8f9e9()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_fb79f5b1_9937_4e8a_b1af_e9cc31f8f9e9");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_fb82183e_b225_4803_b4c4_31216a4dec09()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_fb82183e_b225_4803_b4c4_31216a4dec09");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_fc849093_a81d_4493_b605_0786a56c0bcc()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_fc849093_a81d_4493_b605_0786a56c0bcc");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_fd11057b_00c2_4ada_aeee_5b7e83f1e021()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_fd11057b_00c2_4ada_aeee_5b7e83f1e021");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_fd465db5_3737_4bc5_9bac_7c3161287fcf()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_fd465db5_3737_4bc5_9bac_7c3161287fcf");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_ff0bc0cc_2c73_4c8d_b54e_0c8b2f1a3ffc()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_ff0bc0cc_2c73_4c8d_b54e_0c8b2f1a3ffc");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_ffa96a88_38d2_47d5_8fc6_c388bf7d92d9()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_ffa96a88_38d2_47d5_8fc6_c388bf7d92d9");
        }
    
        public virtual int SqlQueryNotificationStoredProcedure_fff4ce03_353b_49b3_bf90_61104fb5d916()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("SqlQueryNotificationStoredProcedure_fff4ce03_353b_49b3_bf90_61104fb5d916");
        }
    
        public virtual int UncloseUserShift(Nullable<int> shift_id)
        {
            var shift_idParameter = shift_id.HasValue ?
                new ObjectParameter("shift_id", shift_id) :
                new ObjectParameter("shift_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("UncloseUserShift", shift_idParameter);
        }
    
        public virtual int UnlockOrLockRestaurantOrder(Nullable<long> inv_id, Nullable<int> order_status)
        {
            var inv_idParameter = inv_id.HasValue ?
                new ObjectParameter("inv_id", inv_id) :
                new ObjectParameter("inv_id", typeof(long));
    
            var order_statusParameter = order_status.HasValue ?
                new ObjectParameter("order_status", order_status) :
                new ObjectParameter("order_status", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("UnlockOrLockRestaurantOrder", inv_idParameter, order_statusParameter);
        }
    
        public virtual int UpdateInvoicePaymentDetailsAndStatus(Nullable<int> inv_id, Nullable<int> order_status, Nullable<int> payment_kind, Nullable<decimal> paid, Nullable<decimal> cust_resceipient, Nullable<int> discount_kind, Nullable<decimal> discount, Nullable<decimal> discount2, Nullable<decimal> addition, Nullable<decimal> sales_tax, Nullable<decimal> service)
        {
            var inv_idParameter = inv_id.HasValue ?
                new ObjectParameter("inv_id", inv_id) :
                new ObjectParameter("inv_id", typeof(int));
    
            var order_statusParameter = order_status.HasValue ?
                new ObjectParameter("order_status", order_status) :
                new ObjectParameter("order_status", typeof(int));
    
            var payment_kindParameter = payment_kind.HasValue ?
                new ObjectParameter("payment_kind", payment_kind) :
                new ObjectParameter("payment_kind", typeof(int));
    
            var paidParameter = paid.HasValue ?
                new ObjectParameter("paid", paid) :
                new ObjectParameter("paid", typeof(decimal));
    
            var cust_resceipientParameter = cust_resceipient.HasValue ?
                new ObjectParameter("cust_resceipient", cust_resceipient) :
                new ObjectParameter("cust_resceipient", typeof(decimal));
    
            var discount_kindParameter = discount_kind.HasValue ?
                new ObjectParameter("discount_kind", discount_kind) :
                new ObjectParameter("discount_kind", typeof(int));
    
            var discountParameter = discount.HasValue ?
                new ObjectParameter("discount", discount) :
                new ObjectParameter("discount", typeof(decimal));
    
            var discount2Parameter = discount2.HasValue ?
                new ObjectParameter("discount2", discount2) :
                new ObjectParameter("discount2", typeof(decimal));
    
            var additionParameter = addition.HasValue ?
                new ObjectParameter("addition", addition) :
                new ObjectParameter("addition", typeof(decimal));
    
            var sales_taxParameter = sales_tax.HasValue ?
                new ObjectParameter("sales_tax", sales_tax) :
                new ObjectParameter("sales_tax", typeof(decimal));
    
            var serviceParameter = service.HasValue ?
                new ObjectParameter("service", service) :
                new ObjectParameter("service", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("UpdateInvoicePaymentDetailsAndStatus", inv_idParameter, order_statusParameter, payment_kindParameter, paidParameter, cust_resceipientParameter, discount_kindParameter, discountParameter, discount2Parameter, additionParameter, sales_taxParameter, serviceParameter);
        }
    
        public virtual int UpdatePurchasePriceFromPurchaseInvoice(Nullable<long> pro_id, Nullable<int> unit_id, Nullable<decimal> pur_price)
        {
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            var unit_idParameter = unit_id.HasValue ?
                new ObjectParameter("unit_id", unit_id) :
                new ObjectParameter("unit_id", typeof(int));
    
            var pur_priceParameter = pur_price.HasValue ?
                new ObjectParameter("pur_price", pur_price) :
                new ObjectParameter("pur_price", typeof(decimal));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("UpdatePurchasePriceFromPurchaseInvoice", pro_idParameter, unit_idParameter, pur_priceParameter);
        }
    
        public virtual int UpdateRestaurantOrderDeliveryData(Nullable<int> inv_id, Nullable<int> delivery_id, Nullable<System.DateTime> delivery_start_time, Nullable<System.DateTime> delivery_end_time)
        {
            var inv_idParameter = inv_id.HasValue ?
                new ObjectParameter("inv_id", inv_id) :
                new ObjectParameter("inv_id", typeof(int));
    
            var delivery_idParameter = delivery_id.HasValue ?
                new ObjectParameter("delivery_id", delivery_id) :
                new ObjectParameter("delivery_id", typeof(int));
    
            var delivery_start_timeParameter = delivery_start_time.HasValue ?
                new ObjectParameter("delivery_start_time", delivery_start_time) :
                new ObjectParameter("delivery_start_time", typeof(System.DateTime));
    
            var delivery_end_timeParameter = delivery_end_time.HasValue ?
                new ObjectParameter("delivery_end_time", delivery_end_time) :
                new ObjectParameter("delivery_end_time", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("UpdateRestaurantOrderDeliveryData", inv_idParameter, delivery_idParameter, delivery_start_timeParameter, delivery_end_timeParameter);
        }
    
        public virtual int UpdateSaleInvoiceReferenceInvID(Nullable<long> inv_id, Nullable<long> reference_inv_id)
        {
            var inv_idParameter = inv_id.HasValue ?
                new ObjectParameter("inv_id", inv_id) :
                new ObjectParameter("inv_id", typeof(long));
    
            var reference_inv_idParameter = reference_inv_id.HasValue ?
                new ObjectParameter("reference_inv_id", reference_inv_id) :
                new ObjectParameter("reference_inv_id", typeof(long));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("UpdateSaleInvoiceReferenceInvID", inv_idParameter, reference_inv_idParameter);
        }
    
        public virtual int UpdateSalePriceFromSaleInvoice(Nullable<long> pro_id, Nullable<int> unit_id, Nullable<decimal> price, string pricesys)
        {
            var pro_idParameter = pro_id.HasValue ?
                new ObjectParameter("pro_id", pro_id) :
                new ObjectParameter("pro_id", typeof(long));
    
            var unit_idParameter = unit_id.HasValue ?
                new ObjectParameter("unit_id", unit_id) :
                new ObjectParameter("unit_id", typeof(int));
    
            var priceParameter = price.HasValue ?
                new ObjectParameter("price", price) :
                new ObjectParameter("price", typeof(decimal));
    
            var pricesysParameter = pricesys != null ?
                new ObjectParameter("pricesys", pricesys) :
                new ObjectParameter("pricesys", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("UpdateSalePriceFromSaleInvoice", pro_idParameter, unit_idParameter, priceParameter, pricesysParameter);
        }
    
        public virtual ObjectResult<usp_GetErrorInfo_Result> usp_GetErrorInfo()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<usp_GetErrorInfo_Result>("usp_GetErrorInfo");
        }
    }
}
