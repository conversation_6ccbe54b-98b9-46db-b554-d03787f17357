@inherits LayoutComponentBase
@using Blazored.Toast.Configuration
@using Blazored.Toast
@using Blazored.Modal

<BlazoredToasts Position="ToastPosition.TopRight"
                Timeout="5"
                IconType="IconType.FontAwesome"
                SuccessClass="success-toast-override"
                SuccessIcon="fas fa-thumbs-up"
                ErrorIcon="fas fa-bug" />

<BlazoredModal />

<div class="page">
    <div class="sidebar">
        <NavMenu />
    </div>

    <main>
        <div class="top-row px-4">
            <div class="d-flex align-items-center">
                <h4 class="mb-0 text-primary">ServeIT Modern</h4>
                <span class="badge bg-secondary ms-2">v1.0</span>
            </div>
            <div class="d-flex align-items-center">
                <span class="text-muted me-3">
                    <i class="bi bi-clock"></i> @DateTime.Now.ToString("MMM dd, yyyy HH:mm")
                </span>
                <button class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-person-circle"></i> Admin
                </button>
            </div>
        </div>

        <article class="content px-4">
            @Body
        </article>
    </main>
</div>
