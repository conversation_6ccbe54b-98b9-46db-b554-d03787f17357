using System.ComponentModel.DataAnnotations;

namespace ServeIT.Models.Entities;

public class Revenue : BaseEntity<int>
{
    public int? ParentId { get; set; }

    [Required]
    [StringLength(100)]
    public string Name { get; set; } = string.Empty;

    [StringLength(1000)]
    public string? Notes { get; set; }

    // Navigation Properties
    public virtual Revenue? Parent { get; set; }
    public virtual ICollection<Revenue> Children { get; set; } = new List<Revenue>();
    public virtual ICollection<TreasuryPermission> TreasuryPermissions { get; set; } = new List<TreasuryPermission>();
}
