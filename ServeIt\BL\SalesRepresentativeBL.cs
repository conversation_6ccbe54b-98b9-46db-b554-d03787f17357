﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ServeIt.BL
{
    class SalesRepresentativeBL
    {

        public DataTable GetSaleRepresentativeDetailsByID(string SaleRepID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@sale_rep_id", SaleRepID);
                return DAL.GetData("GetSaleRepresentativeDetailsByID", para);
            }
        }

        public DataTable GetPreviousSaleRepresentativeID(string CurrentID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@current_id", CurrentID);
                return DAL.GetData("GetPreviousSaleRepresentativeID", para);
            }
        }

        public DataTable GetNextSaleRepresentativeID(string CurrentID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@current_id", CurrentID);
                return DAL.GetData("GetNextSaleRepresentativeID", para);
            }
        }

        public DataTable GetAllSalesRepresentative() 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetData("getallsalesrepresentative", null);
            }
        }


        public DataTable SearchSalesRepresentative(BE.SalesRepresentativeBE salrepbe) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@name", salrepbe.Name);
                return DAL.GetData("SearchSalesRepresentative", para);
            }
        }


        public string GetMaxSalesRepresentativeId() 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetValue("getmaxsalesrepresentativeid");
            }
        }


        public string AddOrUpdateSAlesRepresentative(BE.SalesRepresentativeBE salrepbe) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[9];
                para[0] = new SqlParameter("@check", "a");
                para[1] = new SqlParameter("@sales_rep_id", salrepbe.ID);
                para[2] = new SqlParameter("@sales_rep_name", salrepbe.Name);
                para[3] = new SqlParameter("@national_id", salrepbe.NationalID);
                para[4] = new SqlParameter("@addess", salrepbe.Address);
                para[5] = new SqlParameter("@mobile", salrepbe.Mobile);
                para[6] = new SqlParameter("@phone", salrepbe.Phone);
                para[7] = new SqlParameter("@notes", salrepbe.Notes);
                para[8] = new SqlParameter("@user_name", salrepbe.UserName);
                return DAL.InsUpdDel("managesalesrepresentative", para);
            }
        }


        public string DeleteSalesRepresentative(BE.SalesRepresentativeBE salrepbe) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "d");
                para[1] = new SqlParameter("@sales_rep_id", salrepbe.ID);
                return DAL.InsUpdDel("managesalesrepresentative", para);
            }
        }
    }
}
