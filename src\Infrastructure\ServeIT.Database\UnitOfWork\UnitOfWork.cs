using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using ServeIT.Database.Context;
using ServeIT.Database.Repositories;
using ServeIT.Database.Repositories.Interfaces;

namespace ServeIT.Database.UnitOfWork;

public class UnitOfWork : IUnitOfWork
{
    private readonly ServeITDbContext _context;
    private IDbContextTransaction? _transaction;
    private readonly Dictionary<Type, object> _repositories = new();

    // Lazy-loaded repositories
    private ICustomerRepository? _customers;
    private IProductRepository? _products;
    private IInvoiceRepository? _invoices;

    public UnitOfWork(ServeITDbContext context)
    {
        _context = context;
    }

    public ICustomerRepository Customers => 
        _customers ??= new CustomerRepository(_context);

    public IProductRepository Products =>
        _products ??= new ProductRepository(_context);

    public IInvoiceRepository Invoices =>
        _invoices ??= new InvoiceRepository(_context);

    public IRepository<TEntity, TKey> Repository<TEntity, TKey>() where TEntity : class
    {
        var type = typeof(TEntity);
        
        if (_repositories.ContainsKey(type))
        {
            return (IRepository<TEntity, TKey>)_repositories[type];
        }

        var repository = new Repository<TEntity, TKey>(_context);
        _repositories[type] = repository;
        return repository;
    }

    private T? GetRepository<T>() where T : class
    {
        var type = typeof(T);
        return _repositories.ContainsKey(type) ? (T)_repositories[type] : null;
    }

    public async Task<int> SaveChangesAsync()
    {
        return await _context.SaveChangesAsync();
    }

    public int SaveChanges()
    {
        return _context.SaveChanges();
    }

    public async Task BeginTransactionAsync()
    {
        _transaction = await _context.Database.BeginTransactionAsync();
    }

    public async Task CommitTransactionAsync()
    {
        if (_transaction != null)
        {
            await _transaction.CommitAsync();
            await _transaction.DisposeAsync();
            _transaction = null;
        }
    }

    public async Task RollbackTransactionAsync()
    {
        if (_transaction != null)
        {
            await _transaction.RollbackAsync();
            await _transaction.DisposeAsync();
            _transaction = null;
        }
    }

    public void BeginTransaction()
    {
        _transaction = _context.Database.BeginTransaction();
    }

    public void CommitTransaction()
    {
        if (_transaction != null)
        {
            _transaction.Commit();
            _transaction.Dispose();
            _transaction = null;
        }
    }

    public void RollbackTransaction()
    {
        if (_transaction != null)
        {
            _transaction.Rollback();
            _transaction.Dispose();
            _transaction = null;
        }
    }

    public async Task<int> ExecuteSqlRawAsync(string sql, params object[] parameters)
    {
        return await _context.Database.ExecuteSqlRawAsync(sql, parameters);
    }

    public async Task<int> ExecuteSqlRawAsync(string sql, CancellationToken cancellationToken, params object[] parameters)
    {
        return await _context.Database.ExecuteSqlRawAsync(sql, cancellationToken, parameters);
    }

    public void Dispose()
    {
        _transaction?.Dispose();
        _context.Dispose();
    }
}
