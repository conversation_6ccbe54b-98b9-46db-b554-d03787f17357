﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ServeIt.BE
{
    class PaperBE
    {
        public int Paper_Id { get; set; }
        public string Paper_NO { get; set; }
        public string Paper_Name { get; set; }
        public int? Cust_id { get; set; }
        public int? Paper_Type { get; set; }
        public int? Bank_Id { get; set; }
        public int Branch_Id { get; set; }
        public string BalanceAffected { get; set; }
        public int Treasury_id { get; set; }
        public int Bank_Account_id{get;set;}
        public DateTime Edit_Date { get; set; }
        public DateTime Due_Date { get; set; }
        public DateTime CollectingDate { get; set; }
        public int Paper_Status { get; set; }
        public string Notes { get; set; }
        public byte[] Image { get; set; }
        public decimal value { get; set; }
        public string User_Name { get; set; }
    }
}
