//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace ServeIt.DA
{
    using System;
    using System.Collections.Generic;
    
    public partial class stock
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public stock()
        {
            this.invoice_detail = new HashSet<invoice_detail>();
            this.products = new HashSet<product>();
            this.product_initial_balance_detail = new HashSet<product_initial_balance_detail>();
            this.product_partitioning_header = new HashSet<product_partitioning_header>();
            this.product_partitioning_header1 = new HashSet<product_partitioning_header>();
            this.stock_permission_header = new HashSet<stock_permission_header>();
        }
    
        public int stock_id { get; set; }
        public string stock_name { get; set; }
        public Nullable<int> cost_center_id { get; set; }
        public string location { get; set; }
        public string stock_emp { get; set; }
        public Nullable<bool> active { get; set; }
        public Nullable<bool> default_stock { get; set; }
        public string user_name { get; set; }
    
        public virtual cost_center cost_center { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<invoice_detail> invoice_detail { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<product> products { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<product_initial_balance_detail> product_initial_balance_detail { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<product_partitioning_header> product_partitioning_header { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<product_partitioning_header> product_partitioning_header1 { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<stock_permission_header> stock_permission_header { get; set; }
    }
}
