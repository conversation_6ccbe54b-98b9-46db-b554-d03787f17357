﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;

namespace ServeIt.BL
{
    class SalaryItemsBL
    {
        public string SalaryItemID { get; set; }
        public string SalaryItemName{get;set;}
        public string SalaryItemTypeID { get; set; }
        public decimal SalaryItemValue { get; set; }
        public bool IsActivity { get; set; }
        public string Notes { get; set; }
        public string UserName { get; set; }

        public string AddOrUpdateSalaryItems(SalaryItemsBL sal_itm_bl)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[8];
                para[0] = new SqlParameter("@check", "a");
                para[1] = new SqlParameter("@salary_item_id", sal_itm_bl.SalaryItemID);
                para[2] = new SqlParameter("@salary_item_name", sal_itm_bl.SalaryItemName);
                para[3] = new SqlParameter("@salary_item_type_id", sal_itm_bl.SalaryItemTypeID);
                para[4] = new SqlParameter("@salary_item_value", sal_itm_bl.SalaryItemValue);
                para[5] = new SqlParameter("@is_activity", sal_itm_bl.IsActivity);
                para[6] = new SqlParameter("@notes", sal_itm_bl.Notes);
                para[7] = new SqlParameter("@user_name", sal_itm_bl.UserName);
                return DAL.InsUpdDel("ManageSalaryItems", para);
            }
        }

        public string DeleteSalaryItems(string SalaryItemsID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "d");
                para[1] = new SqlParameter("@salary_item_id", SalaryItemsID);
                return DAL.InsUpdDel("ManageSalaryItems", para);
            }
        }

        public string GetMaxSalaryItemsID()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@check", "m");
                return DAL.GetValue("ManageSalaryItems", para);
            }
        }

        public DataTable GetSalaryItemTypes()
        {
            using (DA.DataAccess DAL= new DA.DataAccess())
            {
                return DAL.GetData("GetSalaryItemTypes", null);
            }
        }

        public DataTable GetSalaryItemByID(string SalaryItemID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "g");
                para[1] = new SqlParameter("@salary_item_id", SalaryItemID);
                return DAL.GetData("ManageSalaryItems", para);
            }
        }

        public DataTable SearchSalaryItems(SalaryItemsBL sal_itm_bl)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[4];
                para[0] = new SqlParameter("@check", "s");
                para[1] = new SqlParameter("@salary_item_name", sal_itm_bl.SalaryItemName);
                para[2] = new SqlParameter("salary_item_type_id", sal_itm_bl.SalaryItemTypeID);
                para[3] = new SqlParameter("@is_activity", sal_itm_bl.IsActivity);
                return DAL.GetData("ManageSalaryItems", para);
            }
        }
    }
}
