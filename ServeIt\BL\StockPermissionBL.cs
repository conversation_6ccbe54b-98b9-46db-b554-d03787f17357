﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ServeIt.BL
{
    class StockPermissionBL
    {

        public string GetInventoryPermissionCount()
        {
            string result = string.Empty;
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                using (SqlConnection con = new SqlConnection())
                {
                    con.ConnectionString = DAL.MyConnectionString().ConnectionString;
                    if (con.State != ConnectionState.Open)
                    {
                        con.Open();
                    }
                    using (SqlCommand cmd = new SqlCommand())
                    {
                        cmd.Connection = con;
                        cmd.CommandText = "select count(per_id) from stock_permission_header";
                        try
                        {
                            result = cmd.ExecuteScalar().ToString();
                        }
                        catch (SqlException ex)
                        {
                            return ex.Message;
                        }
                    }
                }
            }
            return result;
        }

        public DataTable GetPreviousStockPermissionID(string CurrentID,string PerKind)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@current_id", CurrentID);
                para[1] = new SqlParameter("@per_kind_id", PerKind);
                return DAL.GetData("GetPreviousStockPermissionID", para);
            }
        }

        public DataTable GetNextStockPermissionID(string CurrentID, string PerKind)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@current_id", CurrentID);
                para[1] = new SqlParameter("@per_kind_id", PerKind);
                return DAL.GetData("GetNextStockPermissionID", para);
            }
        }

        public string GetMaxInventoryPermissionId() 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetValue("GetMaxInventoryPermissionId");
            }
        }

        public string AddOrUpdateInventoryPermission(BE.StockPermissionBE inv_per_be) 
        {
            using (DA.DataAccess DAl = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[18];
                para[0] = new SqlParameter("@check", "a");
                para[1] = new SqlParameter("@per_id", inv_per_be.PerID);
                para[2] = new SqlParameter("@maintenance_id", inv_per_be.MaintenanceID);
                para[3] = new SqlParameter("@per_no", inv_per_be.PerNo);
                para[4] = new SqlParameter("@per_kind", inv_per_be.PerKind);
                para[5] = new SqlParameter("@stock_id", inv_per_be.StockId);
                para[6] = new SqlParameter("@stock_id_from", inv_per_be.StockIdFrom);
                para[7] = new SqlParameter("@stock_id_to", inv_per_be.StockIdTo);
                para[8] = new SqlParameter("@per_date", inv_per_be.PerDate);
                para[9] = new SqlParameter("@notes", inv_per_be.Note);
                para[10] = new SqlParameter("@user_name", inv_per_be.User_Name);
                para[11] = new SqlParameter("@per_detail_id", inv_per_be.PerDetId);
                para[12] = new SqlParameter("@pro_id", inv_per_be.ProID);
                para[13] = new SqlParameter("@pro_serial", inv_per_be.ProSerial);
                para[14] = new SqlParameter("@pro_expirey", inv_per_be.ProExpirey);
                para[15] = new SqlParameter("@unit_id", inv_per_be.UnitID);
                para[16] = new SqlParameter("@quantity", inv_per_be.Quantity);
                para[17] = new SqlParameter("@price", inv_per_be.Price);
                return DAl.InsUpdDel("ManageInventoryPermission", para);
            }
        }

        //public string AddOrUpdateRelocateInventoryPermission(BE.InventoryPermissionBE inv_per_be)
        //{
        //    using (DA.DataAccess DAl = new DA.DataAccess())
        //    {
        //        SqlParameter[] para = new SqlParameter[13];
        //        para[0] = new SqlParameter("@check", "a");
        //        para[1] = new SqlParameter("@per_id", inv_per_be.Per_Id);
        //        para[2] = new SqlParameter("@per_no", inv_per_be.Per_No);
        //        para[3] = new SqlParameter("@per_kind", inv_per_be.Per_Kind);
        //        para[4] = new SqlParameter("@stock_id", inv_per_be.Stock_Id);
        //        para[5] = new SqlParameter("@stock_id_from", inv_per_be.Stock_Id_From);
        //        para[6] = new SqlParameter("@stock_id_to", inv_per_be.Stock_Id_To);
        //        para[7] = new SqlParameter("@per_date", inv_per_be.Per_Date);
        //        para[8] = new SqlParameter("@notes", inv_per_be.Note);
        //        para[9] = new SqlParameter("@per_detail_id", inv_per_be.Per_Det_Id);
        //        para[10] = new SqlParameter("@pro_id", inv_per_be.Pro_Id);
        //        para[11] = new SqlParameter("@unit_id", inv_per_be.Unit_Id);
        //        para[12] = new SqlParameter("@quantity", inv_per_be.Quantity);
        //        return DAl.InsUpdDel("ManageInventoryPermissionRelocate", para);
        //    }
        //}

        public string DeleteInventoryPermission(BE.StockPermissionBE inv_per_be)
        {
            using (DA.DataAccess DAl = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "r");
                para[1] = new SqlParameter("@per_id", inv_per_be.PerID);
                return DAl.InsUpdDel("ManageInventoryPermission", para);
            }
        }

        public string DeleteInventoryPermissionRow(BE.StockPermissionBE inv_per_be)
        {
            using (DA.DataAccess DAl = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[3];
                para[0] = new SqlParameter("@check", "d");
                para[1] = new SqlParameter("@per_id", inv_per_be.PerID);
                para[2] = new SqlParameter("@per_detail_id", inv_per_be.PerDetId);
                return DAl.InsUpdDel("ManageInventoryPermission", para);
            }
        }

        public DataTable SearchInventoryPermission(BE.StockPermissionBE inv_per_be) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[6];
                para[0] = new SqlParameter("@per_kind",inv_per_be.PerKind);
                para[1] = new SqlParameter("@per_id", inv_per_be.PerID);
                para[2] = new SqlParameter("@per_no", inv_per_be.PerNo);
                para[3] = new SqlParameter("@stock_id", inv_per_be.StockId);
                para[4] = new SqlParameter("@datefrom", inv_per_be.DateFrom);
                para[5] = new SqlParameter("@dateto", inv_per_be.DateTo);
                return DAL.GetData("SearchInventoryPermission", para);
            }
        }

        public DataTable SearchInventoryPermissionRelocate(BE.StockPermissionBE inv_per_be)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[7];
                para[0] = new SqlParameter("@per_kind", inv_per_be.PerKind);
                para[1] = new SqlParameter("@per_id", inv_per_be.PerID);
                para[2] = new SqlParameter("@per_no", inv_per_be.PerNo);
                para[3] = new SqlParameter("@stock_id_from", inv_per_be.StockIdFrom);
                para[4] = new SqlParameter("@stock_id_to", inv_per_be.StockIdTo);
                para[5] = new SqlParameter("@datefrom", inv_per_be.DateFrom);
                para[6] = new SqlParameter("@dateto", inv_per_be.DateTo);
                return DAL.GetData("SearchInventoryPermissionRelocate", para);
            }
        }

        public DataTable GetInventoryPermissionHeaderByPerId(string PerID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@per_id",PerID);
                return DAL.GetData("GetStockPermissionHeaderByPerId", para);
            }
        }

        public DataTable GetInventoryPermissionDetailByPerId(string PerID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@per_id", PerID);
                return DAL.GetData("GetStockPermissionDetailByPerId", para);
            }
        }



        public DataTable GetInventoryPermissionStockDetailByPerId(string PerID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@per_id", PerID);
                return DAL.GetData("GetStockPermissionDetailAndStockByPerId", para);
            }
        }

        public DataTable GetMaintenanceSparePartsDetailsByMaintenanceID(string MaintenanceID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@maintenance_id",MaintenanceID);
                return DAL.GetData("GetMaintenanceSparePartsDetailsByMaintenanceID", para);
            }
        }
    }
}
