using ServeIT.Models.DTOs;
using ServeIT.Models.Common;

namespace ServeIT.API.Services;

public interface IProductService
{
    Task<ServiceResult<IEnumerable<ProductDto>>> GetAllProductsAsync();
    Task<ServiceResult<ProductDto>> GetProductByIdAsync(string id);
    Task<ServiceResult<PagedResult<ProductDto>>> GetProductsPagedAsync(PagingParameters parameters);
    Task<ServiceResult<ProductDto>> CreateProductAsync(ProductDto productDto);
    Task<ServiceResult<ProductDto>> UpdateProductAsync(string id, ProductDto productDto);
    Task<ServiceResult> DeleteProductAsync(string id);
}
