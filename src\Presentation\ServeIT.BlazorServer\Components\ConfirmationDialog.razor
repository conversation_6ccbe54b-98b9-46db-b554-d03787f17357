@using Blazored.Modal
@using Blazored.Modal.Services

<div class="modal-content">
    <div class="modal-header bg-danger text-white">
        <h5 class="modal-title">
            <i class="bi bi-exclamation-triangle"></i>
            @Title
        </h5>
    </div>
    
    <div class="modal-body">
        <div class="d-flex align-items-start">
            <div class="me-3">
                <i class="bi bi-exclamation-triangle-fill text-warning display-4"></i>
            </div>
            <div class="flex-grow-1">
                <p class="mb-2 fw-semibold">@Message</p>
                @if (!string.IsNullOrEmpty(Details))
                {
                    <p class="text-muted small mb-0">@Details</p>
                }
            </div>
        </div>
        
        @if (ShowWarningText)
        {
            <div class="alert alert-warning mt-3 mb-0">
                <i class="bi bi-info-circle"></i>
                <strong>Warning:</strong> This action cannot be undone.
            </div>
        }
    </div>
    
    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" @onclick="Cancel" disabled="@isProcessing">
            <i class="bi bi-x-circle"></i> Cancel
        </button>
        <button type="button" class="btn @ConfirmButtonClass" @onclick="Confirm" disabled="@isProcessing">
            @if (isProcessing)
            {
                <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
            }
            else
            {
                <i class="bi @ConfirmButtonIcon"></i>
            }
            @ConfirmButtonText
        </button>
    </div>
</div>

@code {
    [CascadingParameter] BlazoredModalInstance BlazoredModal { get; set; } = default!;
    
    [Parameter] public string Title { get; set; } = "Confirm Action";
    [Parameter] public string Message { get; set; } = "Are you sure you want to proceed?";
    [Parameter] public string? Details { get; set; }
    [Parameter] public string ConfirmButtonText { get; set; } = "Confirm";
    [Parameter] public string ConfirmButtonClass { get; set; } = "btn-danger";
    [Parameter] public string ConfirmButtonIcon { get; set; } = "bi-check-circle";
    [Parameter] public bool ShowWarningText { get; set; } = true;
    
    private bool isProcessing = false;

    private async Task Confirm()
    {
        isProcessing = true;
        StateHasChanged();
        
        // Small delay to show the processing state
        await Task.Delay(100);
        
        await BlazoredModal.CloseAsync(ModalResult.Ok(true));
    }

    private async Task Cancel()
    {
        await BlazoredModal.CancelAsync();
    }
}
