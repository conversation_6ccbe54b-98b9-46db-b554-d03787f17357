using AutoMapper;
using ServeIT.Database.UnitOfWork;
using ServeIT.Models.DTOs;
using ServeIT.Models.Entities;
using ServeIT.Models.Common;

namespace ServeIT.API.Services;

public class CustomerService : ICustomerService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;

    public CustomerService(IUnitOfWork unitOfWork, IMapper mapper)
    {
        _unitOfWork = unitOfWork;
        _mapper = mapper;
    }

    public async Task<ServiceResult<IEnumerable<CustomerDto>>> GetAllCustomersAsync()
    {
        try
        {
            var customers = await _unitOfWork.Customers.GetAllAsync();
            var customerDtos = _mapper.Map<IEnumerable<CustomerDto>>(customers);
            return ServiceResult<IEnumerable<CustomerDto>>.Success(customerDtos);
        }
        catch (Exception ex)
        {
            return ServiceResult<IEnumerable<CustomerDto>>.Failure("Failed to retrieve customers", ex.Message);
        }
    }

    public async Task<ServiceResult<CustomerDto>> GetCustomerByIdAsync(int id)
    {
        try
        {
            var customer = await _unitOfWork.Customers.GetByIdAsync(id);
            if (customer == null)
            {
                return ServiceResult<CustomerDto>.Failure("Customer not found");
            }

            var customerDto = _mapper.Map<CustomerDto>(customer);
            return ServiceResult<CustomerDto>.Success(customerDto);
        }
        catch (Exception ex)
        {
            return ServiceResult<CustomerDto>.Failure("Failed to retrieve customer", ex.Message);
        }
    }

    public async Task<ServiceResult<PagedResult<CustomerDto>>> GetCustomersPagedAsync(PagingParameters parameters)
    {
        try
        {
            var pagedCustomers = await _unitOfWork.Customers.GetPagedAsync(parameters);
            var customerDtos = _mapper.Map<List<CustomerDto>>(pagedCustomers.Items);
            
            var result = new PagedResult<CustomerDto>(
                customerDtos, 
                pagedCustomers.TotalCount, 
                pagedCustomers.PageNumber, 
                pagedCustomers.PageSize);

            return ServiceResult<PagedResult<CustomerDto>>.Success(result);
        }
        catch (Exception ex)
        {
            return ServiceResult<PagedResult<CustomerDto>>.Failure("Failed to retrieve customers", ex.Message);
        }
    }

    public async Task<ServiceResult<PagedResult<CustomerDto>>> SearchCustomersAsync(string searchTerm, PagingParameters parameters)
    {
        try
        {
            var pagedCustomers = await _unitOfWork.Customers.SearchCustomersAsync(searchTerm, parameters);
            var customerDtos = _mapper.Map<List<CustomerDto>>(pagedCustomers.Items);
            
            var result = new PagedResult<CustomerDto>(
                customerDtos, 
                pagedCustomers.TotalCount, 
                pagedCustomers.PageNumber, 
                pagedCustomers.PageSize);

            return ServiceResult<PagedResult<CustomerDto>>.Success(result);
        }
        catch (Exception ex)
        {
            return ServiceResult<PagedResult<CustomerDto>>.Failure("Failed to search customers", ex.Message);
        }
    }

    public async Task<ServiceResult<CustomerDto>> CreateCustomerAsync(CustomerDto customerDto)
    {
        try
        {
            // Validate mobile uniqueness
            if (!string.IsNullOrEmpty(customerDto.Mobile))
            {
                var isUnique = await _unitOfWork.Customers.IsMobileUniqueAsync(customerDto.Mobile);
                if (!isUnique)
                {
                    return ServiceResult<CustomerDto>.Failure("Mobile number already exists");
                }
            }

            var customer = _mapper.Map<Customer>(customerDto);
            customer.CreatedAt = DateTime.UtcNow;
            
            await _unitOfWork.Customers.AddAsync(customer);
            await _unitOfWork.SaveChangesAsync();

            var resultDto = _mapper.Map<CustomerDto>(customer);
            return ServiceResult<CustomerDto>.Success(resultDto, "Customer created successfully");
        }
        catch (Exception ex)
        {
            return ServiceResult<CustomerDto>.Failure("Failed to create customer", ex.Message);
        }
    }

    public async Task<ServiceResult<CustomerDto>> UpdateCustomerAsync(int id, CustomerDto customerDto)
    {
        try
        {
            var existingCustomer = await _unitOfWork.Customers.GetByIdAsync(id);
            if (existingCustomer == null)
            {
                return ServiceResult<CustomerDto>.Failure("Customer not found");
            }

            // Validate mobile uniqueness
            if (!string.IsNullOrEmpty(customerDto.Mobile))
            {
                var isUnique = await _unitOfWork.Customers.IsMobileUniqueAsync(customerDto.Mobile, id);
                if (!isUnique)
                {
                    return ServiceResult<CustomerDto>.Failure("Mobile number already exists");
                }
            }

            _mapper.Map(customerDto, existingCustomer);
            existingCustomer.UpdatedAt = DateTime.UtcNow;
            
            _unitOfWork.Customers.Update(existingCustomer);
            await _unitOfWork.SaveChangesAsync();

            var resultDto = _mapper.Map<CustomerDto>(existingCustomer);
            return ServiceResult<CustomerDto>.Success(resultDto, "Customer updated successfully");
        }
        catch (Exception ex)
        {
            return ServiceResult<CustomerDto>.Failure("Failed to update customer", ex.Message);
        }
    }

    public async Task<ServiceResult> DeleteCustomerAsync(int id)
    {
        try
        {
            var customer = await _unitOfWork.Customers.GetByIdAsync(id);
            if (customer == null)
            {
                return ServiceResult.Failure("Customer not found");
            }

            // Soft delete
            customer.IsActive = false;
            customer.UpdatedAt = DateTime.UtcNow;
            
            _unitOfWork.Customers.Update(customer);
            await _unitOfWork.SaveChangesAsync();

            return ServiceResult.Success("Customer deleted successfully");
        }
        catch (Exception ex)
        {
            return ServiceResult.Failure("Failed to delete customer", ex.Message);
        }
    }

    public async Task<ServiceResult<bool>> IsMobileUniqueAsync(string mobile, int? excludeCustomerId = null)
    {
        try
        {
            var isUnique = await _unitOfWork.Customers.IsMobileUniqueAsync(mobile, excludeCustomerId);
            return ServiceResult<bool>.Success(isUnique);
        }
        catch (Exception ex)
        {
            return ServiceResult<bool>.Failure("Failed to check mobile uniqueness", ex.Message);
        }
    }

    public async Task<ServiceResult<IEnumerable<CustomerDto>>> GetActiveCustomersAsync()
    {
        try
        {
            var customers = await _unitOfWork.Customers.GetActiveCustomersAsync();
            var customerDtos = _mapper.Map<IEnumerable<CustomerDto>>(customers);
            return ServiceResult<IEnumerable<CustomerDto>>.Success(customerDtos);
        }
        catch (Exception ex)
        {
            return ServiceResult<IEnumerable<CustomerDto>>.Failure("Failed to retrieve active customers", ex.Message);
        }
    }

    public async Task<ServiceResult<CustomerDto>> GetCustomerByMobileAsync(string mobile)
    {
        try
        {
            var customer = await _unitOfWork.Customers.GetByMobileAsync(mobile);
            if (customer == null)
            {
                return ServiceResult<CustomerDto>.Failure("Customer not found");
            }

            var customerDto = _mapper.Map<CustomerDto>(customer);
            return ServiceResult<CustomerDto>.Success(customerDto);
        }
        catch (Exception ex)
        {
            return ServiceResult<CustomerDto>.Failure("Failed to retrieve customer", ex.Message);
        }
    }
}
