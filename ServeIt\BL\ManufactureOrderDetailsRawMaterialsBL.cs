﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;

namespace ServeIt.BL
{
    class ManufactureOrderDetailsRawMaterialsBL
    {
        public string ManufactureOrderID { get; set; }
        public string ManufactureOrderDetailRawID { get; set; }
        public string ProID { get; set; }
        public string UnitID { get; set; }
        public string StockID { get; set; }
        public decimal ComponentQuantity { get; set; }
        public decimal Quantity { get; set; }
        public decimal Price { get; set; }
       public string FinishedProID { get; set; }

        public string AddOrUpdateManufactureOrderDetailsRawProduct(ManufactureOrderDetailsRawMaterialsBL manf_detail_raw_bl)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[10];
                para[0] = new SqlParameter("@check", "a");
                para[1] = new SqlParameter("@manufacture_order_id", manf_detail_raw_bl.ManufactureOrderID);
                para[2] = new SqlParameter("@manufacture_order_raw_detail_id", manf_detail_raw_bl.ManufactureOrderDetailRawID);
                para[3] = new SqlParameter("@pro_id", manf_detail_raw_bl.ProID);
                para[4] = new SqlParameter("@unit_id", manf_detail_raw_bl.UnitID);
                para[5] = new SqlParameter("@stock_id", manf_detail_raw_bl.StockID);
                para[6] = new SqlParameter("@component_quantity", manf_detail_raw_bl.ComponentQuantity);
                para[7] = new SqlParameter("@quantity", manf_detail_raw_bl.Quantity);
                para[8] = new SqlParameter("@price", manf_detail_raw_bl.Price);
                para[9] = new SqlParameter("@finished_pro_id", manf_detail_raw_bl.FinishedProID);

                return DAL.InsUpdDel("ManageManufactureOrderRawMaerialsDetails", para);
            }
        }

        public string DeleteManufactureOrderDetailsRawRow(string ManufactureOrderID, string ManufactureOrderDetailRawID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[3];
                para[0] = new SqlParameter("@check", "d");
                para[1] = new SqlParameter("@manufacture_order_id", ManufactureOrderID);
                para[2] = new SqlParameter("@manufacture_order_raw_detail_id", ManufactureOrderDetailRawID);
                return DAL.InsUpdDel("ManageManufactureOrderRawMaerialsDetails", para);
            }
        }

        public string DeleteManufactureOrderDetailsRaw(string ManufactureOrderID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "r");
                para[1] = new SqlParameter("@manufacture_order_id", ManufactureOrderID);
                return DAL.InsUpdDel("ManageManufactureOrderRawMaerialsDetails", para);
            }
        }

        public DataTable GetManufactureOrderOrderDetailsRawByID(string ManufactureOrderID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@manufacture_order_id", ManufactureOrderID);
                return DAL.GetData("GetManufactureOrderRawMaterialDetailByOrderID", para);
            }
        }
    }
}
