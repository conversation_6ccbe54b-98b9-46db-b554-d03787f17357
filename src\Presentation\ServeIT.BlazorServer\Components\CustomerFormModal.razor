@using ServeIT.Models.DTOs
@using ServeIT.Models.Common
@using ServeIT.BlazorServer.Services
@using Blazored.Toast.Services
@using Blazored.Modal
@using Blazored.Modal.Services
@using System.ComponentModel.DataAnnotations
@inject ICustomerApiService CustomerService
@inject IToastService ToastService

<div class="modal-content">
    <div class="modal-header bg-primary text-white">
        <h5 class="modal-title">
            <i class="bi @(IsEditMode ? "bi-pencil-square" : "bi-plus-circle")"></i>
            @(IsEditMode ? "Edit Customer" : "Add New Customer")
        </h5>
    </div>

    <EditForm Model="Customer" OnValidSubmit="HandleValidSubmit">
        <DataAnnotationsValidator />
        
        <div class="modal-body">
            @if (isLoading)
            {
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">@(IsEditMode ? "Updating customer..." : "Creating customer...")</p>
                </div>
            }
            else
            {
                <div class="row g-3">
                    <!-- Customer Name -->
                    <div class="col-md-6">
                        <label for="customerName" class="form-label">
                            <i class="bi bi-person"></i> Customer Name <span class="text-danger">*</span>
                        </label>
                        <InputText id="customerName" class="form-control" @bind-Value="Customer.Name"
                                   placeholder="Enter customer name" />
                        <ValidationMessage For="@(() => Customer.Name)" class="text-danger small" />
                    </div>

                    <!-- Customer Kind -->
                    <div class="col-md-6">
                        <label for="customerKind" class="form-label">
                            <i class="bi bi-tag"></i> Customer Type
                        </label>
                        <select id="customerKind" class="form-select" @bind="Customer.CustomerKind">
                            <option value="">Select customer type</option>
                            <option value="Individual">Individual</option>
                            <option value="Company">Company</option>
                            <option value="Wholesale">Wholesale</option>
                            <option value="Retail">Retail</option>
                        </select>
                    </div>

                    <!-- Mobile -->
                    <div class="col-md-6">
                        <label for="mobile" class="form-label">
                            <i class="bi bi-phone"></i> Mobile Number
                        </label>
                        <InputText id="mobile" class="form-control" @bind-Value="Customer.Mobile" 
                                   placeholder="Enter mobile number" />
                        <ValidationMessage For="@(() => Customer.Mobile)" class="text-danger small" />
                        @if (!string.IsNullOrEmpty(mobileValidationMessage))
                        {
                            <div class="text-danger small">@mobileValidationMessage</div>
                        }
                    </div>

                    <!-- Phone -->
                    <div class="col-md-6">
                        <label for="phone" class="form-label">
                            <i class="bi bi-telephone"></i> Phone Number
                        </label>
                        <InputText id="phone" class="form-control" @bind-Value="Customer.Phone" 
                                   placeholder="Enter phone number" />
                        <ValidationMessage For="@(() => Customer.Phone)" class="text-danger small" />
                    </div>

                    <!-- City -->
                    <div class="col-md-6">
                        <label for="city" class="form-label">
                            <i class="bi bi-geo-alt"></i> City
                        </label>
                        <InputText id="city" class="form-control" @bind-Value="Customer.City" 
                                   placeholder="Enter city" />
                    </div>

                    <!-- Area -->
                    <div class="col-md-6">
                        <label for="area" class="form-label">
                            <i class="bi bi-map"></i> Area
                        </label>
                        <InputText id="area" class="form-control" @bind-Value="Customer.Area" 
                                   placeholder="Enter area" />
                    </div>

                    <!-- Address -->
                    <div class="col-12">
                        <label for="address" class="form-label">
                            <i class="bi bi-house"></i> Address
                        </label>
                        <InputTextArea id="address" class="form-control" @bind-Value="Customer.Address" 
                                       rows="2" placeholder="Enter full address" />
                    </div>

                    <!-- Initial Balance -->
                    <div class="col-md-6">
                        <label for="initialBalance" class="form-label">
                            <i class="bi bi-currency-dollar"></i> Initial Balance
                        </label>
                        <InputNumber id="initialBalance" class="form-control" @bind-Value="Customer.InitialBalance" 
                                     step="0.01" placeholder="0.00" />
                        <ValidationMessage For="@(() => Customer.InitialBalance)" class="text-danger small" />
                    </div>

                    <!-- Price System -->
                    <div class="col-md-6">
                        <label for="priceSystem" class="form-label">
                            <i class="bi bi-tags"></i> Price System
                        </label>
                        <select id="priceSystem" class="form-select" @bind="Customer.PriceSystem">
                            <option value="">Select price system</option>
                            <option value="Retail">Retail</option>
                            <option value="Wholesale">Wholesale</option>
                            <option value="HalfWholesale">Half Wholesale</option>
                            <option value="Special">Special</option>
                        </select>
                    </div>

                    <!-- Sales Representative -->
                    <div class="col-md-6">
                        <label for="salesRep" class="form-label">
                            <i class="bi bi-person-badge"></i> Sales Representative
                        </label>
                        <InputNumber id="salesRep" class="form-control" @bind-Value="Customer.SalesRepresentativeId" 
                                     placeholder="Sales Rep ID" />
                    </div>

                    <!-- Default Customer -->
                    <div class="col-md-6 d-flex align-items-end">
                        <div class="form-check">
                            <InputCheckbox id="isDefault" class="form-check-input" @bind-Value="Customer.IsDefaultCustomer" />
                            <label class="form-check-label" for="isDefault">
                                <i class="bi bi-star"></i> Default Customer
                            </label>
                        </div>
                    </div>

                    <!-- Notes -->
                    <div class="col-12">
                        <label for="notes" class="form-label">
                            <i class="bi bi-journal-text"></i> Notes
                        </label>
                        <InputTextArea id="notes" class="form-control" @bind-Value="Customer.Notes" 
                                       rows="3" placeholder="Enter any additional notes" />
                        <ValidationMessage For="@(() => Customer.Notes)" class="text-danger small" />
                    </div>

                    @if (IsEditMode)
                    {
                        <!-- Active Status -->
                        <div class="col-12">
                            <div class="form-check">
                                <InputCheckbox id="isActive" class="form-check-input" @bind-Value="Customer.IsActive" />
                                <label class="form-check-label" for="isActive">
                                    <i class="bi bi-check-circle"></i> Active Customer
                                </label>
                            </div>
                        </div>
                    }
                </div>
            }
        </div>

        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" @onclick="Cancel" disabled="@isLoading">
                <i class="bi bi-x-circle"></i> Cancel
            </button>
            <button type="submit" class="btn btn-primary" disabled="@isLoading">
                <i class="bi @(IsEditMode ? "bi-check-circle" : "bi-plus-circle")"></i>
                @(IsEditMode ? "Update Customer" : "Create Customer")
            </button>
        </div>
    </EditForm>
</div>

@code {
    [CascadingParameter] BlazoredModalInstance BlazoredModal { get; set; } = default!;
    [Parameter] public CustomerDto? ExistingCustomer { get; set; }

    private CustomerDto Customer = new();
    private bool isLoading = false;
    private string mobileValidationMessage = string.Empty;
    private bool IsEditMode => ExistingCustomer != null;

    protected override void OnInitialized()
    {
        if (IsEditMode && ExistingCustomer != null)
        {
            // Create a copy to avoid modifying the original
            Customer = new CustomerDto
            {
                Id = ExistingCustomer.Id,
                Name = ExistingCustomer.Name,
                CustomerKind = ExistingCustomer.CustomerKind,
                InitialBalance = ExistingCustomer.InitialBalance,
                PriceSystem = ExistingCustomer.PriceSystem,
                City = ExistingCustomer.City,
                Area = ExistingCustomer.Area,
                Address = ExistingCustomer.Address,
                Mobile = ExistingCustomer.Mobile,
                Phone = ExistingCustomer.Phone,
                SalesRepresentativeId = ExistingCustomer.SalesRepresentativeId,
                SalesRepresentativeName = ExistingCustomer.SalesRepresentativeName,
                IsDefaultCustomer = ExistingCustomer.IsDefaultCustomer,
                Notes = ExistingCustomer.Notes,
                IsActive = ExistingCustomer.IsActive,
                CreatedAt = ExistingCustomer.CreatedAt,
                UpdatedAt = ExistingCustomer.UpdatedAt,
                CreatedBy = ExistingCustomer.CreatedBy,
                UpdatedBy = ExistingCustomer.UpdatedBy
            };
        }
        else
        {
            Customer.IsActive = true;
        }
    }

    private async Task HandleValidSubmit()
    {
        // Validate mobile uniqueness before submitting
        if (!string.IsNullOrEmpty(Customer.Mobile))
        {
            var mobileCheckResult = await CustomerService.CheckMobileUniqueAsync(
                Customer.Mobile, 
                IsEditMode ? Customer.Id : null);
            
            if (mobileCheckResult.IsSuccess && mobileCheckResult.Data == false)
            {
                mobileValidationMessage = "This mobile number is already in use by another customer.";
                return;
            }
        }

        mobileValidationMessage = string.Empty;
        isLoading = true;
        StateHasChanged();

        try
        {
            ServiceResult<CustomerDto> result;
            
            if (IsEditMode)
            {
                result = await CustomerService.UpdateCustomerAsync(Customer.Id, Customer);
            }
            else
            {
                result = await CustomerService.CreateCustomerAsync(Customer);
            }

            if (result.IsSuccess)
            {
                ToastService.ShowSuccess(result.Message ?? 
                    $"Customer {(IsEditMode ? "updated" : "created")} successfully!");
                await BlazoredModal.CloseAsync(ModalResult.Ok(result.Data));
            }
            else
            {
                ToastService.ShowError(result.Message ?? 
                    $"Failed to {(IsEditMode ? "update" : "create")} customer");
            }
        }
        catch (Exception ex)
        {
            ToastService.ShowError($"An error occurred: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task Cancel()
    {
        await BlazoredModal.CancelAsync();
    }
}
