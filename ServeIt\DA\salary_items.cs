//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace ServeIt.DA
{
    using System;
    using System.Collections.Generic;
    
    public partial class salary_items
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public salary_items()
        {
            this.employee_salary = new HashSet<employee_salary>();
            this.employee_payroll_details = new HashSet<employee_payroll_details>();
        }
    
        public int salary_item_id { get; set; }
        public string salary_item_name { get; set; }
        public Nullable<int> salary_item_type_id { get; set; }
        public Nullable<decimal> salary_item_value { get; set; }
        public Nullable<bool> is_activity { get; set; }
        public string notes { get; set; }
        public string user_name { get; set; }
    
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<employee_salary> employee_salary { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<employee_payroll_details> employee_payroll_details { get; set; }
        public virtual salary_Items_type salary_Items_type { get; set; }
    }
}
