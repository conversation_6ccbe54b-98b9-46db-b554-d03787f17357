# ServeIT Modern - Point of Sale System

A modern, web-based Point of Sale (POS) and inventory management system built with Blazor Server, ASP.NET Core Web API, and Entity Framework Core.

## 🏗️ Architecture

This solution follows Clean Architecture principles with a multi-project structure:

```
ServeIT.Modern/
├── src/
│   ├── Core/
│   │   └── ServeIT.Models/           # Domain entities, DTOs, and common classes
│   ├── Infrastructure/
│   │   └── ServeIT.Database/         # Data access layer with EF Core
│   └── Presentation/
│       ├── ServeIT.API/              # RESTful Web API
│       └── ServeIT.BlazorServer/     # Blazor Server web application
└── ServeIT.Modern.sln
```

## 🚀 Technologies Used

- **.NET 8** - Latest LTS version
- **Blazor Server** - Server-side rendering with real-time UI updates
- **ASP.NET Core Web API** - RESTful API backend
- **Entity Framework Core** - Code-First ORM with SQL Server
- **Repository Pattern** - Data access abstraction
- **Unit of Work Pattern** - Transaction management
- **AutoMapper** - Object-to-object mapping
- **FluentValidation** - Input validation
- **Bootstrap 5** - Responsive UI framework
- **Blazored Components** - Toast notifications and modals

## 📋 Features

### Core Functionality
- **Customer Management** - Complete CRUD operations with search and pagination
- **Product Management** - Inventory tracking with categories and units
- **Invoice Management** - Sales processing with detailed line items
- **Dashboard** - Real-time business metrics and analytics

### Technical Features
- **Clean Architecture** - Separation of concerns and maintainability
- **Repository Pattern** - Testable data access layer
- **Generic Repository** - Type-safe CRUD operations
- **Paging & Search** - Efficient data handling for large datasets
- **Service Result Pattern** - Consistent error handling
- **Audit Trail** - Automatic tracking of created/updated timestamps
- **Soft Delete** - Data preservation with IsActive flag

## 🛠️ Setup Instructions

### Prerequisites
- .NET 8 SDK
- SQL Server (LocalDB or full instance)
- Visual Studio 2022 or VS Code

### Database Setup
1. Update connection strings in:
   - `src/Presentation/ServeIT.API/appsettings.json`
   - `src/Presentation/ServeIT.API/appsettings.Development.json`

2. Run Entity Framework migrations:
   ```bash
   cd src/Infrastructure/ServeIT.Database
   dotnet ef database update --startup-project ../../Presentation/ServeIT.API
   ```

### Running the Application

#### Option 1: Multiple Startup Projects (Recommended)
1. Set both `ServeIT.API` and `ServeIT.BlazorServer` as startup projects
2. Configure ports:
   - API: `https://localhost:7000`
   - Blazor: `https://localhost:7001`
3. Start both projects

#### Option 2: Command Line
```bash
# Terminal 1 - Start API
cd src/Presentation/ServeIT.API
dotnet run

# Terminal 2 - Start Blazor Server
cd src/Presentation/ServeIT.BlazorServer
dotnet run
```

### Default URLs
- **Blazor Server App**: https://localhost:7001
- **API Documentation**: https://localhost:7000/swagger

## 📁 Project Structure

### ServeIT.Models (Core)
- **Entities**: Domain models (Customer, Product, Invoice, etc.)
- **DTOs**: Data transfer objects for API communication
- **Common**: Shared classes (ServiceResult, PagedResult, etc.)
- **Enums**: Business enumerations

### ServeIT.Database (Infrastructure)
- **Context**: Entity Framework DbContext
- **Configurations**: Entity configurations using Fluent API
- **Repositories**: Repository implementations
- **UnitOfWork**: Transaction management
- **Extensions**: Dependency injection setup

### ServeIT.API (Presentation)
- **Controllers**: RESTful API endpoints
- **Services**: Business logic layer
- **Mapping**: AutoMapper profiles
- **Program.cs**: API configuration and DI setup

### ServeIT.BlazorServer (Presentation)
- **Pages**: Razor pages and components
- **Services**: API client services
- **Shared**: Layout components and shared UI
- **wwwroot**: Static assets (CSS, JS, images)

## 🔧 Configuration

### Connection Strings
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=ServeITModernDb;Trusted_Connection=true;MultipleActiveResultSets=true"
  }
}
```

### API Settings
```json
{
  "ApiSettings": {
    "BaseUrl": "https://localhost:7000"
  }
}
```

## 🧪 Testing

### Running Tests
```bash
# Run all tests
dotnet test

# Run with coverage
dotnet test --collect:"XPlat Code Coverage"
```

### Test Structure
- Unit tests for repositories and services
- Integration tests for API endpoints
- Component tests for Blazor components

## 📈 Development Status

### ✅ Completed
- [x] Solution structure and project setup
- [x] Domain models and DTOs
- [x] Entity Framework Core configuration
- [x] Repository and Unit of Work patterns
- [x] API controllers and services
- [x] Blazor Server application setup
- [x] Customer management (full CRUD)
- [x] Basic dashboard and navigation

### 🚧 In Progress
- [ ] Product management implementation
- [ ] Invoice management implementation
- [ ] Advanced search and filtering
- [ ] Real-time updates with SignalR

### 📋 Planned
- [ ] Authentication and authorization
- [ ] Role-based access control
- [ ] Reporting and analytics
- [ ] Data export functionality
- [ ] Mobile responsiveness improvements
- [ ] Performance optimization
- [ ] Comprehensive testing suite

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 Support

For questions or support, please contact the development team or create an issue in the repository.
