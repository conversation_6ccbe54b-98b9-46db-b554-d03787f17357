﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;

namespace ServeIt.BL
{
    class CostCenterBL
    {

        public string CostCenterID { get; set; }
        public string CostCenterName { get; set; }
        public bool IsMainCostCenter { get; set; }


        public string GetMaxCostCenterID() 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetValue("GetMaxCostCenterID");
            }
        }

        public string AddOrUpdateCostCenter(CostCenterBL cost_center) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[4];
                para[0] = new SqlParameter("@check","a");
                para[1] = new SqlParameter("@cost_center_id",cost_center.CostCenterID);
                para[2] = new SqlParameter("@cost_center_name",cost_center.CostCenterName);
                para[3] = new SqlParameter("@is_main_cost_center",cost_center.IsMainCostCenter);
                return DAL.InsUpdDel("ManageCostCenters", para);
            }
        }

        public string DeleteCostCenter(string CostCenterID) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "d");
                para[1] = new SqlParameter("@cost_center_id", CostCenterID);
                return DAL.InsUpdDel("ManageCostCenters", para);
            }
        }

        public DataTable GetAllCostCenters() 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetData("GetAllCostCenters", null);
            }
        }
    }
}
