//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace ServeIt.DA
{
    using System;
    using System.Collections.Generic;
    
    public partial class user
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public user()
        {
            this.Active_permissions = new HashSet<Active_permissions>();
            this.bank_action = new HashSet<bank_action>();
            this.invoice_header = new HashSet<invoice_header>();
            this.products = new HashSet<product>();
            this.stock_permission_header = new HashSet<stock_permission_header>();
            this.treasury_permission = new HashSet<treasury_permission>();
            this.user_log = new HashSet<user_log>();
        }
    
        public string user_name { get; set; }
        public string password { get; set; }
        public string alter_password { get; set; }
        public string delete_password { get; set; }
        public Nullable<bool> is_active { get; set; }
        public string user_job { get; set; }
        public Nullable<int> cost_center_id { get; set; }
        public Nullable<int> treasury_id { get; set; }
        public string cashier_invoice_name { get; set; }
    
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Active_permissions> Active_permissions { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<bank_action> bank_action { get; set; }
        public virtual cost_center cost_center { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<invoice_header> invoice_header { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<product> products { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<stock_permission_header> stock_permission_header { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<treasury_permission> treasury_permission { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<user_log> user_log { get; set; }
    }
}
