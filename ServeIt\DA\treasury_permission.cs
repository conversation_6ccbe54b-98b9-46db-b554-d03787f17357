//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace ServeIt.DA
{
    using System;
    using System.Collections.Generic;
    
    public partial class treasury_permission
    {
        public int per_id { get; set; }
        public Nullable<int> per_no { get; set; }
        public Nullable<int> per_kind_id { get; set; }
        public Nullable<System.DateTime> per_date { get; set; }
        public Nullable<int> cust_id { get; set; }
        public Nullable<int> expense_id { get; set; }
        public Nullable<bool> manufacture_expense { get; set; }
        public Nullable<int> revenue_id { get; set; }
        public Nullable<int> cost_center_id { get; set; }
        public Nullable<int> treasury_id { get; set; }
        public Nullable<int> treas_from { get; set; }
        public Nullable<int> treas_to { get; set; }
        public Nullable<bool> is_discount { get; set; }
        public Nullable<decimal> value { get; set; }
        public string notes { get; set; }
        public string user_name { get; set; }
    
        public virtual customer_vendor customer_vendor { get; set; }
        public virtual expens expens { get; set; }
        public virtual revenues_items revenues_items { get; set; }
        public virtual treasury treasury { get; set; }
        public virtual treasury_permssion_kind treasury_permssion_kind { get; set; }
        public virtual user user { get; set; }
    }
}
