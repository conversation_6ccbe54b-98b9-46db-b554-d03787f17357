﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;

namespace ServeIt.BL
{
    class EmployeePayrollBL
    {
        public string PayrollID { get; set; }
        public string EmployeeID { get; set; }
        public string ManagementID { get; set; }
        public string EmployeeName { get; set; }
        public DateTime? TranDate { get; set; }
        public DateTime DateFrom { get; set; }
        public DateTime DateTo { get; set; }
        public string AttendanceDays { get; set; }
        public string AbsenceDays { get; set; }
        public string DelayMinutes { get; set; }
        public string AdditionMinutes { get; set; }
        public string WorkPeriodHours { get; set; }
        public decimal BorrowValue { get; set; }
        public string PayrollDetailID { get; set; }
        public string SalaryItemID { get; set; }
        public bool IsActivity { get; set; }
        public string SalaryItemValue { get; set; }

        public string AddOrUpdateEmployeePayrollHeader(EmployeePayrollBL emp_payrol_bl)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[12];
                para[0] = new SqlParameter("@check","a");
                para[1] = new SqlParameter("@payroll_id",emp_payrol_bl.PayrollID);
                para[2] = new SqlParameter("@employee_id",emp_payrol_bl.EmployeeID);
                para[3] = new SqlParameter("@tran_date",emp_payrol_bl.TranDate);
                para[4] = new SqlParameter("@date_from",emp_payrol_bl.DateFrom);
                para[5] = new SqlParameter("@date_to",emp_payrol_bl.DateTo);
                para[6] = new SqlParameter("@attendance_days",emp_payrol_bl.AttendanceDays);
                para[7] = new SqlParameter("@absence_days",emp_payrol_bl.AbsenceDays);
                para[8] = new SqlParameter("@delay_minutes",emp_payrol_bl.DelayMinutes);
                para[9] = new SqlParameter("@addition_minutes",emp_payrol_bl.AdditionMinutes);
                para[10] = new SqlParameter("@work_period_hours",emp_payrol_bl.WorkPeriodHours);
                para[11] = new SqlParameter("@borrow_value", emp_payrol_bl.BorrowValue);
                return DAL.InsUpdDel("ManageEmployeePayrollHeader", para);
            }
        }

        public string DeleteEmployeePayrollHeader(string PayrollID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "d");
                para[1] = new SqlParameter("@payroll_id", PayrollID);
                
                return DAL.InsUpdDel("ManageEmployeePayrollHeader", para);
            }
        }

        public string GetMaxEmployeePayrollHeaderID()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@check", "m");
                
                return DAL.GetValue("ManageEmployeePayrollHeader", para);
            }
        }

        public DataTable GetEmployeePayrollHeaderByID(string PayrollID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "g");
                para[1] = new SqlParameter("@payroll_id", PayrollID);
                
                return DAL.GetData("ManageEmployeePayrollHeader", para);
            }
        }

        public DataTable SearchEmployeePayrollHeader(EmployeePayrollBL emp_payrol_bl)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[5];
                para[0] = new SqlParameter("@check", "s");
                para[1] = new SqlParameter("@management_id", emp_payrol_bl.ManagementID);
                para[2] = new SqlParameter("@employee_name", emp_payrol_bl.EmployeeName);
                para[3] = new SqlParameter("@date_from", emp_payrol_bl.DateFrom);
                para[4] = new SqlParameter("@date_to", emp_payrol_bl.DateTo);
                return DAL.GetData("ManageEmployeePayrollHeader", para);
            }
        }
        public string AddOrUpdateEmployeePayrollDetails(EmployeePayrollBL emp_payrol_bl)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[6];
                para[0] = new SqlParameter("@check", "a");
                para[1] = new SqlParameter("@payroll_id", emp_payrol_bl.PayrollID);
                para[2] = new SqlParameter("@payroll_detail_id", emp_payrol_bl.PayrollDetailID);
                para[3] = new SqlParameter("@salary_item_id", emp_payrol_bl.SalaryItemID);
                para[4] = new SqlParameter("@is_activity", emp_payrol_bl.IsActivity);
                para[5] = new SqlParameter("@salary_item_value", emp_payrol_bl.SalaryItemValue);
               
                return DAL.InsUpdDel("ManageEmployeePayrollDetails", para);
            }
        }

        public string DeleteEmployeePayrollDetail(string PayrollID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "d");
                para[1] = new SqlParameter("@payroll_id", PayrollID);

                return DAL.InsUpdDel("ManageEmployeePayrollDetails", para);
            }
        }

        public DataTable GetEmployeePayrollDetailsByID(string PayrollID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "g");
                para[1] = new SqlParameter("@payroll_id",PayrollID);

                return DAL.GetData("ManageEmployeePayrollDetails", para);
            }
        }

        public DataTable CalculateEmployeePayrollHeaderOnPeriod(string EmployeeID, DateTime DateFrom, DateTime DateTo)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[3];
                para[0] = new SqlParameter("@employee_id", EmployeeID);
                para[1] = new SqlParameter("@date_from", DateFrom);
                para[2] = new SqlParameter("@date_to", DateTo);
                return DAL.GetData("CalculateEmployeePayrollHeaderOnPeriod", para);
            }
        }

        public DataTable CalculateEmployeePayrollDetailsOnPeriod(string EmployeeID, DateTime DateFrom, DateTime DateTo)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[3];
                para[0] = new SqlParameter("@employee_id", EmployeeID);
                para[1] = new SqlParameter("@date_from", DateFrom);
                para[2] = new SqlParameter("@date_to", DateTo);
                return DAL.GetData("CalculateEmployeePayrollDetailsOnPeriod", para);
            }
        }

        public DataTable RPTGetEmployeePayrollHeader(string PayrollID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@payroll_id", PayrollID);
                return DAL.GetData("RPTGetEmployeePayrollHeader", para);
            }
        }

        public DataTable RPTGetEmployeePayrollDetailsActivityItems(string PayrollID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@payroll_id", PayrollID);
                return DAL.GetData("RPTGetEmployeePayrollDetailsActivityItems", para);
            }
        }

        public DataTable RPTGetEmployeePayrollDetailsSalaryItems(string PayrollID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@payroll_id", PayrollID);
                return DAL.GetData("RPTGetEmployeePayrollDetailsSalaryItems", para);
            }
        }


    }
}
