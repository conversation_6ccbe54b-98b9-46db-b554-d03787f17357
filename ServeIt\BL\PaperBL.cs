﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ServeIt.BL
{
    class PaperBL
    {
        public string AddOrUpdateFinancialPaper(BE.PaperBE paperbe) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[15];
                para[0] = new SqlParameter("@check", "a");
                para[1] = new SqlParameter("@paper_id", paperbe.Paper_Id);
                para[2] = new SqlParameter("@paper_no", paperbe.Paper_NO);
                para[3] = new SqlParameter("@paper_name", paperbe.Paper_Name);
                para[4] = new SqlParameter("@cust_id", paperbe.Cust_id);
                para[5] = new SqlParameter("@paper_type", paperbe.Paper_Type);
                para[6] = new SqlParameter("@bank_id", paperbe.Bank_Id);
                para[7] = new SqlParameter("@branch_id", paperbe.Branch_Id);
                para[8] = new SqlParameter("@edit_date", paperbe.Edit_Date);
                para[9] = new SqlParameter("@due_date", paperbe.Due_Date);
                para[10] = new SqlParameter("@paper_status", paperbe.Paper_Status);
                para[11] = new SqlParameter("@paper_image", paperbe.Image);
                para[12] = new SqlParameter("@value", paperbe.value);
                para[13] = new SqlParameter("@notes", paperbe.Notes);
                para[14] = new SqlParameter("@user_name", paperbe.User_Name);
                return DAL.InsUpdDel("ManagePapers", para);
            }
        }

        public string CollectingPapers(BE.PaperBE paperbe) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[9];
                para[0] = new SqlParameter("@check", "c");
                para[1] = new SqlParameter("@balance_affected", paperbe.BalanceAffected);
                para[2] = new SqlParameter("@paper_type", paperbe.Paper_Type);
                para[3] = new SqlParameter("@paper_id", paperbe.Paper_Id);
                para[4] = new SqlParameter("@paper_status", paperbe.Paper_Status);
                para[5] = new SqlParameter("@treas_id", paperbe.Treasury_id);
                para[6] = new SqlParameter("@account_id", paperbe.Bank_Account_id);
                para[7] = new SqlParameter("@collecting_date", paperbe.CollectingDate);
                para[8] = new SqlParameter("@value", paperbe.value);
                return DAL.InsUpdDel("CollectingOrRefusingPapers", para);
            }
        }

        public string RefusingPapers(BE.PaperBE paperbe)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[3];
                para[0] = new SqlParameter("@check", "r");
                para[1] = new SqlParameter("@paper_id", paperbe.Paper_Id);
                para[2] = new SqlParameter("@paper_status", paperbe.Paper_Status);
                return DAL.InsUpdDel("CollectingOrRefusingPapers", para);
            }
        }


        public string DeleteFinancialPaper(BE.PaperBE paperbe)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "d");
                para[1] = new SqlParameter("paper_id", paperbe.Paper_Id);
                return DAL.InsUpdDel("ManagePapers", para);
            }
        }

        public DataTable SearchFinancialPapers(BE.PaperBE paperbe) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[5];
                para[0] = new SqlParameter("@paper_no", paperbe.Paper_NO);
                para[1] = new SqlParameter("@cust_id", paperbe.Cust_id);
                para[2] = new SqlParameter("@paper_type", paperbe.Paper_Type);
                para[3] = new SqlParameter("@bank_id", paperbe.Bank_Id);
                para[4] = new SqlParameter("@paper_status", paperbe.Paper_Status);
                return DAL.GetData("SearchPapers", para);
            }
        }

        public string GetMaxPaperId()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetValue("GetMaxPaperId");
            }
        }

        public DataTable GetPaperDetailsByPaperId(int paper_id) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@paper_id", paper_id);
                return DAL.GetData("GetPaperDetailsByPaperId", para);
            }
        }


        public DataTable GetPaperImage(string paper_id)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@paper_id", paper_id);
                return DAL.GetData("GetPaperImage", para);
            }
        }

        public string GetOutStandingChecksCount() 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetValue("GetOutStandingChecksCount");
            }
        }

        public string GetPayableChecksCount() 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetValue("GetPayableChecksCount");
            }
        }

    }
}
