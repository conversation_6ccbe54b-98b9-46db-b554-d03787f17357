﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;

namespace ServeIt.BL
{
    class UserLogBL
    {
        public string UserLogID { get; set; }
        public string UserName { get; set; }
        public int? ActionTypeID { get; set; }
        public int? DocumentTypeID { get; set; }
        public string DocumentID { get; set; }
        public string Description { get; set; }
        public DateTime ActionDate { get; set; }
        public DateTime? DateFrom { get; set; }
        public DateTime? DateTo { get; set; }

        public string AddUserLog(UserLogBL usr_log)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[7];
                para[0] = new SqlParameter("@check","a");
                para[1] = new SqlParameter("@user_log_id",usr_log.UserLogID);
                para[2] = new SqlParameter("@user_name",usr_log.UserName);
                para[3] = new SqlParameter("@action_type_id",usr_log.ActionTypeID);
                para[4] = new SqlParameter("@document_type_id",usr_log.DocumentTypeID);
                para[5] = new SqlParameter("@document_id",usr_log.DocumentID);
                para[6] = new SqlParameter("@action_date",usr_log.ActionDate);
                return DAL.InsUpdDel("ManageUserLog", para);
            }
        }

        public string DeleteUserLog(string UserLogID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "d");
                para[1] = new SqlParameter("@user_log_id", UserLogID);
               
                return DAL.InsUpdDel("ManageUserLog", para);
            }
        }

        public string GetMaxUserLogID()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@check", "m");
                return DAL.GetValue("ManageUserLog",para);
            }
        }

        public DataTable SearchUserLog(UserLogBL usr_log_bl)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[6];
                para[0] = new SqlParameter("@check", "s");
                para[1] = new SqlParameter("@user_name", usr_log_bl.UserName);
                para[2] = new SqlParameter("@action_type_id", usr_log_bl.ActionTypeID);
                para[3] = new SqlParameter("@document_type_id", usr_log_bl.DocumentTypeID);
                para[4] = new SqlParameter("@date_from", usr_log_bl.DateFrom);
                para[5] = new SqlParameter("@date_to", usr_log_bl.DateTo);
                return DAL.GetData("ManageUserLog", para);
            }
        }

        public DataTable GetActionTypes()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetData("GetActionTypes",null);
            }
        }

        public DataTable GetDocumentTypes()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetData("GetDocumentTypes", null);
            }
        }
    }
}
