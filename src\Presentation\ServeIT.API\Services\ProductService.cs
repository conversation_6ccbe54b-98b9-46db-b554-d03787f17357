using AutoMapper;
using ServeIT.Database.UnitOfWork;
using ServeIT.Models.DTOs;
using ServeIT.Models.Common;
using ServeIT.Models.Entities;

namespace ServeIT.API.Services;

public class ProductService : IProductService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;

    public ProductService(IUnitOfWork unitOfWork, IMapper mapper)
    {
        _unitOfWork = unitOfWork;
        _mapper = mapper;
    }

    public async Task<ServiceResult<IEnumerable<ProductDto>>> GetAllProductsAsync()
    {
        try
        {
            var products = await _unitOfWork.Products.GetActiveProductsAsync();
            var productDtos = _mapper.Map<IEnumerable<ProductDto>>(products);
            return ServiceResult<IEnumerable<ProductDto>>.Success(productDtos);
        }
        catch (Exception ex)
        {
            return ServiceResult<IEnumerable<ProductDto>>.Failure($"Error retrieving products: {ex.Message}");
        }
    }

    public async Task<ServiceResult<ProductDto>> GetProductByIdAsync(string id)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(id))
            {
                return ServiceResult<ProductDto>.Failure("Product ID is required");
            }

            var product = await _unitOfWork.Products.GetByIdAsync(id);
            if (product == null)
            {
                return ServiceResult<ProductDto>.Failure("Product not found");
            }

            var productDto = _mapper.Map<ProductDto>(product);
            return ServiceResult<ProductDto>.Success(productDto);
        }
        catch (Exception ex)
        {
            return ServiceResult<ProductDto>.Failure($"Error retrieving product: {ex.Message}");
        }
    }

    public async Task<ServiceResult<PagedResult<ProductDto>>> GetProductsPagedAsync(PagingParameters parameters)
    {
        try
        {
            var pagedProducts = await _unitOfWork.Products.GetPagedAsync(parameters);
            var productDtos = _mapper.Map<IEnumerable<ProductDto>>(pagedProducts.Items);

            var pagedResult = new PagedResult<ProductDto>(
                productDtos,
                pagedProducts.TotalCount,
                pagedProducts.PageNumber,
                pagedProducts.PageSize);

            return ServiceResult<PagedResult<ProductDto>>.Success(pagedResult);
        }
        catch (Exception ex)
        {
            return ServiceResult<PagedResult<ProductDto>>.Failure($"Error retrieving products: {ex.Message}");
        }
    }

    public async Task<ServiceResult<ProductDto>> CreateProductAsync(ProductDto productDto)
    {
        try
        {
            if (productDto == null)
            {
                return ServiceResult<ProductDto>.Failure("Product data is required");
            }

            // Check if barcode is unique
            if (!string.IsNullOrEmpty(productDto.InternationalBarcode))
            {
                var isBarcodeUnique = await _unitOfWork.Products.IsBarcodeUniqueAsync(productDto.InternationalBarcode);
                if (!isBarcodeUnique)
                {
                    return ServiceResult<ProductDto>.Failure("International barcode already exists");
                }
            }

            if (!string.IsNullOrEmpty(productDto.SystemBarcode))
            {
                var isBarcodeUnique = await _unitOfWork.Products.IsBarcodeUniqueAsync(productDto.SystemBarcode);
                if (!isBarcodeUnique)
                {
                    return ServiceResult<ProductDto>.Failure("System barcode already exists");
                }
            }

            var product = _mapper.Map<Product>(productDto);
            product.CreatedAt = DateTime.UtcNow;
            product.IsActive = true;

            await _unitOfWork.Products.AddAsync(product);
            await _unitOfWork.SaveChangesAsync();

            var createdProductDto = _mapper.Map<ProductDto>(product);
            return ServiceResult<ProductDto>.Success(createdProductDto);
        }
        catch (Exception ex)
        {
            return ServiceResult<ProductDto>.Failure($"Error creating product: {ex.Message}");
        }
    }

    public async Task<ServiceResult<ProductDto>> UpdateProductAsync(string id, ProductDto productDto)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(id))
            {
                return ServiceResult<ProductDto>.Failure("Product ID is required");
            }

            if (productDto == null)
            {
                return ServiceResult<ProductDto>.Failure("Product data is required");
            }

            var existingProduct = await _unitOfWork.Products.GetByIdAsync(id);
            if (existingProduct == null)
            {
                return ServiceResult<ProductDto>.Failure("Product not found");
            }

            // Check if barcode is unique (excluding current product)
            if (!string.IsNullOrEmpty(productDto.InternationalBarcode))
            {
                var isBarcodeUnique = await _unitOfWork.Products.IsBarcodeUniqueAsync(productDto.InternationalBarcode, id);
                if (!isBarcodeUnique)
                {
                    return ServiceResult<ProductDto>.Failure("International barcode already exists");
                }
            }

            if (!string.IsNullOrEmpty(productDto.SystemBarcode))
            {
                var isBarcodeUnique = await _unitOfWork.Products.IsBarcodeUniqueAsync(productDto.SystemBarcode, id);
                if (!isBarcodeUnique)
                {
                    return ServiceResult<ProductDto>.Failure("System barcode already exists");
                }
            }

            // Update properties
            _mapper.Map(productDto, existingProduct);
            existingProduct.UpdatedAt = DateTime.UtcNow;

            _unitOfWork.Products.Update(existingProduct);
            await _unitOfWork.SaveChangesAsync();

            var updatedProductDto = _mapper.Map<ProductDto>(existingProduct);
            return ServiceResult<ProductDto>.Success(updatedProductDto);
        }
        catch (Exception ex)
        {
            return ServiceResult<ProductDto>.Failure($"Error updating product: {ex.Message}");
        }
    }

    public async Task<ServiceResult> DeleteProductAsync(string id)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(id))
            {
                return ServiceResult.Failure("Product ID is required");
            }

            var product = await _unitOfWork.Products.GetByIdAsync(id);
            if (product == null)
            {
                return ServiceResult.Failure("Product not found");
            }

            // Soft delete
            product.IsActive = false;
            product.UpdatedAt = DateTime.UtcNow;

            _unitOfWork.Products.Update(product);
            await _unitOfWork.SaveChangesAsync();

            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            return ServiceResult.Failure($"Error deleting product: {ex.Message}");
        }
    }
}
