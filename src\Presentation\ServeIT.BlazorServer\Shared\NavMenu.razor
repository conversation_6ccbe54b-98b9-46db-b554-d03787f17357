<div class="top-row ps-3 navbar navbar-dark">
    <div class="container-fluid">
        <a class="navbar-brand" href="">
            <i class="bi bi-shop"></i> ServeIT
        </a>
        <button title="Navigation menu" class="navbar-toggler" @onclick="ToggleNavMenu">
            <span class="navbar-toggler-icon"></span>
        </button>
    </div>
</div>

<div class="@NavMenuCssClass nav-scrollable" @onclick="CollapseNavMenu">
    <nav class="flex-column">
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="" Match="NavLinkMatch.All">
                <span class="bi bi-house-door-fill-nav-menu" aria-hidden="true"></span> Dashboard
            </NavLink>
        </div>

        <div class="nav-item px-3">
            <NavLink class="nav-link" href="customers">
                <span class="bi bi-people-fill" aria-hidden="true"></span> Customers
            </NavLink>
        </div>

        <div class="nav-item px-3">
            <NavLink class="nav-link" href="products">
                <span class="bi bi-box-seam" aria-hidden="true"></span> Products
            </NavLink>
        </div>

        <div class="nav-item px-3">
            <NavLink class="nav-link" href="invoices">
                <span class="bi bi-receipt" aria-hidden="true"></span> Invoices
            </NavLink>
        </div>

        <div class="nav-item px-3">
            <NavLink class="nav-link" href="reports">
                <span class="bi bi-graph-up" aria-hidden="true"></span> Reports
            </NavLink>
        </div>

        <div class="nav-item px-3">
            <NavLink class="nav-link" href="settings">
                <span class="bi bi-gear-fill" aria-hidden="true"></span> Settings
            </NavLink>
        </div>
    </nav>
</div>

@code {
    private bool collapseNavMenu = true;

    private string? NavMenuCssClass => collapseNavMenu ? "collapse" : null;

    private void ToggleNavMenu()
    {
        collapseNavMenu = !collapseNavMenu;
    }

    private void CollapseNavMenu()
    {
        collapseNavMenu = true;
    }
}
