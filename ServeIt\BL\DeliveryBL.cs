﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;

namespace ServeIt.BL
{
    class DeliveryBL
    {
        public string DeliveryID { get; set; }
        public string DeliveryName { get; set; }
        public bool IsActive { get; set; }
        public string Notes { get; set; }

        public string GetMaxDeliveryID()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetValue("GetMaxDeliveryID");
            }
        }

        public string AddOrUpdateDelivery(DeliveryBL deliver_bl)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[5];
                para[0] = new SqlParameter("@check", "a");
                para[1] = new SqlParameter("@delivery_id", deliver_bl.DeliveryID);
                para[2] = new SqlParameter("@delivery_name", deliver_bl.DeliveryName);
                para[3] = new SqlParameter("@is_active", deliver_bl.IsActive);
                para[4] = new SqlParameter("@notes", deliver_bl.Notes);
                return DAL.InsUpdDel("ManageDelivery", para);
            }
        }

        public string DeleteDelivery(string DeliveryID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "d");
                para[1] = new SqlParameter("@delivery_id", DeliveryID);
                return DAL.InsUpdDel("ManageDelivery", para);
            }
        }

        public DataTable SearchDelivery(string DeliveryName)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "s");
                para[1] = new SqlParameter("@delivery_name", DeliveryName);
                return DAL.GetData("ManageDelivery", para);
            }
        }


        public DataTable GetDeliveryByID(string DeliveryID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@delivery_id", DeliveryID);
                return DAL.GetData("GetDeliveryByID", para);
            }
        }

        public DataTable GetAllDelivery()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@check", "g");
                return DAL.GetData("ManageDelivery", para);
            }
        }

        public DataTable GetActiveDelivery()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@check", "c");
                return DAL.GetData("ManageDelivery", para);
            }
        }
    }

}
