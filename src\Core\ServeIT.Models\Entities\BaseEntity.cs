using System.ComponentModel.DataAnnotations;

namespace ServeIT.Models.Entities;

public abstract class BaseEntity
{
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime? UpdatedAt { get; set; }
    public string? CreatedBy { get; set; }
    public string? UpdatedBy { get; set; }
    public bool IsActive { get; set; } = true;
}

public abstract class BaseEntity<TKey> : BaseEntity
{
    [Key]
    public virtual TKey Id { get; set; } = default!;
}
