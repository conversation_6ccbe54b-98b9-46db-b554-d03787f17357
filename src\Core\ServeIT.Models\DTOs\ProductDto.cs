using System.ComponentModel.DataAnnotations;

namespace ServeIT.Models.DTOs;

public class ProductDto
{
    [Required(ErrorMessage = "Product ID is required")]
    [StringLength(50)]
    public string Id { get; set; } = string.Empty;

    [StringLength(50)]
    public string? ProductId2 { get; set; }

    [StringLength(50)]
    public string? ProductNumber { get; set; }

    [Required(ErrorMessage = "Product name is required")]
    [StringLength(200, ErrorMessage = "Product name cannot exceed 200 characters")]
    public string Name { get; set; } = string.Empty;

    [StringLength(100)]
    public string? Serial { get; set; }

    public int? CategoryId { get; set; }
    public string? CategoryName { get; set; }

    [StringLength(100)]
    public string? ManufactureCountry { get; set; }

    [StringLength(200)]
    public string? StorePlace { get; set; }

    [Range(0, 100, ErrorMessage = "VAT must be between 0 and 100")]
    public decimal VAT { get; set; }

    [Range(0, double.MaxValue, ErrorMessage = "Request limit must be positive")]
    public decimal RequestLimit { get; set; }

    public int? DefaultStockId { get; set; }
    public string? DefaultStockName { get; set; }

    public int ProductKind { get; set; }

    [StringLength(50)]
    public string? InternationalBarcode { get; set; }

    [StringLength(50)]
    public string? SystemBarcode { get; set; }

    [StringLength(1000)]
    public string? Notes { get; set; }

    public bool IsActive { get; set; } = true;

    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string? CreatedBy { get; set; }
    public string? UpdatedBy { get; set; }

    public List<ProductUnitDto> ProductUnits { get; set; } = new();
}
