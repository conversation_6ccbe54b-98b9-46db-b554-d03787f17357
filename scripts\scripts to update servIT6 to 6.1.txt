USE [ServIT6]

go

/****** Object:  StoredProcedure [dbo].[manageproduct]    Script Date: 2022-02-06 6:12:30 PM ******/
SET ansi_nulls ON

go

SET quoted_identifier ON

go

ALTER PROC [dbo].[Manageproduct]
(
    @check NCHAR(1),
    @pro_id BIGINT,
    @pro_id2 NVARCHAR(100) = NULL,
    @pro_kind INT = NULL,
    @pro_name NVARCHAR(100) = NULL,
    @cat_id INT = NULL,
    @img VARBINARY(max) = NULL,
    @manfacturing_country NVARCHAR(100) = NULL,
    @store_place NVARCHAR(max) = NULL,
    @vat MONEY = NULL,
    @request_limit DECIMAL(18, 5) = NULL,
    @default_stock INT = NULL,
    @unit_id INT = NULL,
    @unit_kind INT = NULL,
    @unit_count DECIMAL(18, 3) = NULL,
    @pur_price MONEY = NULL,
    @wholesale_Price MONEY = NULL,
    @half_wholesale_price MONEY = NULL,
    @retail_price MONEY = NULL,
    @lowest_price MONEY = NULL,
    @highest_price MONEY = NULL,
    @discount NVARCHAR(20) = NULL,
    @default_purchase_unit BIT = NULL,
    @default_sale_unit BIT = NULL,
    @international_barcode NVARCHAR(50) = NULL,
    @system_barcode NVARCHAR(20) = NULL,
    @is_active BIT = NULL,
    @notes NVARCHAR(200) = NULL,
    @user_name NVARCHAR(100) = NULL
)
AS
BEGIN
    BEGIN TRANSACTION

    IF @check = 'a'
    BEGIN
        IF NOT EXISTS (SELECT pro_id FROM product WHERE pro_id = @pro_id)
        BEGIN
            INSERT INTO product
            (
                pro_id,
                pro_id2,
                pro_kind,
                pro_name,
                cat_id,
                manufacturing_country,
                store_place,
                vat,
                request_limit,
                default_stock,
                is_active,
                notes,
                [user_name]
            )
            VALUES
            (@pro_id,
             @pro_id2,
             @pro_kind,
             @pro_name,
             @cat_id,
             @manfacturing_country,
             @store_place,
             @vat,
             @request_limit,
             @default_stock,
             @is_active,
             @notes,
             @user_name
            )

            INSERT INTO product_unit
            (
                pro_id,
                unit_id,
                unit_kind,
                unit_count,
                purchase_price,
                wholesale_price,
                half_wholesale_price,
                retail_price,
                lowest_price,
                highest_price,
                discount,
                default_purchase_unit,
                default_sale_unit,
                international_barcode,
                system_barcode
            )
            VALUES
            (@pro_id,
             @unit_id,
             @unit_kind,
             @unit_count,
             @pur_price,
             @wholesale_Price,
             @half_wholesale_price,
             @retail_price,
             @lowest_price,
             @highest_price,
             @discount,
             @default_purchase_unit,
             @default_sale_unit,
             @international_barcode,
             @system_barcode
            )

            INSERT INTO product_images
            VALUES
            (@pro_id, @img)
        END
        ELSE
        BEGIN
            UPDATE product
            SET pro_id2 = @pro_id2,
                pro_kind = @pro_kind,
                pro_name = @pro_name,
                cat_id = @cat_id,
                manufacturing_country = @manfacturing_country,
                store_place = @store_place,
                vat = @vat,
                request_limit = @request_limit,
                default_stock = @default_stock,
                is_active = @is_active,
                notes = @notes
            WHERE pro_id = @pro_id

            UPDATE product_images
            SET pro_image = @img
            WHERE pro_id = @pro_id

            IF NOT EXISTS
            (
                SELECT pro_id
                FROM product_unit
                WHERE pro_id = @pro_id
                      AND unit_kind = @unit_kind
            )
            BEGIN
                INSERT INTO product_unit
                (
                    pro_id,
                    unit_id,
                    unit_kind,
                    unit_count,
                    purchase_price,
                    wholesale_price,
                    half_wholesale_price,
                    retail_price,
                    lowest_price,
                    highest_price,
                    discount,
                    default_purchase_unit,
                    default_sale_unit,
                    international_barcode,
                    system_barcode
                )
                VALUES
                (@pro_id,
                 @unit_id,
                 @unit_kind,
                 @unit_count,
                 @pur_price,
                 @wholesale_Price,
                 @half_wholesale_price,
                 @retail_price,
                 @lowest_price,
                 @highest_price,
                 @discount,
                 @default_purchase_unit,
                 @default_sale_unit,
                 @international_barcode,
                 @system_barcode
                )
            END
            ELSE
            BEGIN
                UPDATE product_unit
                SET unit_id = @unit_id,
                    unit_count = @unit_count,
                    purchase_price = @pur_price,
                    wholesale_price = @wholesale_Price,
                    half_wholesale_price = @half_wholesale_price,
                    retail_price = @retail_price,
                    lowest_price = @lowest_price,
                    highest_price = @highest_price,
                    discount = @discount,
                    default_purchase_unit = @default_purchase_unit,
                    default_sale_unit = @default_sale_unit,
                    international_barcode = @international_barcode,
                    system_barcode = @system_barcode
                WHERE pro_id = @pro_id
                      AND unit_kind = @unit_kind
            END
        END
    END
    ELSE IF @check = 'd'
    BEGIN
        DELETE FROM product_collected
        WHERE pro_id = @pro_id

        DELETE FROM product_initial_balance_detail
        WHERE pro_id = @pro_id

        DELETE FROM product_unit
        WHERE pro_id = @pro_id

        DELETE FROM product_images
        WHERE pro_id = @pro_id

        DELETE FROM product
        WHERE pro_id = @pro_id
    END

    IF (@@ERROR <> 0)
    BEGIN
        ROLLBACK TRANSACTION
    END
    ELSE
    BEGIN
        COMMIT TRAN
    END
END



go

/****** Object:  StoredProcedure [dbo].[RPTSalesOfProduct]    Script Date: 2022-02-07 12:00:09 PM ******/
SET ansi_nulls ON

go

SET quoted_identifier ON

go

ALTER PROC [dbo].[Rptsalesofproduct]
(
    @cat_id INT = NULL,
    @pro_id BIGINT = NULL,
    @cust_id INT = NULL,
    @sale_rep_id INT = NULL,
    @treas_id INT = NULL,
    @datefrom DATETIME = NULL,
    @dateto DATETIME = NULL,
    @user_name NVARCHAR(100) = NULL
)
AS
BEGIN
    DECLARE @stockTransaction TABLE
    (
        stock_id INT,
        init_bal_id INT NULL,
        inv_id INT NULL,
        per_id INT NULL,
        relocate_kind INT NULL,
        inv_det_id INT NULL,
        per_det_id INT NULL,
        pro_id BIGINT,
        unit_id INT,
        quantity DECIMAL(18, 5),
        price MONEY,
        date DATETIME,
        [user_name] NVARCHAR(100),
        description NVARCHAR(200),
        notes NVARCHAR(max) NULL
    )

    INSERT INTO @stockTransaction
    SELECT stock_id,
           init_bal_id,
           inv_id,
           per_id,
           relocate_kind,
           inv_det_id,
           per_det_id,
           pro_id,
           unit_id,
           quantity,
           price,
           [date],
           dbo.Getinvoiceusernamebyinvid(inv_id),
           [description],
           notes
    FROM dbo.Stocktransactionwithservicesproducts(@pro_id)

    DECLARE @salesTable TABLE
    (
        date_from DATETIME,
        date_to DATETIME,
        treas_name NVARCHAR(100),
        salse_rep_name NVARCHAR(100),
        cust_name NVARCHAR(100),
        pro_id INT,
        pro_name NVARCHAR(200),
        sales_quantity DECIMAL(18, 5),
        sales_price MONEY,
        sales_value MONEY,
        return_sales_quantity DECIMAL(18, 5),
        return_sales_price MONEY,
        return_sales_value MONEY,
        net_sales_quantity DECIMAL(18, 5),
        net_sales_value MONEY
    )

    INSERT INTO @salesTable
    SELECT @datefrom,
           @dateto,
           dbo.Getsalesrepnamebyid(@sale_rep_id),
           dbo.Gettreasurynamebytreasid(@treas_id),
           dbo.Getcustnamebyid(@cust_id),
           ST.pro_id,
           dbo.Getproname(ST.pro_id),
           Sum(dbo.Getsalesquantityofproducttransactionfromstocktransaction(quantity)),
           price,
           Sum(dbo.Getsalesquantityofproducttransactionfromstocktransaction(quantity) * price),
           Sum(dbo.Getretsalesquantityofproducttransactionfromstocktransaction(quantity)),
           price,
           Sum((dbo.Getretsalesquantityofproducttransactionfromstocktransaction(quantity) * price)),
           (Sum(dbo.Getsalesquantityofproducttransactionfromstocktransaction(quantity))
            - Sum((dbo.Getretsalesquantityofproducttransactionfromstocktransaction(quantity)))
           ),
           (Sum(dbo.Getsalesquantityofproducttransactionfromstocktransaction(quantity) * price)
            - Sum((dbo.Getretsalesquantityofproducttransactionfromstocktransaction(quantity) * price))
           )
    FROM @stockTransaction ST
        JOIN product PR
            ON ST.pro_id = PR.pro_id
    WHERE dbo.Getinvoicekindbyinvid(inv_id) <> 5
          AND dbo.Getinvoicekindbyinvid(inv_id) <> 1
          AND dbo.Getinvoicekindbyinvid(inv_id) <> 2
          AND (
                  @cat_id IS NULL
                  OR PR.cat_id = @cat_id
                  OR PR.cat_id IN (
                                      SELECT child FROM dbo.Getsubcategoriesofcategory(@cat_id)
                                  )
              )
          AND (
                  @pro_id IS NULL
                  OR ST.pro_id = @pro_id
              )
          AND (
                  @treas_id IS NULL
                  OR dbo.Gettreasuryidbyinvid(inv_id) = @treas_id
              )
          AND (
                  @cust_id IS NULL
                  OR dbo.Getcustidbyinvid(inv_id) = @cust_id
              )
          AND (
                  @sale_rep_id IS NULL
                  OR dbo.Getsalerepidbyinvid(inv_id) = @sale_rep_id
              )
          AND (
                  @user_name IS NULL
                  OR ST.[user_name] = @user_name
              )
          AND (
                  @datefrom IS NULL
                  OR @dateto IS NULL
                  OR dbo.Getinvoicedatebyinvid(inv_id)
          BETWEEN @datefrom AND @dateto
              )
    GROUP BY ST.pro_id,
             price

    SELECT *
    FROM @salesTable
    ORDER BY pro_name
END


GO
/****** Object:  UserDefinedFunction [dbo].[CalculateProductsCostsAndBalances]    Script Date: 2022-02-07 3:56:30 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
ALTER function [dbo].[CalculateProductsCostsAndBalances]
(
    @pro_id bigint = null,
    @cat_id int = null,
    @dateto datetime = null
)
returns @products_costs_Balances table
(
    tran_id int NOT NULL PRIMARY KEY NONCLUSTERED identity(1, 1),
    inv_id int,
    per_id int,
    init_bal_id int,
    manf_order_id int,
    tran_date datetime,
    stock_id int,
    pro_id bigint,
    description nvarchar(200),
    quantity decimal(18, 3),
    outgoing decimal(18, 3),
    price money,
    cost_price money,
    balance decimal(18, 3)
)
as
begin
    declare @stock_transaction table
    (
        stock_id int,
        init_bal_id int null,
        inv_id int null,
        per_id int null,
        manf_order_id int null,
        relocate_kind int null,
        inv_det_id int null,
        per_det_id int null,
        pro_id bigint,
        unit_id int,
        quantity decimal(18, 3),
        price float,
        date datetime,
        description nvarchar(200),
        notes nvarchar(max) null
    )
    insert into @stock_transaction
    select stock_id,
           init_bal_id,
           inv_id,
           per_id,
           manf_order_id,
           relocate_kind,
           inv_det_id,
           per_det_id,
           pro_id,
           unit_id,
           quantity,
           price,
           [date],
           [description],
           notes
    from StockTransaction(@pro_id)
    where (
              @cat_id is null
              or dbo.GetCatIdBYProId(pro_id) = @cat_id
              or dbo.GetCatIdBYProId(pro_id) in (
                                                    select child from dbo.GetSubCategoriesOfCategory(@cat_id)
                                                )
          )
          and quantity <> 0
          and [date] <= @dateto

    --where dbo.GetStockPermissionKindByperId(per_id) <> 3



    declare @current_date datetime
    declare @current_balance decimal(18, 3)

    declare @pro_id_cursor bigint

    -- generate table with balance column for each transaction 
    -- to use it again and generate products_out table and products_in for each product

    declare balance_cursor cursor for
    select distinct
        [date],
        pro_id
    from @stock_transaction
    open balance_cursor
    fetch next from balance_cursor
    into @current_date,
         @pro_id_cursor
    while @@FETCH_STATUS = 0
    begin
        set @current_balance =
        (
            select SUM(quantity)
            from @stock_transaction
            where date <= @current_date
                  and pro_id = @pro_id_cursor
        )


        insert into @products_costs_Balances
        select inv_id,
               per_id,
               init_bal_id,
               manf_order_id,
               [date],
               stock_id,
               pro_id,
               [description],
               quantity,
               0,
               price,
               0,
               @current_balance
        from @stock_transaction
        where date = @current_date
              and pro_id = @pro_id_cursor
        --and quantity <> 0

        fetch next from balance_cursor
        into @current_date,
             @pro_id_cursor
    end
    close balance_cursor
    deallocate balance_cursor


    -- generate table for all out transactions to loop for each row and generate products in table for each out transaction
    declare @products_out table
    (
        tran_id int,
        inv_id int,
        per_id int,
        init_bal_id int,
        manf_order_id int,
        tran_date datetime,
        stock_id int,
        pro_id bigint,
        description nvarchar(200),
        quantity decimal(18, 3),
        outgoing decimal(18, 3),
        price money,
        cost_price money,
        balance decimal(18, 3)
    )
    insert into @products_out
    select *
    from @products_costs_Balances
    where init_bal_id is null
          and (
        (
            dbo.GetInvoiceKindByInvId(inv_id) = 2
            or dbo.GetInvoiceKindByInvId(inv_id) = 3
            or dbo.GetStockPermissionKindByperId(per_id) = 2
            or dbo.GetStockPermissionKindByperId(per_id) = 4
            or (
                   manf_order_id is not null
                   and quantity > 0
               )
        )
              )
          and quantity <> 0



    -- declaring product in table to use for each out transaction		
    declare @products_in table
    (
        tran_id int,
        inv_id int,
        per_id int,
        init_bal_id int,
        manf_order_id int,
        tran_date datetime,
        stock_id int,
        pro_id bigint,
        description nvarchar(200),
        quantity decimal(18, 3),
        outgoing decimal(18, 3),
        price money,
        cost_price money,
        balance decimal(18, 3)
    )

    declare @current_date_out datetime
    declare @pro_id_cursor_out bigint
    declare @tran_id_out int

    declare @outgoing_quantity decimal(18, 3)
    declare @new_cost_price money
    declare @cost_price money
    declare @outgoing_row_quantity decimal(18, 3)
    declare @RemainingRowQuantity decimal(18, 3)
    declare @NewQuantityOut decimal(18, 3)
    declare @Old_Quantity_Outgoing decimal(18, 3)
    declare @Historical_In_Quantity decimal(18, 3)
    declare @AvailableRowQuantity decimal(18, 3)
    declare @SaleItemsCost money
    set @SaleItemsCost = 0

    -- generate cursor to loop for each product out transaction and populate product in table for each out transaction
    declare pro_out_cursor cursor for
    select distinct
        tran_date,
        pro_id,
        tran_id
    from @products_out

    open pro_out_cursor
    fetch next from pro_out_cursor
    into @current_date_out,
         @pro_id_cursor_out,
         @tran_id_out
    while @@FETCH_STATUS = 0
    begin
        delete from @products_in

        insert into @products_in
        select *
        from @products_costs_Balances
        where pro_id = @pro_id_cursor_out
              --and tran_date < @current_date_out 
              and quantity > outgoing
              and quantity <> 0
              and (
                      init_bal_id is not null
                      or dbo.GetInvoiceKindByInvId(inv_id) = 1
                      or dbo.GetInvoiceKindByInvId(inv_id) = 4
                      or dbo.GetStockPermissionKindByperId(per_id) = 1
                      or (
                             manf_order_id is not null
                             and quantity > 0
                         )
                  )

        set @NewQuantityOut =
        (
            select (quantity * -1)
            from @products_out
            where tran_date = @current_date_out
                  and pro_id = @pro_id_cursor_out
                  and tran_id = @tran_id_out
        )

        declare @current_date_in datetime
        declare @pro_id_cursor_in bigint
        declare @tran_id_cursor_in int
        declare @inv_kind int
        declare pro_in_cursor cursor for
        select distinct
            tran_date,
            pro_id,
            tran_id
        from @products_in
        open pro_in_cursor
        fetch next from pro_in_cursor
        into @current_date_in,
             @pro_id_cursor_in,
             @tran_id_cursor_in
        while @@FETCH_STATUS = 0
        begin
            set @Historical_In_Quantity =
            (
                select quantity
                from @products_in
                where pro_id = @pro_id_cursor_in
                      and tran_date = @current_date_in
                      and tran_id = @tran_id_cursor_in
            )
            set @Old_Quantity_Outgoing =
            (
                select outgoing
                from @products_in
                where pro_id = @pro_id_cursor_in
                      and tran_date = @current_date_in
                      and tran_id = @tran_id_cursor_in
            )
            set @inv_kind =
            (
                select dbo.GetInvoiceKindByInvId(inv_id)
                from @products_in
                where pro_id = @pro_id_cursor_in
                      and tran_date = @current_date_in
                      and tran_id = @tran_id_cursor_in
            )
            declare @invoice_sale_reference bigint
            set @AvailableRowQuantity = @Historical_In_Quantity - @Old_Quantity_Outgoing
            if @NewQuantityOut <= @AvailableRowQuantity
            begin
                set @outgoing_row_quantity = @Old_Quantity_Outgoing + @NewQuantityOut

                -- get sale return cost for each product out product
                if @inv_kind = 4
                begin
                    --set @new_cost_price = 
                    set @invoice_sale_reference =
                    (
                        select dbo.GetInvoiceSaleReferenceIDByInvID(inv_id)
                        from @products_in
                        where pro_id = @pro_id_cursor_in
                              and tran_date = @current_date_in
                              and tran_id = @tran_id_cursor_in
                    )
                    update @products_in
                    set price =
                        (
                            select cost_price
                            from @products_costs_Balances
                            where pro_id = @pro_id_cursor_in
                                  and inv_id = @invoice_sale_reference
                        )

                end
                set @new_cost_price =
                (
                    select price
                    from @products_in
                    where pro_id = @pro_id_cursor_in
                          and tran_date = @current_date_in
                          and tran_id = @tran_id_cursor_in
                )

                update @products_in
                set outgoing = @outgoing_row_quantity
                where pro_id = @pro_id_cursor_in
                      and tran_date = @current_date_in
                      and tran_id = @tran_id_cursor_in

                update @products_costs_Balances
                set outgoing = @outgoing_row_quantity
                where pro_id = @pro_id_cursor_in
                      and tran_date = @current_date_in
                      and tran_id = @tran_id_cursor_in

                set @SaleItemsCost += (@NewQuantityOut * @new_cost_price)
                BREAK
            end
            else if @NewQuantityOut > @AvailableRowQuantity
            begin
                set @RemainingRowQuantity = @NewQuantityOut - @AvailableRowQuantity
                set @outgoing_row_quantity = @AvailableRowQuantity + @Old_Quantity_Outgoing
                set @NewQuantityOut = @NewQuantityOut - @AvailableRowQuantity
                if @inv_kind = 4
                begin
                    set @invoice_sale_reference =
                    (
                        select dbo.GetInvoiceSaleReferenceIDByInvID(inv_id)
                        from @products_in
                        where pro_id = @pro_id_cursor_in
                              and tran_date = @current_date_in
                              and tran_id = @tran_id_cursor_in
                    )
                    update @products_in
                    set price =
                        (
                            select cost_price
                            from @products_costs_Balances
                            where pro_id = @pro_id_cursor_in
                                  and inv_id = @invoice_sale_reference
                        )
                end
                --else
                set @new_cost_price =
                (
                    select price
                    from @products_in
                    where pro_id = @pro_id_cursor_in
                          and tran_date = @current_date_in
                          and tran_id = @tran_id_cursor_in
                )
                --set @new_cost_price = (select price from @products_in where pro_id = @pro_id_cursor_in and tran_date = @current_date_in and tran_id = @tran_id_cursor_in)

                update @products_in
                set outgoing = @outgoing_row_quantity
                where pro_id = @pro_id_cursor_in
                      and tran_date = @current_date_in
                      and tran_id = @tran_id_cursor_in

                update @products_costs_Balances
                set outgoing = @outgoing_row_quantity
                where pro_id = @pro_id_cursor_in
                      and tran_date = @current_date_in
                      and tran_id = @tran_id_cursor_in

                set @SaleItemsCost += (@AvailableRowQuantity * @new_cost_price)
            end

            fetch next from pro_in_cursor
            into @current_date_in,
                 @pro_id_cursor_in,
                 @tran_id_cursor_in
        end

        close pro_in_cursor
        deallocate pro_in_cursor

        set @NewQuantityOut =
        (
            select (quantity * -1)
            from @products_out
            where tran_date = @current_date_out
                  and pro_id = @pro_id_cursor_out
                  and tran_id = @tran_id_out
        )

        set @cost_price = coalesce((@SaleItemsCost / @NewQuantityOut), 0)

        update @products_costs_Balances
        set cost_price = @cost_price
        where pro_id = @pro_id_cursor_in
              and tran_date = @current_date_in
              and tran_id = @tran_id_cursor_in
        --set @SaleItemsCost = 0

        update @products_costs_Balances
        set cost_price = @cost_price
        where pro_id = @pro_id_cursor_out
              and tran_date = @current_date_out
              and tran_id = @tran_id_out
        set @SaleItemsCost = 0


        fetch next from pro_out_cursor
        into @current_date_out,
             @pro_id_cursor_out,
             @tran_id_out
    end
    close pro_out_cursor
    deallocate pro_out_cursor

    return
end


GO
/****** Object:  UserDefinedFunction [dbo].[StockTransactionExpirey]    Script Date: 2022-02-07 3:50:53 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
ALTER function [dbo].[StockTransactionExpirey] (@pro_id bigint = null)
returns @table table
(
    stock_id int,
    init_bal_id int null,
    inv_id int null,
    per_id int null,
    manf_order_id int null,
    relocate_kind int null,
    inv_det_id int null,
    per_det_id int null,
    pro_id bigint,
    pro_expirey datetime null,
    unit_id int,
    quantity decimal(18, 5),
    price float,
    [date] datetime,
    [description] nvarchar(200),
    notes nvarchar(max) null
)
as
begin

    -- insert initial balance data
    insert into @table
    (
        stock_id,
        init_bal_id,
        pro_id,
        pro_expirey,
        unit_id,
        quantity,
        price,
        date,
        description
    )
    select stock_id,
           init_bal_id,
           pro_id,
           pro_expirey,
           unit_id,
           dbo.GetTotalQuantityOfItems(pro_id, unit_id, quantity),
           dbo.GetProPriceInSmallUnit(pro_id, unit_id, price),
           (
               select first_period_date from company
           ),
           N' رصيد اول المدة '
    from product_initial_balance_detail
    where (
              @pro_id is null
              or pro_id = @pro_id
          )
          and (dbo.GetPtoductKindByProID(pro_id) <> 3)
          and (dbo.GetPtoductKindByProID(pro_id) <> 4)

    -- insert stock permission data where stock relocate from is not null
    insert into @table
    (
        stock_id,
        per_id,
        per_det_id,
        pro_id,
        pro_expirey,
        unit_id,
        quantity,
        price,
        date,
        description
    )
    select dbo.GetStockIDFromByStockPerId(per_id),
           per_id,
           per_detail_id,
           pro_id,
           pro_expirey,
           unit_id,
           dbo.GetTotalQuantityOfItems(pro_id, unit_id, quantity) * -1,
           dbo.GetProPriceInSmallUnit(pro_id, unit_id, price),
           dbo.GetStockPerDateByPerId(per_id),
           N'تحويل من مخزن : ' + dbo.GetStockNameByStockId(dbo.GetStockIDFromByStockPerId(per_id))
    from stock_permission_detail
    where (
              dbo.GetStockPermissionKindByperId(per_id) = 3
              and dbo.GetStockIDFromByStockPerId(per_id) is not null
          )
          and (
                  @pro_id is null
                  or pro_id = @pro_id
              )
          and (dbo.GetPtoductKindByProID(pro_id) <> 3)
          and (dbo.GetPtoductKindByProID(pro_id) <> 4)


    -- insert stock permission data where stock relocate to is not null
    insert into @table
    (
        stock_id,
        per_id,
        per_det_id,
        pro_id,
        pro_expirey,
        unit_id,
        quantity,
        price,
        date,
        description
    )
    select dbo.GetStockIDToByStockPerId(per_id),
           per_id,
           per_detail_id,
           pro_id,
           pro_expirey,
           unit_id,
           dbo.GetTotalQuantityOfItems(pro_id, unit_id, quantity),
           dbo.GetProPriceInSmallUnit(pro_id, unit_id, price),
           dbo.GetStockPerDateByPerId(per_id),
           N'تحويل الي مخزن : ' + dbo.GetStockNameByStockId(dbo.GetStockIDToByStockPerId(per_id))
    from stock_permission_detail
    where (
              dbo.GetStockPermissionKindByperId(per_id) = 3
              and dbo.GetStockIDToByStockPerId(per_id) is not null
          )
          and (
                  @pro_id is null
                  or pro_id = @pro_id
              )
          and (dbo.GetPtoductKindByProID(pro_id) <> 3)
          and (dbo.GetPtoductKindByProID(pro_id) <> 4)

    -- insert stock permission data
    insert into @table
    (
        stock_id,
        per_id,
        per_det_id,
        pro_id,
        pro_expirey,
        unit_id,
        quantity,
        price,
        date,
        description
    )
    select dbo.GetStockIDByStockPerId(per_id),
           per_id,
           per_detail_id,
           pro_id,
           pro_expirey,
           unit_id,
           dbo.SetStockTransactionQuantitySignOfStockPermission(per_id, pro_id, unit_id, quantity),
           dbo.GetProPriceInSmallUnit(pro_id, unit_id, price),
           dbo.GetStockPerDateByPerId(per_id),
           dbo.GetStockPermissionDescriptionByPerId(per_id)
    from stock_permission_detail
    where (
              dbo.GetStockPermissionKindByperId(per_id) = 1
              or dbo.GetStockPermissionKindByperId(per_id) = 2
              or dbo.GetStockPermissionKindByperId(per_id) = 4
              or dbo.GetStockPermissionKindByperId(per_id) = 5
          )
          and (
                  @pro_id is null
                  or pro_id = @pro_id
              )
          and (dbo.GetPtoductKindByProID(pro_id) <> 3)
          and (dbo.GetPtoductKindByProID(pro_id) <> 4)

    -- insert stock permission data
    insert into @table
    (
        stock_id,
        per_id,
        per_det_id,
        pro_id,
        unit_id,
        quantity,
        price,
        date,
        description
    )
    select stock_id,
           manufacture_order_id,
           manufacture_order_raw_detail_id,
           MD.pro_id,
           MD.unit_id,
           MD.quantity * -1,
           dbo.GetProPriceInSmallUnit(MD.pro_id, MD.unit_id, MD.price),
           dbo.GetManufactureOrderDateByOrderID(manufacture_order_id),
           N'صرف في تصنيع الصنف : ' + dbo.GetProName(PC.pro_id)
    from manufacture_order_Raw_detail MD
        join product_collected PC
            on md.pro_id = pc.pro_raw_id
    where (
              @pro_id is null
              or md.pro_id = @pro_id
          )

    -- insert manufacture order
    insert into @table
    (
        stock_id,
        manf_order_id,
        per_det_id,
        pro_id,
        pro_expirey,
        unit_id,
        quantity,
        price,
        date,
        description
    )
    select stock_id,
           manufacture_order_id,
           manufacture_order_finished_detail_id,
           pro_id,
           pro_expirey,
           unit_id,
           default_quantity,
           dbo.GetProPriceInSmallUnit(pro_id, unit_id, unit_cost),
           dbo.GetManufactureOrderDateByOrderID(manufacture_order_id),
           N'امر تصنيع الصنف : ' + dbo.GetProName(pro_id)
    from manufacture_order_Finished_detail
    where (
              @pro_id is null
              or pro_id = @pro_id
          )

    -- insert manufacture order
    insert into @table
    (
        stock_id,
        manf_order_id,
        per_det_id,
        pro_id,
        pro_expirey,
        unit_id,
        quantity,
        price,
        date,
        description
    )
    select stock_id,
           manufacture_order_id,
           manufacture_order_finished_detail_id,
           pro_id,
           pro_expirey,
           unit_id,
           damaged_quantity * -1,
           dbo.GetProPriceInSmallUnit(pro_id, unit_id, unit_cost),
           dbo.GetManufactureOrderDateByOrderID(manufacture_order_id),
           N'هالك في امر تصنيع الصنف: ' + dbo.GetProName(pro_id)
    from manufacture_order_Finished_detail
    where (
              @pro_id is null
              or pro_id = @pro_id
          )

    -- insert invoice data
    insert into @table
    (
        stock_id,
        inv_id,
        inv_det_id,
        pro_id,
        pro_expirey,
        unit_id,
        quantity,
        price,
        date,
        description,
        notes
    )
    select stock_id,
           inv_id,
           inv_det_id,
           pro_id,
           pro_expirey,
           unit_id,
           dbo.SetStockTransactionQuantitySignOfInvoices(inv_id, pro_id, unit_id, quantity),
           dbo.GetProPriceInSmallUnit(pro_id, unit_id, (price - discount)),
           dbo.GetInvoiceDateByInvId(inv_id),
           dbo.SetStockTransactionDescriptionOfInvoices(inv_id),
           notes
    from invoice_detail
    where (
              @pro_id is null
              or pro_id = @pro_id
          )
          and dbo.GetInvoiceKindByInvId(inv_id) <> 5
          --and (dbo.GetPtoductKindByProID(pro_id ) <> 3  )
          and (dbo.GetPtoductKindByProID(pro_id) <> 4)


    -- insert invoice data
    --insert into @table (stock_id, inv_id, inv_det_id, pro_id, unit_id, quantity, date, description,notes)
    --select dbo.GetStockIdByInvId(inv_id),
    --	   inv_id,
    --	   inv_det_id,
    --	   pro_raw_id,
    --	   PC.unit_id,
    --	   dbo.SetStockTransactionQuantitySignOfInvoices(inv_id, PC.pro_raw_id, PC.unit_id, (PC.quantity * id.quantity)),
    --	   dbo.GetInvoiceDateByInvId(inv_id),
    --	   N'صرف في تصنيع الصنف : ' + dbo.GetProName(ID.pro_id),
    --	   notes
    --from product_collected PC join invoice_detail ID on PC.pro_id = ID.pro_id
    --where (@pro_id is null or pro_raw_id = @pro_id )
    --and (dbo.GetInvoiceKindByInvId(inv_id) <> 5)
    --and (dbo.GetPtoductKindByProID(PC.pro_id ) = 3)

    return
end

GO
/****** Object:  UserDefinedFunction [dbo].[TreasuryTransaction]    Script Date: 2022-02-07 3:52:45 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


ALTER function [dbo].[TreasuryTransaction] (@treas_id int = null)
returns @table table
(
    treas_id int,
    init_bal_id int null,
    inv_id bigint null,
    per_id int null,
    paper_id int null,
    expense_id int null,
    revenue_id int null,
    shift_id int null,
    relocate_kind int null,
    date datetime,
    cust_name nvarchar(200) null,
    value money,
    description nvarchar(200),
    notes nvarchar(200) null,
    [user_name] nvarchar(100) null
)
as
begin

    -- insert initial balance values
    insert into @table
    (
        init_bal_id,
        treas_id,
        date,
        value,
        description
    )
    select treausry_init_bal_id,
           treasury_id,
           date,
           quantity,
           'رصيد افتتاحي'
    from treasury_initial_balance
    where @treas_id is null
          or treasury_id = @treas_id


    -- insert invoice values
    insert into @table
    (
        treas_id,
        inv_id,
        date,
        cust_name,
        value,
        description,
        notes,
        [user_name]
    )
    select dbo.GetTreasuryIdByInvId(inv_id),
           inv_id,
           inv_date,
           dbo.GetCustNameById(cust_id),
           dbo.GetTreasuryTransactionInvoiceValue(inv_id),
           dbo.GetTreasuryTransactionInvoiceDescriptionByInvId(inv_id),
           notes,
           [user_name]
    from invoice_header
    where (
              @treas_id is null
              or treasury_id = @treas_id
          )
          and Paid > 0
          and dbo.GetInvoiceKindByInvId(inv_id) <> 5

    -- insert installment paids values
    insert into @table
    (
        treas_id,
        inv_id,
        date,
        cust_name,
        value,
        description,
        [user_name]
    )
    select treasury_id,
           inv_id,
           collecting_date,
           dbo.GetCustNameById(cust_id),
           paid,
           'تحصيل قسط رقم  ' + CAST(paid_id as nvarchar) + ' فاتورة رقم  ' + CAST(inv_id as nvarchar),
           [user_name]
    from Installment_paids
    where (
              @treas_id is null
              or treasury_id = @treas_id
          )
          and paid > 0

    -- insert treasury permission values
    insert into @table
    (
        treas_id,
        per_id,
        date,
        cust_name,
        value,
        description,
        notes,
        [user_name]
    )
    select treasury_id,
           per_id,
           per_date,
           dbo.GetCustNameById(cust_id),
           dbo.GetTreasuryPermissionValueByPerId(per_id, per_kind_id, treas_from, treas_to, value),
           dbo.GetTreasuryPermissionDescriptionByPerId(per_id, per_kind_id, treas_from, treas_to, cust_id),
           notes,
           [user_name]
    from treasury_permission
    where (
              @treas_id is null
              or treasury_id = @treas_id
          )
          and (
                  is_discount = 0
                  and per_kind_id <> 5
                  and per_kind_id <> 6
                  and per_kind_id <> 7
              )


    -- insert treasury permission relocate from values
    insert into @table
    (
        treas_id,
        per_id,
        date,
        value,
        description,
        notes,
        [user_name]
    )
    select treas_from,
           per_id,
           per_date,
           dbo.GetTreasuryPermissionValueByPerId(per_id, per_kind_id, treas_from, null, value),
           dbo.GetTreasuryPermissionDescriptionByPerId(per_id, per_kind_id, treas_from, null, cust_id),
           notes,
           [user_name]
    from treasury_permission
    where (
              @treas_id is null
              or treas_from = @treas_id
          )
          and per_kind_id = 5


    -- insert treasury permission relocate to values
    insert into @table
    (
        treas_id,
        per_id,
        date,
        value,
        description,
        notes,
        [user_name]
    )
    select treas_to,
           per_id,
           per_date,
           dbo.GetTreasuryPermissionValueByPerId(per_id, per_kind_id, null, treas_to, value),
           dbo.GetTreasuryPermissionDescriptionByPerId(per_id, per_kind_id, null, treas_to, cust_id),
           notes,
           [user_name]
    from treasury_permission
    where (
              @treas_id is null
              or treas_to = @treas_id
          )
          and per_kind_id = 5

    -- insert treasury permission papers collecting values
    --insert into @table (treas_id, paper_id, date, cust_name,value, description,notes)
    --select treas_id_affected,
    --	   paper_id,
    --	   due_date,
    --	   dbo.GetCustNameById(cust_id),
    --	   dbo.GetTreasuryTransactionValueByPaperId(paper_id, value),
    --	   dbo.GetTreasuryTransactionDescriptionByPaperId(paper_id),
    --	   notes
    --from paper
    --where paper_status = 2 and treas_id_affected is not null

    -- insert expenses
    insert into @table
    (
        treas_id,
        per_id,
        expense_id,
        date,
        value,
        description,
        notes,
        [user_name]
    )
    select treasury_id,
           per_id,
           expense_id,
           per_date,
           value * -1,
           dbo.GetExpenseNameByID(expense_id),
           notes,
           [user_name]
    from treasury_permission
    where (
              @treas_id is null
              or treasury_id = @treas_id
          )
          and per_kind_id = 6

    -- insert revenues
    insert into @table
    (
        treas_id,
        per_id,
        revenue_id,
        date,
        value,
        description,
        notes,
        [user_name]
    )
    select treasury_id,
           per_id,
           revenue_id,
           per_date,
           value,
           dbo.GetRevenueNameByID(revenue_id),
           notes,
           [user_name]
    from treasury_permission
    where (
              @treas_id is null
              or treasury_id = @treas_id
          )
          and revenue_id is not null

    -- insert financial papers collected
    insert into @table
    (
        treas_id,
        paper_id,
        date,
        cust_name,
        value,
        description,
        notes,
        [user_name]
    )
    select treas_id_affected,
           paper_id,
           collecting_date,
           dbo.GetCustNameById(cust_id),
           dbo.GetPaperValueByPaperID(paper_id, value),
           dbo.GetPaperDescriptionByPaperID(paper_id),
           notes,
           [user_name]
    from paper
    where paper_status = 2
          and treas_id_affected is not null
          and (
                  @treas_id is null
                  or treas_id_affected = @treas_id
              )



    -- insert work shift closing data
    insert into @table
    (
        treas_id,
        shift_id,
        [date],
        value,
        description,
        [user_name]
    )
    select treasury_id,
           shift_id,
           actual_end_time,
           net_cash_drawer * -1,
           'اقفال خزينة المستخدم  ' + CAST(user_name as nvarchar) + ' يومية رقم  ' + CAST(shift_id as nvarchar),
           [user_name]
    from user_work_shift
    where @treas_id is null
          or treasury_id = @treas_id

    -- insert work shift closing data
    insert into @table
    (
        treas_id,
        shift_id,
        [date],
        value,
        description,
        [user_name]
    )
    select close_treasury_id,
           shift_id,
           actual_end_time,
           net_cash_drawer,
           'اقفال خزينة المستخدم  ' + CAST(user_name as nvarchar) + ' يومية رقم  ' + CAST(shift_id as nvarchar),
           [user_name]
    from user_work_shift
    where @treas_id is null
          or close_treasury_id = @treas_id

    return
end


GO
/****** Object:  UserDefinedFunction [dbo].[StockTransactionSerial]    Script Date: 2022-02-07 3:52:03 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
ALTER function [dbo].[StockTransactionSerial] (@pro_id bigint = null)
returns @table table
(
    stock_id int,
    init_bal_id int null,
    inv_id int null,
    per_id int null,
    relocate_kind int null,
    inv_det_id int null,
    per_det_id int null,
    pro_id bigint,
    pro_serial nvarchar(50) null,
    unit_id int,
    quantity decimal(18, 3),
    date datetime,
    description nvarchar(200),
    notes nvarchar(max) null
)
as
begin

    -- insert initial balance data
    insert into @table
    (
        stock_id,
        init_bal_id,
        pro_id,
        pro_serial,
        unit_id,
        quantity,
        date,
        description
    )
    select stock_id,
           init_bal_id,
           pro_id,
           pro_serial,
           unit_id,
           dbo.GetTotalQuantityOfItems(pro_id, unit_id, quantity),
           (
               select first_period_date from company
           ),
           N' رصيد اول المدة '
    from product_initial_balance_detail
    where (
              @pro_id is null
              or pro_id = @pro_id
          )
          and (dbo.GetPtoductKindByProID(pro_id) <> 3)
          and (dbo.GetPtoductKindByProID(pro_id) <> 4)

    -- insert stock permission data where stock relocate from is not null
    insert into @table
    (
        stock_id,
        per_id,
        per_det_id,
        pro_id,
        pro_serial,
        unit_id,
        quantity,
        date,
        description
    )
    select dbo.GetStockIDFromByStockPerId(per_id),
           per_id,
           per_detail_id,
           pro_id,
           pro_serial,
           unit_id,
           dbo.GetTotalQuantityOfItems(pro_id, unit_id, quantity) * -1,
           dbo.GetStockPerDateByPerId(per_id),
           N'تحويل من مخزن : ' + dbo.GetStockNameByStockId(dbo.GetStockIDFromByStockPerId(per_id))
    from stock_permission_detail
    where (
              dbo.GetStockPermissionKindByperId(per_id) = 3
              and dbo.GetStockIDFromByStockPerId(per_id) is not null
          )
          and (
                  @pro_id is null
                  or pro_id = @pro_id
              )
          and (dbo.GetPtoductKindByProID(pro_id) <> 3)
          and (dbo.GetPtoductKindByProID(pro_id) <> 4)


    -- insert stock permission data where stock relocate to is not null
    insert into @table
    (
        stock_id,
        per_id,
        per_det_id,
        pro_id,
        pro_serial,
        unit_id,
        quantity,
        date,
        description
    )
    select dbo.GetStockIDToByStockPerId(per_id),
           per_id,
           per_detail_id,
           pro_id,
           pro_serial,
           unit_id,
           dbo.GetTotalQuantityOfItems(pro_id, unit_id, quantity),
           dbo.GetStockPerDateByPerId(per_id),
           N'تحويل الي مخزن : ' + dbo.GetStockNameByStockId(dbo.GetStockIDToByStockPerId(per_id))
    from stock_permission_detail
    where (
              dbo.GetStockPermissionKindByperId(per_id) = 3
              and dbo.GetStockIDToByStockPerId(per_id) is not null
          )
          and (
                  @pro_id is null
                  or pro_id = @pro_id
              )
          and (dbo.GetPtoductKindByProID(pro_id) <> 3)
          and (dbo.GetPtoductKindByProID(pro_id) <> 4)

    -- insert stock permission data
    insert into @table
    (
        stock_id,
        per_id,
        per_det_id,
        pro_id,
        pro_serial,
        unit_id,
        quantity,
        date,
        description
    )
    select dbo.GetStockIDByStockPerId(per_id),
           per_id,
           per_detail_id,
           pro_id,
           pro_serial,
           unit_id,
           dbo.SetStockTransactionQuantitySignOfStockPermission(per_id, pro_id, unit_id, quantity),
           dbo.GetStockPerDateByPerId(per_id),
           dbo.GetStockPermissionDescriptionByPerId(per_id)
    from stock_permission_detail
    where (
              dbo.GetStockPermissionKindByperId(per_id) = 1
              or dbo.GetStockPermissionKindByperId(per_id) = 2
              or dbo.GetStockPermissionKindByperId(per_id) = 4
              or dbo.GetStockPermissionKindByperId(per_id) = 5
          )
          and (
                  @pro_id is null
                  or pro_id = @pro_id
              )
          and (dbo.GetPtoductKindByProID(pro_id) <> 3)
          and (dbo.GetPtoductKindByProID(pro_id) <> 4)

    -- insert stock permission data
    insert into @table
    (
        stock_id,
        per_id,
        per_det_id,
        pro_id,
        pro_serial,
        unit_id,
        quantity,
        date,
        description
    )
    select dbo.GetStockIDByStockPerId(per_id),
           per_id,
           per_detail_id,
           pro_raw_id,
           pro_serial,
           PC.unit_id,
           (PC.quantity * SPD.quantity) * -1,
           dbo.GetStockPerDateByPerId(per_id),
           N'صرف في تصنيع الصنف : ' + dbo.GetProName(SPD.pro_id)
    from product_collected PC
        join stock_permission_detail SPD
            on PC.pro_id = SPD.pro_id
    where (dbo.GetStockPermissionKindByperId(per_id) = 6)
          and (
                  @pro_id is null
                  or pro_raw_id = @pro_id
              )
          and (dbo.GetPtoductKindByProID(PC.pro_id) = 3)


    -- insert manufacture order
    insert into @table
    (
        stock_id,
        per_id,
        per_det_id,
        pro_id,
        pro_serial,
        unit_id,
        quantity,
        date,
        description
    )
    select dbo.GetStockIDByStockPerId(per_id),
           per_id,
           per_detail_id,
           pro_id,
           pro_serial,
           unit_id,
           dbo.SetStockTransactionQuantitySignOfStockPermission(per_id, pro_id, unit_id, quantity),
           dbo.GetStockPerDateByPerId(per_id),
           N'امر تصنيع الصنف : ' + dbo.GetProName(pro_id)
    from stock_permission_detail
    where (dbo.GetStockPermissionKindByperId(per_id) = 6)
          and (
                  @pro_id is null
                  or pro_id = @pro_id
              )
          and (dbo.GetPtoductKindByProID(pro_id) = 3)

    -- insert invoice data
    insert into @table
    (
        stock_id,
        inv_id,
        inv_det_id,
        pro_id,
        pro_serial,
        unit_id,
        quantity,
        date,
        description,
        notes
    )
    select dbo.GetStockIdByInvIdAndInvDetID(inv_id, inv_det_id),
           inv_id,
           inv_det_id,
           pro_id,
           pro_serial,
           unit_id,
           dbo.SetStockTransactionQuantitySignOfInvoices(inv_id, pro_id, unit_id, quantity),
           dbo.GetInvoiceDateByInvId(inv_id),
           dbo.SetStockTransactionDescriptionOfInvoices(inv_id),
           notes
    from invoice_detail
    where (
              @pro_id is null
              or pro_id = @pro_id
          )
          and dbo.GetInvoiceKindByInvId(inv_id) <> 5
          --and (dbo.GetPtoductKindByProID(pro_id ) <> 3  )
          and (dbo.GetPtoductKindByProID(pro_id) <> 4)


    -- insert invoice data
    --insert into @table (stock_id, inv_id, inv_det_id, pro_id, unit_id, quantity, date, description,notes)
    --select dbo.GetStockIdByInvId(inv_id),
    --	   inv_id,
    --	   inv_det_id,
    --	   pro_raw_id,
    --	   PC.unit_id,
    --	   dbo.SetStockTransactionQuantitySignOfInvoices(inv_id, PC.pro_raw_id, PC.unit_id, (PC.quantity * id.quantity)),
    --	   dbo.GetInvoiceDateByInvId(inv_id),
    --	   N'صرف في تصنيع الصنف : ' + dbo.GetProName(ID.pro_id),
    --	   notes
    --from product_collected PC join invoice_detail ID on PC.pro_id = ID.pro_id
    --where (@pro_id is null or pro_raw_id = @pro_id )
    --and (dbo.GetInvoiceKindByInvId(inv_id) <> 5)
    --and (dbo.GetPtoductKindByProID(PC.pro_id ) = 3)

    return
end


GO
/****** Object:  UserDefinedFunction [dbo].[StockTransactionExpirey]    Script Date: 2022-02-07 3:50:53 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
ALTER function [dbo].[StockTransactionExpirey] (@pro_id bigint = null)
returns @table table
(
    stock_id int,
    init_bal_id int null,
    inv_id int null,
    per_id int null,
    manf_order_id int null,
    relocate_kind int null,
    inv_det_id int null,
    per_det_id int null,
    pro_id bigint,
    pro_expirey datetime null,
    unit_id int,
    quantity decimal(18, 5),
    price float,
    [date] datetime,
    [description] nvarchar(200),
    notes nvarchar(max) null
)
as
begin

    -- insert initial balance data
    insert into @table
    (
        stock_id,
        init_bal_id,
        pro_id,
        pro_expirey,
        unit_id,
        quantity,
        price,
        date,
        description
    )
    select stock_id,
           init_bal_id,
           pro_id,
           pro_expirey,
           unit_id,
           dbo.GetTotalQuantityOfItems(pro_id, unit_id, quantity),
           dbo.GetProPriceInSmallUnit(pro_id, unit_id, price),
           (
               select first_period_date from company
           ),
           N' رصيد اول المدة '
    from product_initial_balance_detail
    where (
              @pro_id is null
              or pro_id = @pro_id
          )
          and (dbo.GetPtoductKindByProID(pro_id) <> 3)
          and (dbo.GetPtoductKindByProID(pro_id) <> 4)

    -- insert stock permission data where stock relocate from is not null
    insert into @table
    (
        stock_id,
        per_id,
        per_det_id,
        pro_id,
        pro_expirey,
        unit_id,
        quantity,
        price,
        date,
        description
    )
    select dbo.GetStockIDFromByStockPerId(per_id),
           per_id,
           per_detail_id,
           pro_id,
           pro_expirey,
           unit_id,
           dbo.GetTotalQuantityOfItems(pro_id, unit_id, quantity) * -1,
           dbo.GetProPriceInSmallUnit(pro_id, unit_id, price),
           dbo.GetStockPerDateByPerId(per_id),
           N'تحويل من مخزن : ' + dbo.GetStockNameByStockId(dbo.GetStockIDFromByStockPerId(per_id))
    from stock_permission_detail
    where (
              dbo.GetStockPermissionKindByperId(per_id) = 3
              and dbo.GetStockIDFromByStockPerId(per_id) is not null
          )
          and (
                  @pro_id is null
                  or pro_id = @pro_id
              )
          and (dbo.GetPtoductKindByProID(pro_id) <> 3)
          and (dbo.GetPtoductKindByProID(pro_id) <> 4)


    -- insert stock permission data where stock relocate to is not null
    insert into @table
    (
        stock_id,
        per_id,
        per_det_id,
        pro_id,
        pro_expirey,
        unit_id,
        quantity,
        price,
        date,
        description
    )
    select dbo.GetStockIDToByStockPerId(per_id),
           per_id,
           per_detail_id,
           pro_id,
           pro_expirey,
           unit_id,
           dbo.GetTotalQuantityOfItems(pro_id, unit_id, quantity),
           dbo.GetProPriceInSmallUnit(pro_id, unit_id, price),
           dbo.GetStockPerDateByPerId(per_id),
           N'تحويل الي مخزن : ' + dbo.GetStockNameByStockId(dbo.GetStockIDToByStockPerId(per_id))
    from stock_permission_detail
    where (
              dbo.GetStockPermissionKindByperId(per_id) = 3
              and dbo.GetStockIDToByStockPerId(per_id) is not null
          )
          and (
                  @pro_id is null
                  or pro_id = @pro_id
              )
          and (dbo.GetPtoductKindByProID(pro_id) <> 3)
          and (dbo.GetPtoductKindByProID(pro_id) <> 4)

    -- insert stock permission data
    insert into @table
    (
        stock_id,
        per_id,
        per_det_id,
        pro_id,
        pro_expirey,
        unit_id,
        quantity,
        price,
        date,
        description
    )
    select dbo.GetStockIDByStockPerId(per_id),
           per_id,
           per_detail_id,
           pro_id,
           pro_expirey,
           unit_id,
           dbo.SetStockTransactionQuantitySignOfStockPermission(per_id, pro_id, unit_id, quantity),
           dbo.GetProPriceInSmallUnit(pro_id, unit_id, price),
           dbo.GetStockPerDateByPerId(per_id),
           dbo.GetStockPermissionDescriptionByPerId(per_id)
    from stock_permission_detail
    where (
              dbo.GetStockPermissionKindByperId(per_id) = 1
              or dbo.GetStockPermissionKindByperId(per_id) = 2
              or dbo.GetStockPermissionKindByperId(per_id) = 4
              or dbo.GetStockPermissionKindByperId(per_id) = 5
          )
          and (
                  @pro_id is null
                  or pro_id = @pro_id
              )
          and (dbo.GetPtoductKindByProID(pro_id) <> 3)
          and (dbo.GetPtoductKindByProID(pro_id) <> 4)

    -- insert stock permission data
    insert into @table
    (
        stock_id,
        per_id,
        per_det_id,
        pro_id,
        unit_id,
        quantity,
        price,
        date,
        description
    )
    select stock_id,
           manufacture_order_id,
           manufacture_order_raw_detail_id,
           MD.pro_id,
           MD.unit_id,
           MD.quantity * -1,
           dbo.GetProPriceInSmallUnit(MD.pro_id, MD.unit_id, MD.price),
           dbo.GetManufactureOrderDateByOrderID(manufacture_order_id),
           N'صرف في تصنيع الصنف : ' + dbo.GetProName(PC.pro_id)
    from manufacture_order_Raw_detail MD
        join product_collected PC
            on md.pro_id = pc.pro_raw_id
    where (
              @pro_id is null
              or md.pro_id = @pro_id
          )

    -- insert manufacture order
    insert into @table
    (
        stock_id,
        manf_order_id,
        per_det_id,
        pro_id,
        pro_expirey,
        unit_id,
        quantity,
        price,
        date,
        description
    )
    select stock_id,
           manufacture_order_id,
           manufacture_order_finished_detail_id,
           pro_id,
           pro_expirey,
           unit_id,
           default_quantity,
           dbo.GetProPriceInSmallUnit(pro_id, unit_id, unit_cost),
           dbo.GetManufactureOrderDateByOrderID(manufacture_order_id),
           N'امر تصنيع الصنف : ' + dbo.GetProName(pro_id)
    from manufacture_order_Finished_detail
    where (
              @pro_id is null
              or pro_id = @pro_id
          )

    -- insert manufacture order
    insert into @table
    (
        stock_id,
        manf_order_id,
        per_det_id,
        pro_id,
        pro_expirey,
        unit_id,
        quantity,
        price,
        date,
        description
    )
    select stock_id,
           manufacture_order_id,
           manufacture_order_finished_detail_id,
           pro_id,
           pro_expirey,
           unit_id,
           damaged_quantity * -1,
           dbo.GetProPriceInSmallUnit(pro_id, unit_id, unit_cost),
           dbo.GetManufactureOrderDateByOrderID(manufacture_order_id),
           N'هالك في امر تصنيع الصنف: ' + dbo.GetProName(pro_id)
    from manufacture_order_Finished_detail
    where (
              @pro_id is null
              or pro_id = @pro_id
          )

    -- insert invoice data
    insert into @table
    (
        stock_id,
        inv_id,
        inv_det_id,
        pro_id,
        pro_expirey,
        unit_id,
        quantity,
        price,
        date,
        description,
        notes
    )
    select stock_id,
           inv_id,
           inv_det_id,
           pro_id,
           pro_expirey,
           unit_id,
           dbo.SetStockTransactionQuantitySignOfInvoices(inv_id, pro_id, unit_id, quantity),
           dbo.GetProPriceInSmallUnit(pro_id, unit_id, (price - discount)),
           dbo.GetInvoiceDateByInvId(inv_id),
           dbo.SetStockTransactionDescriptionOfInvoices(inv_id),
           notes
    from invoice_detail
    where (
              @pro_id is null
              or pro_id = @pro_id
          )
          and dbo.GetInvoiceKindByInvId(inv_id) <> 5
          --and (dbo.GetPtoductKindByProID(pro_id ) <> 3  )
          and (dbo.GetPtoductKindByProID(pro_id) <> 4)


    -- insert invoice data
    --insert into @table (stock_id, inv_id, inv_det_id, pro_id, unit_id, quantity, date, description,notes)
    --select dbo.GetStockIdByInvId(inv_id),
    --	   inv_id,
    --	   inv_det_id,
    --	   pro_raw_id,
    --	   PC.unit_id,
    --	   dbo.SetStockTransactionQuantitySignOfInvoices(inv_id, PC.pro_raw_id, PC.unit_id, (PC.quantity * id.quantity)),
    --	   dbo.GetInvoiceDateByInvId(inv_id),
    --	   N'صرف في تصنيع الصنف : ' + dbo.GetProName(ID.pro_id),
    --	   notes
    --from product_collected PC join invoice_detail ID on PC.pro_id = ID.pro_id
    --where (@pro_id is null or pro_raw_id = @pro_id )
    --and (dbo.GetInvoiceKindByInvId(inv_id) <> 5)
    --and (dbo.GetPtoductKindByProID(PC.pro_id ) = 3)

    return
end


GO
/****** Object:  UserDefinedFunction [dbo].[StockTransaction]    Script Date: 2022-02-07 3:47:14 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
ALTER function [dbo].[StockTransaction] (@pro_id bigint = null)
returns @table table
(
    stock_id int,
    init_bal_id int null,
    inv_id int null,
    per_id int null,
    manf_order_id int null,
    relocate_kind int null,
    inv_det_id int null,
    per_det_id int null,
    pro_id bigint,
    unit_id int,
    quantity decimal(18, 5),
    price float,
    [date] datetime,
    [description] nvarchar(200),
    notes nvarchar(max) null
)
as
begin

    -- insert initial balance data
    insert into @table
    (
        stock_id,
        init_bal_id,
        pro_id,
        unit_id,
        quantity,
        price,
        date,
        description
    )
    select stock_id,
           init_bal_id,
           pro_id,
           unit_id,
           dbo.GetTotalQuantityOfItems(pro_id, unit_id, quantity),
           dbo.GetProPriceInSmallUnit(pro_id, unit_id, price),
           (
               select first_period_date from company
           ),
           N' رصيد اول المدة '
    from product_initial_balance_detail
    where (@pro_id is null
          or pro_id = @pro_id)
             --and (dbo.GetPtoductKindByProID(pro_id ) <> 3  )
             and (dbo.GetPtoductKindByProID(pro_id) <> 4)

    -- insert stock permission data where stock relocate from is not null
    insert into @table
    (
        stock_id,
        per_id,
        per_det_id,
        pro_id,
        unit_id,
        quantity,
        price,
        date,
        description
    )
    select dbo.GetStockIDFromByStockPerId(per_id),
           per_id,
           per_detail_id,
           pro_id,
           unit_id,
           dbo.GetTotalQuantityOfItems(pro_id, unit_id, quantity) * -1,
           dbo.GetProPriceInSmallUnit(pro_id, unit_id, price),
           dbo.GetStockPerDateByPerId(per_id),
           N'تحويل من مخزن : ' + dbo.GetStockNameByStockId(dbo.GetStockIDFromByStockPerId(per_id))
    from stock_permission_detail
    where (
              dbo.GetStockPermissionKindByperId(per_id) = 3
              and dbo.GetStockIDFromByStockPerId(per_id) is not null
          )
          and (
                  @pro_id is null
                  or pro_id = @pro_id
              )
          --and (dbo.GetPtoductKindByProID(pro_id ) <> 3  )
          and (dbo.GetPtoductKindByProID(pro_id) <> 4)


    -- insert stock permission data where stock relocate to is not null
    insert into @table
    (
        stock_id,
        per_id,
        per_det_id,
        pro_id,
        unit_id,
        quantity,
        price,
        date,
        description
    )
    select dbo.GetStockIDToByStockPerId(per_id),
           per_id,
           per_detail_id,
           pro_id,
           unit_id,
           dbo.GetTotalQuantityOfItems(pro_id, unit_id, quantity),
           dbo.GetProPriceInSmallUnit(pro_id, unit_id, price),
           dbo.GetStockPerDateByPerId(per_id),
           N'تحويل الي مخزن : ' + dbo.GetStockNameByStockId(dbo.GetStockIDToByStockPerId(per_id))
    from stock_permission_detail
    where (
              dbo.GetStockPermissionKindByperId(per_id) = 3
              and dbo.GetStockIDToByStockPerId(per_id) is not null
          )
          and (
                  @pro_id is null
                  or pro_id = @pro_id
              )
          --and (dbo.GetPtoductKindByProID(pro_id ) <> 3  )
          and (dbo.GetPtoductKindByProID(pro_id) <> 4)

    -- insert stock permission data
    insert into @table
    (
        stock_id,
        per_id,
        per_det_id,
        pro_id,
        unit_id,
        quantity,
        price,
        date,
        description
    )
    select dbo.GetStockIDByStockPerId(per_id),
           per_id,
           per_detail_id,
           pro_id,
           unit_id,
           dbo.SetStockTransactionQuantitySignOfStockPermission(per_id, pro_id, unit_id, quantity),
           dbo.GetProPriceInSmallUnit(pro_id, unit_id, price),
           dbo.GetStockPerDateByPerId(per_id),
           dbo.GetStockPermissionDescriptionByPerId(per_id)
    from stock_permission_detail
    where (
              dbo.GetStockPermissionKindByperId(per_id) = 1
              or dbo.GetStockPermissionKindByperId(per_id) = 2
              or dbo.GetStockPermissionKindByperId(per_id) = 4
              or dbo.GetStockPermissionKindByperId(per_id) = 5
          )
          and (
                  @pro_id is null
                  or pro_id = @pro_id
              )
          --and (dbo.GetPtoductKindByProID(pro_id ) <> 3  )
          and (dbo.GetPtoductKindByProID(pro_id) <> 4)

    -- insert stock permission data
    insert into @table
    (
        stock_id,
        per_id,
        per_det_id,
        pro_id,
        unit_id,
        quantity,
        price,
        date,
        description
    )
    select stock_id,
           manufacture_order_id,
           manufacture_order_raw_detail_id,
           pro_id,
           unit_id,
           quantity * -1,
           dbo.GetProPriceInSmallUnit(pro_id, unit_id, price),
           dbo.GetManufactureOrderDateByOrderID(manufacture_order_id),
           N'صرف في تصنيع الصنف : ' + dbo.GetProName(finished_pro_id)
    from manufacture_order_Raw_detail
    where (
              @pro_id is null
              or pro_id = @pro_id
          )

    -- insert manufacture order
    insert into @table
    (
        stock_id,
        manf_order_id,
        per_det_id,
        pro_id,
        unit_id,
        quantity,
        price,
        date,
        description
    )
    select stock_id,
           manufacture_order_id,
           manufacture_order_finished_detail_id,
           pro_id,
           unit_id,
           default_quantity,
           dbo.GetProPriceInSmallUnit(pro_id, unit_id, unit_cost),
           dbo.GetManufactureOrderDateByOrderID(manufacture_order_id),
           N'امر تصنيع الصنف : ' + dbo.GetProName(pro_id)
    from manufacture_order_Finished_detail
    where (
              @pro_id is null
              or pro_id = @pro_id
          )

    -- insert manufacture order
    insert into @table
    (
        stock_id,
        manf_order_id,
        per_det_id,
        pro_id,
        unit_id,
        quantity,
        price,
        date,
        description
    )
    select stock_id,
           manufacture_order_id,
           manufacture_order_finished_detail_id,
           pro_id,
           unit_id,
           damaged_quantity * -1,
           dbo.GetProPriceInSmallUnit(pro_id, unit_id, unit_cost),
           dbo.GetManufactureOrderDateByOrderID(manufacture_order_id),
           N'هالك في امر تصنيع الصنف: ' + dbo.GetProName(pro_id)
    from manufacture_order_Finished_detail
    where (
              @pro_id is null
              or pro_id = @pro_id
          )
          and damaged_quantity > 0

    -- insert invoice data
    insert into @table
    (
        stock_id,
        inv_id,
        inv_det_id,
        pro_id,
        unit_id,
        quantity,
        price,
        date,
        description,
        notes
    )
    select stock_id,
           inv_id,
           inv_det_id,
           pro_id,
           unit_id,
           dbo.SetStockTransactionQuantitySignOfInvoices(inv_id, pro_id, unit_id, quantity),
           dbo.GetProPriceInSmallUnit(pro_id, unit_id, (price - discount)),
           dbo.GetInvoiceDateByInvId(inv_id),
           dbo.SetStockTransactionDescriptionOfInvoices(inv_id),
           notes
    from invoice_detail
    where (
              @pro_id is null
              or pro_id = @pro_id
          )
          and dbo.GetInvoiceKindByInvId(inv_id) <> 5
          --and (dbo.GetPtoductKindByProID(pro_id ) <> 3  )
          and (dbo.GetPtoductKindByProID(pro_id) <> 4)


    --insert invoice data
    insert into @table
    (
        stock_id,
        inv_id,
        inv_det_id,
        pro_id,
        unit_id,
        quantity,
        date,
        description,
        notes
    )
    select dbo.GetStockIdByInvIdAndInvDetID(inv_id, inv_det_id),
           inv_id,
           inv_det_id,
           pro_raw_id,
           PC.unit_id,
           dbo.SetStockTransactionQuantitySignOfInvoices(inv_id, PC.pro_raw_id, PC.unit_id, (PC.quantity * id.quantity)),
           dbo.GetInvoiceDateByInvId(inv_id),
           N'صرف في تصنيع الصنف : ' + dbo.GetProName(ID.pro_id),
           notes
    from product_collected PC
        join invoice_detail ID
            on PC.pro_id = ID.pro_id
    where (
              @pro_id is null
              or pro_raw_id = @pro_id
          )
          and (dbo.GetInvoiceKindByInvId(inv_id) <> 5)
          and (dbo.GetPtoductKindByProID(PC.pro_id) = 3)

    return
end


GO
/****** Object:  UserDefinedFunction [dbo].[CustomerTransaction]    Script Date: 2022-02-07 3:44:11 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
ALTER function [dbo].[CustomerTransaction] (@cust_id int = null)
returns @table table
(
    inv_id bigint null,
    manf_per_id int null,
    init_bal_id int null,
    per_id int null,
    paper_id int null,
    maintenance_id int null,
    cust_id int,
    type nchar(1) null,
    tran_date datetime null,
    value money,
    description nvarchar(200)
)
as
begin


    -- insert initial balance values
    insert into @table
    (
        init_bal_id,
        cust_id,
        tran_date,
        value,
        description
    )
    select cust_id,
           cust_id,
           date,
           quantity,
           'رصيد افتتاحي'
    from customer_initial_balance
    where @cust_id is null
          or cust_id = @cust_id

    -- insert invoice values
    insert into @table
    (
        inv_id,
        cust_id,
        type,
        tran_date,
        value,
        description
    )
    select inv_id,
           cust_id,
           'i',
           inv_date,
           dbo.GetCustomerTransactionValueOfinvoice(
                                                       inv_id,
                                                       inv_kind_id,
                                                       payment_kind_id,
                                                       dbo.NetTotalOfInvoice(
                                                                                inv_id,
                                                                                [service],
                                                                                discount_kind,
                                                                                discount,
                                                                                discount2,
                                                                                addition,
                                                                                sales_tax
                                                                            )
                                                   ),
           dbo.SetCustomerTransactionDescriptionOfInvoice(inv_id, inv_kind_id, payment_kind_id)
    from invoice_header
    where (
              @cust_id is null
              or cust_id = @cust_id
          )
          and dbo.GetInvoiceKindByInvId(inv_id) <> 5

    -- insert invoice paids values
    insert into @table
    (
        inv_id,
        cust_id,
        type,
        tran_date,
        value,
        description
    )
    select inv_id,
           cust_id,
           dbo.GetCustomerPaidTypeOfInvoice(inv_id, Paid, discount_kind, discount, discount2, addition, sales_tax),
           dbo.SetCustomerTransactionDateOfInvocie(
                                                      inv_id,
                                                      inv_date,
                                                      Paid,
                                                      discount_kind,
                                                      discount,
                                                      discount2,
                                                      addition,
                                                      sales_tax
                                                  ),
           dbo.GetCustomerTransactionPaidOfinvoice(inv_id, inv_kind_id, payment_kind_id, paid),
           dbo.SetCustomerTransactionDescriptionOfInvoicePaid(
                                                                 inv_id,
                                                                 inv_kind_id,
                                                                 payment_kind_id,
                                                                 Paid,
                                                                 discount_kind,
                                                                 discount,
                                                                 discount2,
                                                                 addition,
                                                                 sales_tax
                                                             )
    from invoice_header
    where dbo.GetCustomerPaidTypeOfInvoice(inv_id, Paid, discount_kind, discount, discount2, addition, sales_tax) = 'p'
          and (
                  @cust_id is null
                  or cust_id = @cust_id
              )
          and dbo.GetInvoiceKindByInvId(inv_id) <> 5

    -- insert manufacturing values
    insert into @table
    (
        manf_per_id,
        cust_id,
        tran_date,
        value,
        description
    )
    select manufacture_order_id,
           vendor_id,
           manufacture_date,
           dbo.GetVendorExternalManufacturingValue(manufacture_order_id) * -1,
           'قيمة تصنيع لدي  ' + dbo.GetCustNameById(vendor_id)
    from manufacture_order_header
    where (
              dbo.GetManufactureOrderTypeByManfOrderID(manufacture_order_id) = 3
              or dbo.GetManufactureOrderTypeByManfOrderID(manufacture_order_id) = 4
          )
          and (
                  @cust_id is null
                  or vendor_id = @cust_id
              )

    -- insert installment paids values
    insert into @table
    (
        inv_id,
        cust_id,
        tran_date,
        value,
        description
    )
    select inv_id,
           dbo.GetCustIdByInvId(inv_id) as cust_id,
           collecting_date,
           paid * -1,
           dbo.GetInstallmentDescription(paid_id, inv_id)
    from Installment_paids
    where (
              @cust_id is null
              or dbo.GetCustIdByInvId(inv_id) = @cust_id
          )

    --  insert treasury permission values
    insert into @table
    (
        per_id,
        cust_id,
        tran_date,
        value,
        description
    )
    select per_id,
           cust_id,
           per_date,
           dbo.GetCustomerTransactionValueOfTreasuryPermission(per_id, per_kind_id, value),
           dbo.GetCustomerTransactionDescriptionOfTreasuryPermission(per_id, per_kind_id, cust_id, is_discount)
    from treasury_permission
    where (
              @cust_id is null
              or cust_id = @cust_id
          )

    -- insert papers values
    insert into @table
    (
        paper_id,
        cust_id,
        tran_date,
        value,
        description
    )
    select paper_id,
           cust_id,
           edit_date,
           dbo.GetCustomerTRansactionValueOfPaper(paper_id, paper_type, value),
           dbo.GetCustomerTransactionDescriptionOfPaper(paper_id, paper_type)
    from paper
    where (
              @cust_id is null
              or cust_id = @cust_id
          )
          and paper_status <> 3

    -- insert maintenance values
    insert into @table
    (
        maintenance_id,
        cust_id,
        tran_date,
        value,
        description
    )
    select maintenance_id,
           cust_id,
           draw_date,
           maintenance_indexation,
           'قيمة عملية صيانة رقم  ' + cast(maintenance_id as nvarchar)
    from maintenance
    where (
              @cust_id is null
              or cust_id = @cust_id
          )

    -- insert maintenance payment values
    insert into @table
    (
        maintenance_id,
        cust_id,
        tran_date,
        value,
        description
    )
    select maintenance_id,
           cust_id,
           delivery_date,
           maintenance_indexation * -1,
           'سداد قيمة عملية صيانة رقم  ' + cast(maintenance_id as nvarchar)
    from maintenance
    where (
              @cust_id is null
              or cust_id = @cust_id
          )
          and (is_paid = 1)
    return
end

go

create proc [dbo].[RPTItemsSummaryResultPerStock]
(
	@StockID int = null,
	@CategoryID int = null,
	@ProID int = null,
	@ToDate datetime = null
)
as
begin
	select pro_id,
		   dbo.GetProName(pro_id) as Pro_Name,
		   sum(case when quantity > 0 then quantity else 0 end) as 'Total_Imported' ,
		   sum(case when quantity < 0 then quantity*-1 else 0 end) as 'Total_Exported', 
		   sum(quantity) as 'CurrentAmount',
		   sum(case when quantity < 0 then quantity*-1 * price else 0 end) as 'Total_Sales_Value',
		   sum(case when quantity < 0 then (quantity*-1 *cost_price) else 0 end) as 'Total_Purchased_Value',
		   
		   sum(case when quantity < 0 then (quantity*-1 * price) - (quantity*-1 *cost_price) else 0 end) as 'Total_Profit'
	from dbo.CalculateProductsCostsAndBalances(@ProID,@CategoryID, @ToDate)
	where (@StockID is null or stock_id = @StockID) 
	group by pro_id
end
