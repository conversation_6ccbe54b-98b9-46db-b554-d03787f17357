﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ServeIt.BL
{
    class IncomeStatementBL
    {
        public DataTable RPTIncomeStatement(BE.IncomeStatement inc_statmnt)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[3];
                para[0] = new SqlParameter("@datefrom",inc_statmnt.DateFrom);
                para[1] = new SqlParameter("@dateto",inc_statmnt.DateTo);
                para[2] = new SqlParameter("@pricetype",inc_statmnt.Price_Type);
                return DAL.GetData("RPTIncomeStatement", para);
            }
        }
    }
}
