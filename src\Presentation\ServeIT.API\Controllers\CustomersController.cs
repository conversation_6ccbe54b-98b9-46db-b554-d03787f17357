using Microsoft.AspNetCore.Mvc;
using ServeIT.API.Services;
using ServeIT.Models.DTOs;
using ServeIT.Models.Common;

namespace ServeIT.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class CustomersController : ControllerBase
{
    private readonly ICustomerService _customerService;

    public CustomersController(ICustomerService customerService)
    {
        _customerService = customerService;
    }

    [HttpGet]
    public async Task<ActionResult<ServiceResult<IEnumerable<CustomerDto>>>> GetAllCustomers()
    {
        var result = await _customerService.GetAllCustomersAsync();
        return Ok(result);
    }

    [HttpGet("active")]
    public async Task<ActionResult<ServiceResult<IEnumerable<CustomerDto>>>> GetActiveCustomers()
    {
        var result = await _customerService.GetActiveCustomersAsync();
        return Ok(result);
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<ServiceResult<CustomerDto>>> GetCustomer(int id)
    {
        var result = await _customerService.GetCustomerByIdAsync(id);
        
        if (!result.IsSuccess)
        {
            return NotFound(result);
        }

        return Ok(result);
    }

    [HttpGet("mobile/{mobile}")]
    public async Task<ActionResult<ServiceResult<CustomerDto>>> GetCustomerByMobile(string mobile)
    {
        var result = await _customerService.GetCustomerByMobileAsync(mobile);
        
        if (!result.IsSuccess)
        {
            return NotFound(result);
        }

        return Ok(result);
    }

    [HttpGet("paged")]
    public async Task<ActionResult<ServiceResult<PagedResult<CustomerDto>>>> GetCustomersPaged([FromQuery] PagingParameters parameters)
    {
        var result = await _customerService.GetCustomersPagedAsync(parameters);
        return Ok(result);
    }

    [HttpGet("search")]
    public async Task<ActionResult<ServiceResult<PagedResult<CustomerDto>>>> SearchCustomers(
        [FromQuery] string searchTerm, 
        [FromQuery] PagingParameters parameters)
    {
        var result = await _customerService.SearchCustomersAsync(searchTerm, parameters);
        return Ok(result);
    }

    [HttpPost]
    public async Task<ActionResult<ServiceResult<CustomerDto>>> CreateCustomer([FromBody] CustomerDto customerDto)
    {
        if (!ModelState.IsValid)
        {
            var errors = ModelState.Values
                .SelectMany(v => v.Errors)
                .Select(e => e.ErrorMessage)
                .ToList();
            
            return BadRequest(ServiceResult<CustomerDto>.Failure("Validation failed", errors));
        }

        var result = await _customerService.CreateCustomerAsync(customerDto);
        
        if (!result.IsSuccess)
        {
            return BadRequest(result);
        }

        return CreatedAtAction(nameof(GetCustomer), new { id = result.Data!.Id }, result);
    }

    [HttpPut("{id}")]
    public async Task<ActionResult<ServiceResult<CustomerDto>>> UpdateCustomer(int id, [FromBody] CustomerDto customerDto)
    {
        if (!ModelState.IsValid)
        {
            var errors = ModelState.Values
                .SelectMany(v => v.Errors)
                .Select(e => e.ErrorMessage)
                .ToList();
            
            return BadRequest(ServiceResult<CustomerDto>.Failure("Validation failed", errors));
        }

        var result = await _customerService.UpdateCustomerAsync(id, customerDto);
        
        if (!result.IsSuccess)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    [HttpDelete("{id}")]
    public async Task<ActionResult<ServiceResult>> DeleteCustomer(int id)
    {
        var result = await _customerService.DeleteCustomerAsync(id);
        
        if (!result.IsSuccess)
        {
            return NotFound(result);
        }

        return Ok(result);
    }

    [HttpGet("check-mobile/{mobile}")]
    public async Task<ActionResult<ServiceResult<bool>>> CheckMobileUnique(string mobile, [FromQuery] int? excludeId = null)
    {
        var result = await _customerService.IsMobileUniqueAsync(mobile, excludeId);
        return Ok(result);
    }
}
