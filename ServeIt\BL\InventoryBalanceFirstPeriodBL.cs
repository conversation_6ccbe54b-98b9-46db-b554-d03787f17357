﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ServeIt.BL
{
    class InventoryBalanceFirstPeriodBL
    {


        public DataTable GetAllInventoryFirstPeriodPermission()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetData("getallInventoryfirstperiodpermission", null);
            }
        }

        public DataTable GetProductFirstPeriodBalanceByStockID(string StoreID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@stock_id", StoreID);
                return DAL.GetData("GetProductFirstPeriodBalancesByStockID",para);
            }
        }

        public string AddOrUpdateInventoryFirstPeriodPermission(BE.InventoryBalanceFirstBeriodBE inv_bal_fir_per) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[9];
                para[0] = new SqlParameter("@check", "a");
                para[1] = new SqlParameter("@init_bal_id", inv_bal_fir_per.Init_Bal_Id);
                para[2] = new SqlParameter("@stock_id", inv_bal_fir_per.Stock_Id);
                para[3] = new SqlParameter("@pro_id", inv_bal_fir_per.Pro_Id);
                para[4] = new SqlParameter("@pro_serial", inv_bal_fir_per.ProSerial);
                para[5] = new SqlParameter("@pro_expirey", inv_bal_fir_per.ProExpirey);
                para[6] = new SqlParameter("@unit_id", inv_bal_fir_per.Unit_ID);
                para[7] = new SqlParameter("@quantity", inv_bal_fir_per.Quantity);
                para[8] = new SqlParameter("@price", inv_bal_fir_per.Price);
                return DAL.InsUpdDel("manageproductinitialbalance", para);
            }
        }


        public string GetMaxInventoryFirstPeriodId() 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@check", "g");
                return DAL.GetValue("manageproductinitialbalance",para);
            }
        }

        public DataTable GetDefaultStock() 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetData("getdefaultstock", null);
            }
        }


        public DataTable GetProductInitialBalanceHeaderByIntitBalID(string InitBalID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@init_bal_id", InitBalID);
                return DAL.GetData("GetProductInitialBalanceHeaderByInitBalID", para);
            }
        }

        public DataTable GetInventoryFirstPeriodDetailsOfPermission(string inv_bal_fir_per_id) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@init_bal_id", inv_bal_fir_per_id);
                return DAL.GetData("getproinitialbalancedetail", para);
            }
        }


        public string DeleteInventoryFirstPeriodDetailRow(BE.InventoryBalanceFirstBeriodBE inv_bal_fir_per) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[3];
                para[0] = new SqlParameter("@check", "d");
                para[1] = new SqlParameter("@init_bal_id", inv_bal_fir_per.Init_Bal_Id);
                return DAL.InsUpdDel("manageproductinitialbalance", para);
            }
        }

    }
}
