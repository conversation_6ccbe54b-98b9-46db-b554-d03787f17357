﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ServeIt.BL
{
    class RevenueBL
    {
        public int RevenueID { get; set; }
        public int? ParentID { get; set; }
        public string RevenueName { get; set; }
        public string Notes { get; set; }
        public string UserName { get; set; }


        public string AddOrUpdateRevenue(RevenueBL rev_bl)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[6];
                para[0] = new SqlParameter("@check", "a");
                para[1] = new SqlParameter("@revenue_id", rev_bl.RevenueID);
                para[2] = new SqlParameter("@parent_id", rev_bl.ParentID);
                para[3] = new SqlParameter("@revenue_name", rev_bl.RevenueName);
                para[4] = new SqlParameter("@notes", rev_bl.Notes);
                para[5] = new SqlParameter("@user_name", rev_bl.UserName);
                return DAL.InsUpdDel("ManageRevenuesItems", para);
            }
        }

        public string DeleteRevenue(string RevenueID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "d");
                para[1] = new SqlParameter("@revenue_id", RevenueID);
                return DAL.InsUpdDel("ManageRevenuesItems", para);
            }
        }

        public DataTable GetPreviousRevenueID(string CurrentID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@current_id", CurrentID);
                return DAL.GetData("GetPreviousRevenueItemID", para);
            }
        }

        public DataTable GetNextRevenueID(string CurrentID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@current_id", CurrentID);
                return DAL.GetData("GetNextRevenueItemID", para);
            }
        }

        public string GetMaxRevenueID()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@check", 'm');
                return DAL.GetValue("ManageRevenuesItems", para);
            }
        }

        public DataTable SearchRevenues(string RevenueName)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", 's');
                para[1] = new SqlParameter("@revenue_name", RevenueName);

                return DAL.GetData("ManageRevenuesItems", para);
            }
        }


        public DataTable GetAllRevenue()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetData("GetAllRevenueItems", null);
            }
        }

        public DataTable GetRevenuesNodes(string ParentID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@parent_id", ParentID);
                return DAL.GetData("GetRevenuesNodes", para);
            }
        }

        public DataTable GetRevenueItemByRevenueId(string RevenueID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "g");
                para[1] = new SqlParameter("@revenue_id", RevenueID);
                return DAL.GetData("ManageRevenuesItems", para);
            }
        }

        public DataTable GetChildRevenues()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetData("GetChildRevenues", null);
            }
        }
    }
}
