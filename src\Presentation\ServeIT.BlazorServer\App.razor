@using Blazored.Modal
@using Blazored.Toast

<CascadingBlazoredModal>
    <BlazoredToasts />

    <Router AppAssembly="@typeof(App).Assembly">
        <Found Context="routeData">
            <RouteView RouteData="@routeData" DefaultLayout="@typeof(MainLayout)" />
            <FocusOnNavigate RouteData="@routeData" Selector="h1" />
        </Found>
        <NotFound>
            <PageTitle>Not found</PageTitle>
            <LayoutView Layout="@typeof(MainLayout)">
                <div class="container-fluid">
                    <div class="row justify-content-center">
                        <div class="col-md-6 text-center">
                            <h1 class="display-1 text-muted">404</h1>
                            <h2>Page Not Found</h2>
                            <p class="lead">Sorry, the page you are looking for could not be found.</p>
                            <a href="/" class="btn btn-primary">
                                <i class="bi bi-house"></i> Go Home
                            </a>
                        </div>
                    </div>
                </div>
            </LayoutView>
        </NotFound>
    </Router>
</CascadingBlazoredModal>
