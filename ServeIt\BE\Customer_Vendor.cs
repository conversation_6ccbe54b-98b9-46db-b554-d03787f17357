﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ServeIt.BE
{
    class Customer_Vendor
    {
        public string CustomerID { set; get; }
        public string CustomerName{set; get;}
        public DateTime CustomerAddingDate { get; set; }
        public string CustomerKind { set; get; }
        public decimal CustomerInitialBalance { set; get; }
        public string CustomerPriceSystem { get; set; }
        public byte[] CustomerPicture { set; get; }
        public string CustomerCity { set; get; }
        public string CustomerArea { set; get; }
        public string CustomerAddress { set; get; }
        public string CustomerMobile { set; get; }
        public string CustomerPhone { set; get; }
        public string CustomerRepresentativeID { set; get; }
        public bool CustomerIsActive { set; get; }
        public bool DefaultCustomer { set; get; }
        public string CustomerNotes { set; get; }
        public string CustomerUserName { get; set; }
        public DateTime? CustomerDateFrom { get; set; }
        public DateTime? CustomerDateTo { get; set; }
        public bool CustomerShowZeroAccounts { get; set; }

        public string CustomerBox1Value { get; set; }
        public string CustomerBox2Value { get; set; }
    }
}
