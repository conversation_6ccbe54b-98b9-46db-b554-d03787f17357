﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ServeIt.BE
{
    class ProductBE
    {

        // product properties

        #region  Product Properties
        public int ProKind { set; get; }
        public string ProID { set; get; }
        public string ProID2 { set; get; }
        public string Pro_No { set; get; }
        public string ProName { set; get; }
        public string ProSerial { set; get; }
        public string Cat_Id { set; get; }
        public string Manfacture_Country { set; get; }
        public string Store_Place { get; set; }
        public decimal VAT { get; set; }
        public byte[] image { set; get; }
        public decimal Request_limit { set; get; }
        public string DefaultStock { get; set; }
        public string ProRawDetID { set; get; }
        public string ProRawID { set; get; }
        public string ProRawUnitID { set; get; }
        public decimal ProRawQuantity { set; get; }
        public decimal ProRawPrice { set; get; }
        public int Unit_Id { get; set; }
        public string Unit_Name { get; set; }
        public string Unit_Kind { get; set; }
        public decimal Unit_Count { get; set; }
        public decimal Purchase_Price { get; set; }
        public decimal Wholsale_Price { get; set; }
        public decimal Half_Wholsale_Price { get; set; }
        public decimal Retail_Price { get; set; }
        public decimal LowestPrice { get; set; }
        public decimal HighestPrice { get; set; }
        public string Discount { get; set; }
        public bool DefaultPurchaseUnit { get; set; }
        public bool DefaultSaleUnit { get; set; }
        public string InternationalBarcode { get; set; }
        public string SystemBarcode { get; set; }
        public bool IsActive { get; set; }
        public string Notes { set; get; }
        protected internal string User_Name { get; set; }
        public int BarcodeCount { set; get; }

        public int Price_Type { get; set; }

        public string Price_System { get; set; }
        public DateTime? DateFrom { get; set; }
        public DateTime? DateTo { get; set; }
        #endregion
    }
}
