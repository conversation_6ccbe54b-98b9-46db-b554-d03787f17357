using System.ComponentModel.DataAnnotations;

namespace ServeIT.Models.Entities;

public class SalesRepresentative : BaseEntity<int>
{
    [Required]
    [StringLength(100)]
    public string Name { get; set; } = string.Empty;

    public long? NationalId { get; set; }

    [StringLength(500)]
    public string? Address { get; set; }

    [StringLength(20)]
    public string? Mobile { get; set; }

    [StringLength(20)]
    public string? Phone { get; set; }

    [StringLength(1000)]
    public string? Notes { get; set; }

    // Navigation Properties
    public virtual ICollection<Customer> Customers { get; set; } = new List<Customer>();
    public virtual ICollection<Invoice> Invoices { get; set; } = new List<Invoice>();
}
