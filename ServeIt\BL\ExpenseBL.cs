﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ServeIt.BL
{
    class ExpenseBL
    {
        public int ExpenseID { get; set; }
        public int? ParentID { get; set; }
        public string ExpenseName { get; set; }
        public string Notes { get; set; }
        public string UserName { get; set; }

        public string AddOrUpdateExpense(ExpenseBL exp_bl)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[6];
                para[0] = new SqlParameter("@check", "a");
                para[1] = new SqlParameter("@expense_id", exp_bl.ExpenseID);
                para[2] = new SqlParameter("@parent_id", exp_bl.ParentID);
                para[3] = new SqlParameter("@expense_name", exp_bl.ExpenseName);
                para[4] = new SqlParameter("@notes", exp_bl.Notes);
                para[5] = new SqlParameter("@user_name", exp_bl.UserName);
                return DAL.InsUpdDel("ManageExpenses", para);
            }
        }

        public string DeleteExpense(string ExpenseID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "d");
                para[1] = new SqlParameter("@expense_id", ExpenseID);
                return DAL.InsUpdDel("ManageExpenses", para);
            }
        }

        public DataTable GetPreviousExpenseID(string CurrentID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@current_id", CurrentID);
                return DAL.GetData("GetPreviousExpenseItemID", para);
            }
        }

        public DataTable GetNextExpenseID(string CurrentID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@current_id", CurrentID);
                return DAL.GetData("GetNextExpenseItemID", para);
            }
        }

        public string GetMaxExpenseID()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@check", 'm');
                return DAL.GetValue("ManageExpenses", para);
            }
        }

        public DataTable SearchExpenses(string ExpenseName)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", 's');
                para[1] = new SqlParameter("@expense_name", ExpenseName);

                return DAL.GetData("ManageExpenses", para);
            }
        }


        public DataTable GetAllExpenses()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetData("GetAllExpenses", null);
            }
        }

        public DataTable GetExpensesNodes(string ParentID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@parent_id", ParentID);
                return DAL.GetData("GetExpensesNodes", para);
            }
        }

        public DataTable GetExpenseByExpenseId(string ExpenseID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "g");
                para[1] = new SqlParameter("@expense_id", ExpenseID);
                return DAL.GetData("ManageExpenses", para);
            }
        }

        public DataTable GetChildExpenses()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetData("GetChildExpenses", null);
            }
        }

        //public DataTable RPTExpensesAnalysis(BE.ExpenseBE expbe)
        //{
        //    using (DA.DataAccess DAL = new DA.DataAccess())
        //    {
        //        SqlParameter[] para = new SqlParameter[5];
        //        para[0] = new SqlParameter("@expense_kind_id", expbe.ExpenseKindID);
        //        para[1] = new SqlParameter("@sub_expense_name", expbe.Sub_Expense_Name);
        //        para[2] = new SqlParameter("@treas_id", expbe.Treasury_Id);
        //        para[3] = new SqlParameter("@datefrom", expbe.DataFrom);
        //        para[4] = new SqlParameter("@dateto", expbe.DateTo);
        //        return DAL.GetData("RPTExpensesAnalysing", para);
        //    }
        //}
    }
}
