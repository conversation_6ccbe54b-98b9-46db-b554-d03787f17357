﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;

namespace ServeIt.BL
{
    class ManufactureOrderDetailsFinishedProductBL
    {
        public string ManufactureOrderID { get; set; }
        public string ManufactureOrderDetailFininshedID { get; set; }
        public string ProID { get; set; }
        public string ProBatchNo { get; set; }
        public string ProSerial { get; set; }
        public DateTime? ProExpirey { get; set; }
        public string UnitID { get; set; }
        public string StockID { get; set; }
        public decimal DefaultQuantity { get; set; }
        public decimal DamagedQuantity { get; set; }
        public decimal Quantity { get; set; }
        public decimal Price { get; set; }
        public decimal Expenses { get; set; }
        public decimal ExternalManufacturingCost { get; set; }
        public decimal TotalCost { get; set; }
        public decimal UnitCost { get; set; }
        public decimal RunHours { get; set; }
        public decimal HourValue { get; set; }

        public string AddOrUpdateManufactureOrderDetailsFinishedProduct(ManufactureOrderDetailsFinishedProductBL manf_detail_fnshd_bl)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[19];
                para[0] = new SqlParameter("@check", "a");
                para[1] = new SqlParameter("@manufacture_order_id", manf_detail_fnshd_bl.ManufactureOrderID);
                para[2] = new SqlParameter("@manufacture_order_finished_detail_id", manf_detail_fnshd_bl.ManufactureOrderDetailFininshedID);
                para[3] = new SqlParameter("@pro_id", manf_detail_fnshd_bl.ProID);
                para[4] = new SqlParameter("@pro_serial", manf_detail_fnshd_bl.ProSerial);
                para[5] = new SqlParameter("@pro_expirey", manf_detail_fnshd_bl.ProExpirey);
                para[6] = new SqlParameter("@batch_no", manf_detail_fnshd_bl.ProBatchNo);
                para[7] = new SqlParameter("@unit_id", manf_detail_fnshd_bl.UnitID);
                para[8] = new SqlParameter("@stock_id", manf_detail_fnshd_bl.StockID);
                para[9] = new SqlParameter("@default_quantity", manf_detail_fnshd_bl.DefaultQuantity);
                para[10] = new SqlParameter("@damaged_quantity", manf_detail_fnshd_bl.DamagedQuantity);
                para[11] = new SqlParameter("@quantity", manf_detail_fnshd_bl.Quantity);
                para[12] = new SqlParameter("@price", manf_detail_fnshd_bl.Price);
                para[13] = new SqlParameter("@expenses", manf_detail_fnshd_bl.Expenses);
                para[14] = new SqlParameter("@external_manf_cost", manf_detail_fnshd_bl.ExternalManufacturingCost);
                para[15] = new SqlParameter("@total_cost", manf_detail_fnshd_bl.TotalCost);
                para[16] = new SqlParameter("@unit_cost", manf_detail_fnshd_bl.UnitCost);
                para[17] = new SqlParameter("@run_hours", manf_detail_fnshd_bl.RunHours);
                para[18] = new SqlParameter("@hour_value", manf_detail_fnshd_bl.HourValue);
                return DAL.InsUpdDel("ManageManufactureOrderFinishedDetails", para);
            }
        }

        public string DeleteManufactureOrderDetailsFinishedRow(string ManufactureOrderID, string ManufactureOrderDetailFininshedID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[3];
                para[0] = new SqlParameter("@check", "d");
                para[1] = new SqlParameter("@manufacture_order_id", ManufactureOrderID);
                para[2] = new SqlParameter("@manufacture_order_finished_detail_id", ManufactureOrderDetailFininshedID);
                return DAL.InsUpdDel("ManageManufactureOrderFinishedDetails", para);
            }
        }

        public string DeleteManufactureOrderDetailsFinished(string ManufactureOrderID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "r");
                para[1] = new SqlParameter("@manufacture_order_id", ManufactureOrderID);
                return DAL.InsUpdDel("ManageManufactureOrderFinishedDetails", para);
            }
        }

        public DataTable GetManufactureOrderDetailsFinishedByID(string ManufactureOrderID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@manufacture_order_id", ManufactureOrderID);
                return DAL.GetData("GetManufactureOrderFinishedProductDetailByOrderID", para);
            }
        }

        public DataTable GetManufactureOrderDetailsFinishedAndStockByOrderID(string ManufactureOrderID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@manufacture_order_id", ManufactureOrderID);
                return DAL.GetData("GetManufactureOrderFinishedProductDetailAndStockByOrderID", para);
            }
        }
    }
}
