﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace ServeIt.PL.difinition
{
    public partial class sales_rep_new : Form
    {
        BE.SalesRepresentativeBE salrepbe;
        BL.SalesRepresentativeBL salrepbl;
        
        public sales_rep_new()
        {
            InitializeComponent();
        }

       
        private void SaveSalesRep() 
        {
            if (string.IsNullOrEmpty(TxtName.Text))
            {
                toolStripStatusLabel1.Text = "لم يتم ادخال اسم المندوب, من فضلك ادخل اسم المندوب اولا";
                timer1.Start();
            }
            else if (string.IsNullOrEmpty(TxtNatId.Text))
            {
                toolStripStatusLabel1.Text = "لم يتم ادخال الرقم القومي, من فضلك ادخل الرقم القومي اولا";
                timer1.Start();
            }
            else
            {
                salrepbe = new BE.SalesRepresentativeBE();
                salrepbl = new BL.SalesRepresentativeBL();
                string result;
                if (label1.Text.Contains("تعديل"))
                {
                    salrepbe.ID = TxtId.Text;
                }
                else
                {
                    salrepbe.ID = (int.Parse(salrepbl.GetMaxSalesRepresentativeId()) + 1).ToString();
                }
                salrepbe.Name = TxtName.Text;
                salrepbe.NationalID = Int64.Parse(TxtNatId.Text);
                salrepbe.Address = TxtAddress.Text;
                salrepbe.Mobile = TxtMobile.Text;
                salrepbe.Phone = TxtPhone.Text;
                salrepbe.Notes = TxtNote.Text;
                salrepbe.UserName = Form1.CheckIns.UserName;
                result = salrepbl.AddOrUpdateSAlesRepresentative(salrepbe);
                if (result == "success")
                {
                    toolStripStatusLabel1.Text = "تم الحفظ بنجاح";
                    timer1.Start();
                    
                        NewRep();
                   
                    
                    if (sales_rep.CheckIns != null)
                    {
                        sales_rep.CheckIns.toolStripStatusLabel1.Text = "تم الحفظ بنجاح";
                        sales_rep.CheckIns.SearchSalesRepresentative();
                        sales_rep.CheckIns.timer1.Start();
                    }
                    
                }
                else
                {
                    toolStripStatusLabel1.Text = "فشل الحفظ : " + result;
                    timer1.Start();
                }
            }
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            SaveSalesRep();
        }

        void NewRep() 
        {
            foreach (Control C in this.Controls)
            {
                if (C is Panel)
                {
                    foreach (Control t in C.Controls)
                    {
                        if (t is TextBox)
                        {
                            ((TextBox)t).Text = string.Empty;
                        }
                    }
                }
                else if (C is TextBox)
                {
                    ((TextBox)C).Text = string.Empty;
                }
            }
            label1.Text = "اضافة مندوب : جديد";
            this.Text = "اضافة مندوب : جديد";
            UnlockRep();
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            toolStripStatusLabel1.Text = string.Empty;
            timer1.Stop();
        }


        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void sales_rep_new_Load(object sender, EventArgs e)
        {
            PL.PLUtilities pl_utl = new PL.PLUtilities();
            pl_utl.ALteringNavigationControlWhenChangeLanguage(BtnPrevious, BtnNext);

            string RepID = label1.Text.Contains("تعديل")? TxtId.Text:null;
            LoadSaleRepresentativeDetailsByID(RepID);
            ActiveControl = TxtName;
        }

        private void BtnNew_Click(object sender, EventArgs e)
        {
            NewRep();
        }

        private void DeleteSalesRep() 
        {
            if (!string.IsNullOrEmpty(TxtId.Text))
            {
                if (MessageBox.Show("هل تريد فعلا حذف المندوب المحدد؟", "عملية الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Exclamation) == DialogResult.Yes)
                {
                    string result;
                    salrepbe = new BE.SalesRepresentativeBE();
                    salrepbl = new BL.SalesRepresentativeBL();
                    salrepbe.ID = TxtId.Text;
                    result = salrepbl.DeleteSalesRepresentative(salrepbe);
                    if (result == "success")
                    {
                        if (sales_rep.CheckIns != null)
                        {
                            sales_rep.CheckIns.SearchSalesRepresentative();
                        }
                        NewRep();
                        toolStripStatusLabel1.Text = "تم حذف المندوب بنجاح";
                        timer1.Start();
                    }
                    else
                    {
                        toolStripStatusLabel1.Text = " فشل الحذف" + result;
                        timer1.Start();
                    }
                }
            }
        }

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            DeleteSalesRep();
        }

        private void UnlockRep()
        {
            panel2.Enabled = true;
            BtnSave.Enabled = true;
            BtnEdit.Enabled = false;
            BtnDelete.Enabled = true;
        }

        public void LockRep()
        {
            panel2.Enabled = false;
            BtnSave.Enabled = false;
            BtnEdit.Enabled = true;
            BtnDelete.Enabled = true;
        }

        private void LoadSaleRepresentativeDetailsByID(string SaleRepID)
        {
            salrepbl = new BL.SalesRepresentativeBL();
            DataTable RepDetailsDT = salrepbl.GetSaleRepresentativeDetailsByID(SaleRepID);
            if (RepDetailsDT.Rows.Count > 0)
            {
                TxtName.Text = RepDetailsDT.Rows[0]["sales_rep_name"].ToString();
                TxtNatId.Text = RepDetailsDT.Rows[0]["national_id"].ToString();
                TxtAddress.Text = RepDetailsDT.Rows[0]["address"].ToString();
                TxtMobile.Text = RepDetailsDT.Rows[0]["mobile"].ToString();
                TxtPhone.Text = RepDetailsDT.Rows[0]["phone"].ToString();
                TxtNote.Text = RepDetailsDT.Rows[0]["notes"].ToString();
                LockRep();
            }
        }

        private void LoadPreviousRep()
        {
            string CurrentInvID = string.IsNullOrEmpty(TxtId.Text) ? null : TxtId.Text;
            string PreviousID = string.Empty;
            salrepbl = new BL.SalesRepresentativeBL();
            DataTable RepIDDT = salrepbl.GetPreviousSaleRepresentativeID(CurrentInvID);
            if (RepIDDT.Rows.Count > 0)
            {

                PreviousID = RepIDDT.Rows[0]["previous_id"].ToString();
                if (!string.IsNullOrEmpty(PreviousID))
                {
                    label1.Text = "تعريف مندوب : تعديل";
                    TxtId.Text = PreviousID;
                    LoadSaleRepresentativeDetailsByID(PreviousID);
                    LockRep();
                    BtnNext.Enabled = true;
                }
                else
                {
                    MessageBox.Show("عفوا, لا يوجد مناديب سابقة");
                    BtnPrevious.Enabled = false;
                }
            }
        }

        private void LoadNextRep()
        {
            string CurrentInvID = string.IsNullOrEmpty(TxtId.Text) ? null : TxtId.Text;
            string NextID = string.Empty;
            salrepbl = new BL.SalesRepresentativeBL();
            DataTable RepIDDT = salrepbl.GetNextSaleRepresentativeID(CurrentInvID);
            if (RepIDDT.Rows.Count > 0)
            {
                NextID = RepIDDT.Rows[0]["next_id"].ToString();
                if (!string.IsNullOrEmpty(NextID))
                {
                    label1.Text = "تعريف مندوب : تعديل";
                    TxtId.Text = NextID;
                    LoadSaleRepresentativeDetailsByID(NextID);
                    LockRep();
                    BtnPrevious.Enabled = true;
                }
                else
                {
                    MessageBox.Show("عفوا, لا يوجد مناديب تالية");
                    BtnNext.Enabled = false;
                }
            }
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            using (PL.ALter_Delete_Password_Form.CreateIns)
            {
                if (Properties.Settings.Default.application_dispaly_language == "العربية")
                {
                    PL.ALter_Delete_Password_Form.CheckIns.label1.Text = "كلمة سر التعديل";
                }
                else if (Properties.Settings.Default.application_dispaly_language == "English")
                {
                    PL.ALter_Delete_Password_Form.CheckIns.label1.Text = "Alter Password";
                }
                if (PL.ALter_Delete_Password_Form.CheckIns.ShowDialog() == DialogResult.OK)
                {
                    UnlockRep();
                }
            }
        }

        private void BtnRepresentativesArchive_Click(object sender, EventArgs e)
        {
            if (sales_rep.CheckIns == null)
            {
                sales_rep.CreateIns.Show();
            }
            else
            {
                sales_rep.CreateIns.Focus();
            }
        }

        private void BtnPrevious_Click(object sender, EventArgs e)
        {
            LoadPreviousRep();
        }

        private void BtnNext_Click(object sender, EventArgs e)
        {
            LoadNextRep();
        }

        protected override bool ProcessCmdKey(ref Message msg, Keys keyData)
        {
            switch (keyData)
            {
                case Keys.F10:
                    SaveSalesRep();
                    break;
                case Keys.F1:
                    NewRep();
                    break;
                case Keys.F3:
                    DeleteSalesRep();
                    break;
                case Keys.Escape:
                    this.Close();
                    break;
                default:
                    break;
            }
            return base.ProcessCmdKey(ref msg, keyData);
        }
    }
}
