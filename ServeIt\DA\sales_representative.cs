//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace ServeIt.DA
{
    using System;
    using System.Collections.Generic;
    
    public partial class sales_representative
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public sales_representative()
        {
            this.customer_vendor = new HashSet<customer_vendor>();
            this.invoice_header = new HashSet<invoice_header>();
        }
    
        public int sales_rep_id { get; set; }
        public string sales_rep_name { get; set; }
        public Nullable<long> national_id { get; set; }
        public string address { get; set; }
        public string mobile { get; set; }
        public string phone { get; set; }
        public string notes { get; set; }
        public string user_name { get; set; }
    
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<customer_vendor> customer_vendor { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<invoice_header> invoice_header { get; set; }
    }
}
