﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ServeIt.BL
{
    class Category
    {

        //      methods managing categories

        #region Manage Category

        // function to add new category

        public string AddOrUpdateCategory(BE.Category mycategory)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[7];
                para[0] = new SqlParameter("@check", "a");
                para[1] = new SqlParameter("@cat_id", mycategory.CatID);
                para[2] = new SqlParameter("@parent_id", mycategory.ParentID);
                para[3] = new SqlParameter("@cat_name", mycategory.CatName);
                para[4] = new SqlParameter("@is_displayed", mycategory.IsDisplayed);
                para[5] = new SqlParameter("@description", mycategory.Description);
                para[6] = new SqlParameter("@user_name", mycategory.User_Name);
                return DAL.InsUpdDel("managecategory", para);
            }
        }


        // get max category id to set new category id

        public string GetMaxCatId()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetValue("getmaxcatid");
            }
        }



        // Function to delete chosen category
        public string DeleteCategory(BE.Category mycategory)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[3];
                para[0] = new SqlParameter("@check", "d");
                para[1] = new SqlParameter("@cat_id", mycategory.CatID);
                para[2] = new SqlParameter("@user_name", mycategory.User_Name);
                return DAL.InsUpdDel("managecategory", para);
            }
        }

        public DataTable GetPreviousCategoryID(string CurrentID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@current_id", CurrentID);
                return DAL.GetData("GetPreviousCategoryID", para);
            }
        }

        public DataTable GetNextCategoryID(string CurrentID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@current_id", CurrentID);
                return DAL.GetData("GetNextCategoryID", para);
            }
        }



        // function to get or retreive all my categories

        public DataTable GetAllCat()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetData("getallcat", null);
            }
        }

        public DataTable GetMainCategories() 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetData("GetMainCategories",null);
            }
        }

        public DataTable GetDisplayedCategories()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetData("GetDisplayedCategory", null);
            }
        }

        // function to search for category by name

        public DataTable SearchCat(string CatName)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@cat_name", CatName);
                return DAL.GetData("searchcategory", para);
            }
        }

        public DataTable GetCategoryNodes(string ParentID) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@parent_id", ParentID);
                return DAL.GetData("GetCategoryNodes", para);
            }
        }

        public DataTable GetCategoryByID(string CatID) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@cat_id", CatID);
                return DAL.GetData("GetCategoryByID", para);
            }
        }

        #endregion
    }
}
