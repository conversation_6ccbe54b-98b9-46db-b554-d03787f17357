﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ServeIt.BE
{
    class StockPermissionBE
    {
        public string PerID { get; set; }
        public string MaintenanceID { get; set; }
        public int? PerNo { get; set; }
        public int PerKind { get; set; }
        public int? StockId { get; set; }
        public int? StockIdFrom { get; set; }
        public int? StockIdTo { get; set; }
        public DateTime PerDate { get; set; }
        public string Note { get; set; }
        public string User_Name { get; set; }
        public int PerDetId { get; set; }
        public string ProID { get; set; }
        public string ProSerial { get; set; }
        public DateTime? ProExpirey { get; set; }
        public int UnitID { get; set; }
        public decimal Quantity { get; set; }
        public decimal Price { get; set; }
        public DateTime DateFrom { get; set; }
        public DateTime DateTo { get; set; }
    }
}
