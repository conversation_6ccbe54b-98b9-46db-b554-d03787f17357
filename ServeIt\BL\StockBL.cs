﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ServeIt.BL
{
    class StockBL
    {

        // methods dealing with stock 

        #region Manage Stock

        // fucntion to retreive stocks data
        public DataTable GetAllStock()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetData("getallstock", null);
            }
        }


        //function to add new stock in company
        public string AddOrUpdateStock(BE.StockBE mystock)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[9];
                para[0] = new SqlParameter("@check", "a");
                para[1] = new SqlParameter("@stock_id", mystock.StockId);
                para[2] = new SqlParameter("@stock_name", mystock.Name);
                para[3] = new SqlParameter("@cost_center_id", mystock.CostCenterID);
                para[4] = new SqlParameter("@location", mystock.location);
                para[5] = new SqlParameter("@stock_emp", mystock.StockEmpName);
                para[6] = new SqlParameter("@active", mystock.Active);
                para[7] = new SqlParameter("@default_stock", mystock.Default);
                para[8] = new SqlParameter("@user_name", mystock.User_Name);
                return DAL.InsUpdDel("managestock", para);
            }
        }

        // function to delete exist stock
        public string DeleteStock(BE.StockBE mystock)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@stock_id", mystock.StockId);
                para[1] = new SqlParameter("@check", "d");
                return DAL.InsUpdDel("managestock", para);
            }
        }


        // function to search for exist stock
        public DataTable SearchStock(BE.StockBE mystock)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@stk_name", mystock.Name);
                return DAL.GetData("SearchStock", para);
            }
        }

        public DataTable GetStockDetailsByID(string StockID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@stock_id", StockID);
                return DAL.GetData("GetStockDetailsByID", para);
            }
        }

        public DataTable GetPreviousStockID(string CurrentID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@current_id", CurrentID);
                return DAL.GetData("GetPreviousStockID", para);
            }
        }

        public DataTable GetNextStockID(string CurrentID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@current_id", CurrentID);
                return DAL.GetData("GetNextStockID", para);
            }
        }


        // function to retreive max stock id ,to reuse it in set stock id  when adding new stock
        public string GetMaxStockId()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetValue("getmaxstockid");
            }
        }


        public DataTable GetDefaultStock()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetData("getdefaultstock", null);
            }
        }

        #endregion

        #region Stock Report

        public DataTable RPTItemsList(string ProID, string CatID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@pro_id", ProID);
                para[1] = new SqlParameter("@cat_id", CatID);
                return DAL.GetData("RPTItemList", para);
            }
        }

        public DataTable RPTPricesList(string ProID, string CatID, string PriceType)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[3];
                para[0] = new SqlParameter("@pro_id", ProID);
                para[1] = new SqlParameter("@cat_id", CatID);
                para[2] = new SqlParameter("@price_type", PriceType);
                return DAL.GetData("RPTPricesList", para);
            }
        }

        public DataTable RPTGetProMovement(BE.StockBE stckbe) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para= new SqlParameter[4];
                para[0] = new SqlParameter("@pro_id",stckbe.ProID);
                para[1] = new SqlParameter("@stock_id",stckbe.StockId);
                para[2] = new SqlParameter("@datefrom",stckbe.DateFrom);
                para[3] = new SqlParameter("@dateto",stckbe.DateTo);
                return DAL.GetData("RPTGetProMovement", para);
            }
        }

        public DataTable RPTGetProMovementProfit(BE.StockBE stckbe)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[4];
                para[0] = new SqlParameter("@pro_id", stckbe.ProID);
                para[1] = new SqlParameter("@stock_id", stckbe.StockId);
                para[2] = new SqlParameter("@datefrom", stckbe.DateFrom);
                para[3] = new SqlParameter("@dateto", stckbe.DateTo);
                return DAL.GetData("RPTGetProMovementProfitFIFO", para);
            }
        }

        public DataTable RPTGetProMovementSerial(BE.StockBE stckbe)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[4];
                para[0] = new SqlParameter("@pro_serial", stckbe.ProSerial);
                para[1] = new SqlParameter("@stock_id", stckbe.StockId);
                para[2] = new SqlParameter("@datefrom", stckbe.DateFrom);
                para[3] = new SqlParameter("@dateto", stckbe.DateTo);
                return DAL.GetData("RPTGetProMovementSerial", para);
            }
        }

        public DataTable RPTGetProMovementExpirey(BE.StockBE stckbe)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[5];
                para[0] = new SqlParameter("@pro_id", stckbe.ProID);
                para[1] = new SqlParameter("@pro_expirey", stckbe.ExpireyDate);
                para[2] = new SqlParameter("@stock_id", stckbe.StockId);
                para[3] = new SqlParameter("@datefrom", stckbe.DateFrom);
                para[4] = new SqlParameter("@dateto", stckbe.DateTo);
                return DAL.GetData("RPTGetProMovementExpirey", para);
            }
        }

        public DataTable RPTGetStockItemQty(BE.StockBE stckbe)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[8];
                para[0] = new SqlParameter("@pro_id", stckbe.ProID);
                para[1] = new SqlParameter("@pro_name", stckbe.ProName);
                para[2] = new SqlParameter("@stock_id", stckbe.StockId);
                para[3] = new SqlParameter("@cat_id", stckbe.Cat_Id);
                para[4] = new SqlParameter("@datefrom", stckbe.DateFrom);
                para[5] = new SqlParameter("@dateto", stckbe.DateTo);
                para[6] = new SqlParameter("@price_type", stckbe.PriceSystem);
                para[7] = new SqlParameter("@zero_accounts", stckbe.ZeroAccounts);
                return DAL.GetData("RPTGetStockItemQty", para);
            }

        }

        public DataTable RPTGetStockItemQtyProExpirey(BE.StockBE stckbe)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[9];
                para[0] = new SqlParameter("@pro_id", stckbe.ProID);
                para[1] = new SqlParameter("@pro_name", stckbe.ProName);
                para[2] = new SqlParameter("@stock_id", stckbe.StockId);
                para[3] = new SqlParameter("@cat_id", stckbe.Cat_Id);
                para[4] = new SqlParameter("@pro_expirey", stckbe.ExpireyDate);
                para[5] = new SqlParameter("@datefrom", stckbe.DateFrom);
                para[6] = new SqlParameter("@dateto", stckbe.DateTo);
                para[7] = new SqlParameter("@price_type", stckbe.PriceSystem);
                para[8] = new SqlParameter("@zero_accounts", stckbe.ZeroAccounts);
                return DAL.GetData("RPTGetStockItemQtyProExpirey", para);
            }
        }

        public DataTable GetStockItemQtySerial(BE.StockBE stckbe)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[9];
                para[0] = new SqlParameter("@pro_id", stckbe.ProID);
                para[1] = new SqlParameter("@pro_name", stckbe.ProName);
                para[2] = new SqlParameter("@pro_serial", stckbe.ProSerial);
                para[3] = new SqlParameter("@stock_id", stckbe.StockId);
                para[4] = new SqlParameter("@cat_id", stckbe.Cat_Id);
                para[5] = new SqlParameter("@datefrom", stckbe.DateFrom);
                para[6] = new SqlParameter("@dateto", stckbe.DateTo);
                para[7] = new SqlParameter("@price_type", stckbe.PriceSystem);
                para[8] = new SqlParameter("@zero_accounts", stckbe.ZeroAccounts);
                return DAL.GetData("RPTGetStockItemQtySerial", para);
            }
        }

        public DataTable GetProductBalanceForEachStock(string Pro_Name) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@pro_name", Pro_Name);
                return DAL.GetData("RPTGetProductQuantityForEachStock", para);
            }
        }

        public DataTable GetQuickItemBalance(BE.StockBE stckbe) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[5];
                para[0] = new SqlParameter("@pro_name", stckbe.ProName);
                para[1] = new SqlParameter("@stock_id", stckbe.StockId);
                para[2] = new SqlParameter("@Cat_id", stckbe.Cat_Id);
                para[3] = new SqlParameter("@pro_id", stckbe.ProID);
                para[4] = new SqlParameter("@unit_kind", stckbe.Unit_Kind);
                return DAL.GetData("GetQUickItemBalance", para);
            }
        }

        public DataTable RPTGetProdUnderRequestLimit(BE.StockBE stckbe)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@cat_id", stckbe.Cat_Id);
                para[1] = new SqlParameter("@pro_id", stckbe.ProID);
                return DAL.GetData("RPTGetProdUnderRequestLimit", para);
            }
        }

        public DataTable RPTGetProductsExpired(BE.StockBE stckbe)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[4];

                para[0] = new SqlParameter("@stock_id", stckbe.StockId);
                para[1] = new SqlParameter("@cat_id", stckbe.Cat_Id);
                para[2] = new SqlParameter("@pro_id", stckbe.ProID);
                para[3] = new SqlParameter("@expirey_limit", stckbe.ExpireyDaysLimit);

                return DAL.GetData("RPTGetProductsExpired", para);
            }
        }

        public DataTable GetProDetailedBalanceByProIDAndStockID(string ProID, string StockID) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@stock_id", StockID);
                para[1] = new SqlParameter("@pro_id", ProID);
                return DAL.GetData("GetProDetailedBalanceByProIDAndStockID", para);
            }
        }

        public string GetProductSmallBalanceByProIDAndStockID(string ProID, string StockID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@stock_id", StockID);
                para[1] = new SqlParameter("@pro_id", ProID);
                return DAL.GetValue("GetProductSmallBalanceByProIDAndStockID", para);
            }
        }

        public DataTable RPTSalesOfSalesRepresentativeItemsQuantity(string SalesRepresetativeID, string StockID, DateTime? DateFrom, DateTime? DateTo)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@stock_id", StockID);
                para[1] = new SqlParameter("@sale_rep_id", SalesRepresetativeID);
                para[0] = new SqlParameter("@datefrom", DateFrom);
                para[1] = new SqlParameter("@dateto", DateTo);
                return DAL.GetData("RPTSalesOfSalesRepresentative", para);
            }
        }

        #endregion

    }
}
