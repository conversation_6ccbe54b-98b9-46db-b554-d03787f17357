insert into expenses ( expense_name,parent_id)
select distinct sub_expense_name,0 from ServeItDBNew.dbo.expenses
go 
select * from ServeItDBNew.dbo.expenses
select * from treasury_permission
delete  from treasury_permission where per_kind_id = 6
declare @MaxTreasPerID int
set @MaxTreasPerID = (select MAX(per_id) from treasury_permission)
declare @Expense_name nvarchar(200)
declare @Expense_per_id int
declare @Expense_id int
declare @expense_date datetime
declare @treasury_id int
declare @value decimal(18,2)
declare @notes nvarchar(200)
declare expense_cursor Cursor for select sub_expense_id from ServeItDBNew.dbo.expenses
open expense_cursor
fetch next from expense_cursor into @Expense_per_id
while @@FETCH_STATUS = 0
begin
	set @MaxTreasPerID = @MaxTreasPerID +1
	set @Expense_name = (select sub_expense_name from ServeItDBNew.dbo.expenses where sub_expense_id = @Expense_per_id)
	set @Expense_id = (select expense_id from expenses where expense_name = @Expense_name)
	set @expense_date = (select [date] from ServeItDBNew.dbo.expenses where  sub_expense_id = @Expense_per_id)
	set @treasury_id = (select treas_id from ServeItDBNew.dbo.expenses where  sub_expense_id = @Expense_per_id)
	set @value = (select value from ServeItDBNew.dbo.expenses where  sub_expense_id = @Expense_per_id)
	set @notes = (select notes from ServeItDBNew.dbo.expenses where  sub_expense_id = @Expense_per_id)
	insert into treasury_permission(per_id, per_kind_id,per_date,expense_id,treasury_id,value,notes,[user_name])
	values (@MaxTreasPerID,6,@expense_date,@Expense_id,@treasury_id,@value,@notes,'admin')
	fetch next from expense_cursor into @Expense_per_id
end
close expense_cursor
deallocate expense_cursor