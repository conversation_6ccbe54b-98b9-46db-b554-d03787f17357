﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;

namespace ServeIt.BL
{
    class AssetsBL
    {
        public string AssetID { get; set; }
        public string AssetName { get; set; }
        public string AssetCategoryID { get; set; }
        public string AssetValue { get; set; }
        public string AssetAge { get; set; }
        public string AssetScrapValue { get; set; }
        public string Notes { get; set; }
        public string UserName { get; set; }


        public string AddOrUpdateAsset(AssetsBL aset_bl)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[9];
                para[0] = new SqlParameter("@check", "a");
                para[1] = new SqlParameter("@asset_id", aset_bl.AssetID);
                para[2] = new SqlParameter("@asset_name", aset_bl.AssetName);
                para[3] = new SqlParameter("@asset_cat_id", aset_bl.AssetCategoryID);
                para[4] = new SqlParameter("@asset_value", aset_bl.AssetValue);
                para[5] = new SqlParameter("@asset_age", aset_bl.AssetAge);
                para[6] = new SqlParameter("@asset_scrap_value", aset_bl.AssetScrapValue);
                para[7] = new SqlParameter("@notes", aset_bl.Notes);
                para[8] = new SqlParameter("@user_name", aset_bl.UserName);
                return DAL.InsUpdDel("ManageAssets", para);
            }
        }

        public string DeleteAssets(string AssetID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "d");
                para[1] = new SqlParameter("@asset_id", AssetID);
                return DAL.InsUpdDel("ManageAssets", para);
            }
        }

        public string GetMaxAssetCategoryID()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@check", "m");
                return DAL.GetValue("ManageAssets", para);
            }
        }

        public DataTable GetAssetCategoryByID(string AssetCategoryID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "g");
                para[1] = new SqlParameter("@asset_id", AssetCategoryID);

                return DAL.GetData("ManageAssets", para);
            }
        }

        public DataTable SearchAssets(string AssetName)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "s");
                para[1] = new SqlParameter("@asset_name", AssetName);

                return DAL.GetData("ManageAssets", para);
            }
        }

        public DataTable GetAssetsNodes(string AssetCategoryID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "p");
                para[1] = new SqlParameter("@asset_cat_id", AssetCategoryID);
                return DAL.GetData("ManageAssets", para);
            }
        }

    }
}
