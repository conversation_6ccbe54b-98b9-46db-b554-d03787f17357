//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace ServeIt.DA
{
    using System;
    using System.Collections.Generic;
    
    public partial class treasury
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public treasury()
        {
            this.Installment_paids = new HashSet<Installment_paids>();
            this.invoice_header = new HashSet<invoice_header>();
            this.maintenances = new HashSet<maintenance>();
            this.treasury_initial_balance = new HashSet<treasury_initial_balance>();
            this.treasury_permission = new HashSet<treasury_permission>();
        }
    
        public int treasury_id { get; set; }
        public string treasury_name { get; set; }
        public Nullable<int> cost_center_id { get; set; }
        public Nullable<decimal> initial_balance { get; set; }
        public string location { get; set; }
        public string user_name { get; set; }
    
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Installment_paids> Installment_paids { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<invoice_header> invoice_header { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<maintenance> maintenances { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<treasury_initial_balance> treasury_initial_balance { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<treasury_permission> treasury_permission { get; set; }
    }
}
