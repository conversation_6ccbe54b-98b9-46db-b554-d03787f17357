﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;

namespace ServeIt.BL
{
    class KitchenProductBL
    {
        public string KitchenProID { get; set; }
        public string KitchenID{ get; set; }
        public string ProID { get; set; }

        public string AddOrUpdateProKitchen(KitchenProductBL kchn_pro_bl)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[4];
                para[0] = new SqlParameter("@check", "a");
                para[1] = new SqlParameter("@kitchen_pro_id", kchn_pro_bl.KitchenProID);
                para[2] = new SqlParameter("@kitchen_id", kchn_pro_bl.KitchenID);
                para[3] = new SqlParameter("@pro_id", kchn_pro_bl.ProID);

                return DAL.InsUpdDel("ManageKitchenProducts", para);
            }
        }

        public string DeleteProKitchen(string KitchenID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "d");
                para[1] = new SqlParameter("@kitchen_pro_id", KitchenID);

                return DAL.InsUpdDel("ManageKitchenProducts", para);
            }
        }

        public string GetMaxProKitchenID()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@check", "m");

                return DAL.GetValue("ManageKitchenProducts", para);
            }
        }

        public DataTable GetProKitchenDetailsByID(string KitchenID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "g");
                para[1] = new SqlParameter("@kitchen_pro_id", KitchenID);


                return DAL.GetData("ManageKitchenProducts", para);
            }
        }

        public DataTable SearchProductsKitchens(string KitchenID, string ProName)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[3];
                para[0] = new SqlParameter("@check", "s");
                para[1] = new SqlParameter("@kitchen_id", KitchenID);
                para[2] = new SqlParameter("@pro_name", ProName);

                return DAL.GetData("ManageKitchenProducts", para);
            }
        }

        public DataTable GetProPrinterAndKitchenByProID(string ProID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@pro_id", ProID);
                return DAL.GetData("GetProPrinterAndKitchenByProID", para);
            }
        }
    }
}
