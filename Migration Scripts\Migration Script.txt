--ALTER TABLE ServeItDB51Delta.dbo.Installment_paids
--ADD cust_id int
--go
--ALTER TABLE ServeItDB51Delta.dbo.product
--DROP COLUMN pic
--go
--ALTER TABLE ServeItDB51Delta.dbo.[customer&vendor]
--DROP COLUMN customer_pic
--go

delete from invoice_detail
delete from invoice_header
delete from installment_paids
delete from stock_permission_detail
delete from stock_permission_header
delete from product_initial_balance_detail
delete from product_images
delete from product_unit
delete from product
delete from unit
delete from category
delete from stock
delete from company
delete from treasury_permission
delete from expenses
--delete from revenues
delete from bank_action
delete from paper
delete from customer_vendor_picture
delete from customer_initial_balance
delete from treasury_initial_balance
delete from [customer&vendor]
delete from treasury
delete from bank_account
delete from bank_branch
delete from bank
delete from sales_representative
delete from bank_action
delete from paper
delete from Active_permissions
delete from user_log
delete from [user]
delete from Installment_paids
delete from cost_center
go
insert into cost_center
select * from ServeItDB51Delta.dbo.cost_center
go
insert into [user]
select * from ServeItDB51Delta.dbo.[user]
go
SET IDENTITY_INSERT ServeItDB51DeltaNew.dbo.Active_permissions on

go

insert into Active_permissions(id,[user_name],permission_name)
select id,[user_name],permission_name from ServeItDB51Delta.dbo.Active_permissions
go
SET IDENTITY_INSERT ServeItDB51DeltaNew.dbo.Active_permissions off
go
insert into company
select * from ServeItDB51Delta.dbo.company
go

insert into stock
select * from ServeItDB51Delta.dbo.stock
go
insert into category
select * from ServeItDB51Delta.dbo.category
go

insert into unit
select * from ServeItDB51Delta.dbo.unit
go
insert into product
select * from ServeItDB51Delta.dbo.product
go
insert into product_unit
select * from ServeItDB51Delta.dbo.product_unit
go
insert into sales_representative
select * from ServeItDB51Delta.dbo.sales_representative
go
insert into treasury
select * from ServeItDB51Delta.dbo.treasury
go

SET IDENTITY_INSERT ServeItDB51DeltaNew.dbo.treasury_initial_balance on
go

insert into treasury_initial_balance(treausry_init_bal_id,treasury_id,quantity,[date])
select treausry_init_bal_id,treasury_id,quantity,[date] from ServeItDB51Delta.dbo.treasury_initial_balance

go
SET IDENTITY_INSERT ServeItDB51DeltaNew.dbo.treasury_initial_balance off
go
insert into [customer&vendor]
select * from ServeItDB51Delta.dbo.[customer&vendor]
go

SET IDENTITY_INSERT ServeItDB51DeltaNew.dbo.customer_initial_balance on
go


insert into customer_initial_balance (cust_init_bal_id,cust_id,quantity,[date])
select cust_init_bal_id,cust_id,quantity,[date] from ServeItDB51Delta.dbo.customer_initial_balance
go

SET IDENTITY_INSERT ServeItDB51DeltaNew.dbo.customer_initial_balance off
go

insert into product_initial_balance_detail
select * from ServeItDB51Delta.dbo.product_initial_balance_detail
go
insert into stock_permission_header
select * from ServeItDB51Delta.dbo.stock_permission_header
go
insert into stock_permission_detail
select * from ServeItDB51Delta.dbo.stock_permission_detail
go
insert into invoice_header
select * from ServeItDB51Delta.dbo.invoice_header
go
insert into invoice_detail
select * from ServeItDB51Delta.dbo.invoice_detail
go
insert into treasury_permission
select * from ServeItDB51Delta.dbo.treasury_permission
go
insert into expenses
select * from ServeItDB51Delta.dbo.expenses
go
insert into revenues
select * from ServeItDB51Delta.dbo.revenues
go
insert into bank
select * from ServeItDB51Delta.dbo.bank
go
insert into bank_branch
select * from ServeItDB51Delta.dbo.bank_branch
go
insert into bank_account
select * from ServeItDB51Delta.dbo.bank_account
go
insert into bank_action
select * from ServeItDB51Delta.dbo.bank_action
go
insert into paper
select * from ServeItDB51Delta.dbo.paper
go
insert into Installment_paids
select * from ServeItDB51Delta.dbo.Installment_paids

update product_unit set half_wholesale_price = 0, lowest_price = 0, highest_price = 0
delete from product_images
insert into product_images (pro_id)
select pro_id from product
go
delete from customer_vendor_picture
insert into customer_vendor_picture(cust_id)
select cust_id from [customer&vendor]

SET IDENTITY_INSERT ServeItDB51DeltaNew.dbo.Active_permissions off
go
SET IDENTITY_INSERT ServeItDB51DeltaNew.dbo.customer_initial_balance off
go
SET IDENTITY_INSERT ServeItDB51DeltaNew.dbo.treasury_initial_balance off

update product_unit set half_wholesale_price =0, lowest_price = 0, highest_price = 0
delete from product_images
insert into product_images(pro_id)
select pro_id from product
delete from customer_vendor_picture
insert into customer_vendor_picture(cust_id)
select cust_id from [customer&vendor]
