//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace ServeIt.DA
{
    using System;
    using System.Collections.Generic;
    
    public partial class work_periods
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public work_periods()
        {
            this.employees = new HashSet<employee>();
        }
    
        public int work_period_id { get; set; }
        public string work_period_name { get; set; }
        public Nullable<System.TimeSpan> work_start_time { get; set; }
        public Nullable<System.TimeSpan> work_end_time { get; set; }
        public Nullable<decimal> late_ratio_allowance { get; set; }
        public Nullable<decimal> late_ratio { get; set; }
        public Nullable<decimal> addition_ratio_allowance { get; set; }
        public Nullable<decimal> addition_ratio { get; set; }
        public Nullable<bool> saturday { get; set; }
        public Nullable<bool> sunday { get; set; }
        public Nullable<bool> monday { get; set; }
        public Nullable<bool> tuesday { get; set; }
        public Nullable<bool> wednesday { get; set; }
        public Nullable<bool> thursday { get; set; }
        public Nullable<bool> friday { get; set; }
        public string notes { get; set; }
        public string user_name { get; set; }
    
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<employee> employees { get; set; }
    }
}
