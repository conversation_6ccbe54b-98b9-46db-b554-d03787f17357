﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="BtnNext.ImageScaling" type="System.Windows.Forms.ToolStripItemImageScaling, System.Windows.Forms">
    <value>None</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="BtnEdit.Font" type="System.Drawing.Font, System.Drawing">
    <value>Arial, 10pt, style=Bold</value>
  </data>
  <data name="toolStripSeparator3.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 60</value>
  </data>
  <data name="&gt;&gt;label6.Name" xml:space="preserve">
    <value>label6</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="statusStrip1.AutoSize" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="statusStrip1.Size" type="System.Drawing.Size, System.Drawing">
    <value>728, 30</value>
  </data>
  <data name="&gt;&gt;label8.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;TxtId.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label8.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;TxtLocation.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="toolStripSeparator6.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 60</value>
  </data>
  <data name="label4.TabIndex" type="System.Int32, mscorlib">
    <value>33</value>
  </data>
  <data name="&gt;&gt;TxtId.Name" xml:space="preserve">
    <value>TxtId</value>
  </data>
  <data name="label2.Font" type="System.Drawing.Font, System.Drawing">
    <value>Ara Hamah Kilania, 16pt, style=Bold</value>
  </data>
  <data name="BtnOrdersArchive.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>BottomCenter</value>
  </data>
  <data name="label3.Size" type="System.Drawing.Size, System.Drawing">
    <value>42, 19</value>
  </data>
  <data name="BtnDelete.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>BottomCenter</value>
  </data>
  <data name="&gt;&gt;BtnNext.Name" xml:space="preserve">
    <value>BtnNext</value>
  </data>
  <data name="&gt;&gt;BtnSave.Name" xml:space="preserve">
    <value>BtnSave</value>
  </data>
  <data name="&gt;&gt;TxtName.Name" xml:space="preserve">
    <value>TxtName</value>
  </data>
  <data name="&gt;&gt;label4.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;timer1.Name" xml:space="preserve">
    <value>timer1</value>
  </data>
  <data name="CBCostCenter.Size" type="System.Drawing.Size, System.Drawing">
    <value>371, 26</value>
  </data>
  <data name="label6.Text" xml:space="preserve">
    <value>تاريخ الاضافة</value>
  </data>
  <data name="toolStripStatusLabel1.Font" type="System.Drawing.Font, System.Drawing">
    <value>Arial, 12pt, style=Bold</value>
  </data>
  <data name="&gt;&gt;label2.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="label3.Text" xml:space="preserve">
    <value>المكان</value>
  </data>
  <data name="&gt;&gt;label1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="BtnClose.Size" type="System.Drawing.Size, System.Drawing">
    <value>92, 58</value>
  </data>
  <data name="&gt;&gt;label6.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label2.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator9.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label3.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="label2.Size" type="System.Drawing.Size, System.Drawing">
    <value>93, 28</value>
  </data>
  <data name="BtnEdit.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAO
        wwAADsMBx2+oZAAAAbxJREFUWEe1l9uRgjAYhSnBEvYN9wLYgco2YAm+7cyGGUsQ2QIsgRIsgWHhnRIs
        gRLcnEzAOAZy3TNzHhTyfyd/mBACldbHdpUWbU99M3S/PTU5L2MnB/jo7am98HJmeobX3edPs9FxWvzu
        cP99bLvnZfUkmzmdScUva2l9rBZp0VzZ2Lwp+d9qTbXdNACEMUZj59bcPkDdoRv4HSaHlzAhG5jdIEr1
        wNkE2BTteYTH2XkZZ7fR7+T6lnyv2I0qOGwTYNAyJuUDfHCUVVpw2DbAJJw7oMWVcNhmU1HBYQTgkLrD
        LGXGWvKa2tKBsyUYAmAD4WOdpQMPo6xLksPCewAjOOQzgDEc8hXACg75CGANh1wDOMEhlwBhRHIZUPQs
        HHIKEH71MuhgJRxyCSCDDhbhOKCwATLZBBhfqxMdEOE4jGA3ZQNlsgqAVyvdRiloPwfHFo7aXgOg+H3m
        pHz9yHYIwwLRYOKaA+w9wNOTTw8WWBJ++UH/EgBA1mrWBVJOwSHvAbDmAKMLYqun5D3AeIbTlPcApjIK
        YHPqmZP2hwlNdxFDoBOuNvo00z0V2xoT5KhpIYTYCU/W+DwPgj9YGMXllie2jQAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator5.Name" xml:space="preserve">
    <value>toolStripSeparator5</value>
  </data>
  <data name="&gt;&gt;TxtInitialBalance.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;toolStrip1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStrip, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>728, 414</value>
  </data>
  <data name="BtnDelete.Text" xml:space="preserve">
    <value>حذف  F3</value>
  </data>
  <data name="&gt;&gt;label4.Name" xml:space="preserve">
    <value>label4</value>
  </data>
  <data name="BtnNext.Size" type="System.Drawing.Size, System.Drawing">
    <value>54, 58</value>
  </data>
  <data name="BtnPrevious.ImageScaling" type="System.Windows.Forms.ToolStripItemImageScaling, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="&gt;&gt;BtnClose.Name" xml:space="preserve">
    <value>BtnClose</value>
  </data>
  <data name="DTPTreasInitBal.Size" type="System.Drawing.Size, System.Drawing">
    <value>371, 25</value>
  </data>
  <data name="$this.Font" type="System.Drawing.Font, System.Drawing">
    <value>Droid Arabic Kufi, 7pt, style=Bold</value>
  </data>
  <data name="&gt;&gt;statusStrip1.Name" xml:space="preserve">
    <value>statusStrip1</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>اضافة خزنة</value>
  </data>
  <data name="TxtId.Size" type="System.Drawing.Size, System.Drawing">
    <value>371, 25</value>
  </data>
  <data name="statusStrip1.Font" type="System.Drawing.Font, System.Drawing">
    <value>Arial, 12pt, style=Bold</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator7.Name" xml:space="preserve">
    <value>toolStripSeparator7</value>
  </data>
  <data name="BtnNew.Font" type="System.Drawing.Font, System.Drawing">
    <value>Arial, 10pt, style=Bold</value>
  </data>
  <data name="&gt;&gt;label5.Name" xml:space="preserve">
    <value>label5</value>
  </data>
  <data name="&gt;&gt;TxtId.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="&gt;&gt;label1.Name" xml:space="preserve">
    <value>label1</value>
  </data>
  <data name="BtnSave.Font" type="System.Drawing.Font, System.Drawing">
    <value>Arial, 10pt, style=Bold</value>
  </data>
  <data name="label3.TabIndex" type="System.Int32, mscorlib">
    <value>32</value>
  </data>
  <data name="BtnEdit.ImageTransparentColor" type="System.Drawing.Color, System.Drawing">
    <value>Magenta</value>
  </data>
  <data name="BtnNext.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>BottomCenter</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator5.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="BtnOrdersArchive.ImageScaling" type="System.Windows.Forms.ToolStripItemImageScaling, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="&gt;&gt;TxtId.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="label5.TabIndex" type="System.Int32, mscorlib">
    <value>97</value>
  </data>
  <data name="BtnSave.ImageScaling" type="System.Windows.Forms.ToolStripItemImageScaling, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="TxtInitialBalance.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="DTPTreasInitBal.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="toolStripStatusLabel1.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 25</value>
  </data>
  <data name="toolStripSeparator5.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 60</value>
  </data>
  <data name="BtnNext.Font" type="System.Drawing.Font, System.Drawing">
    <value>Arial, 10pt, style=Bold</value>
  </data>
  <data name="panel1.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="label5.Text" xml:space="preserve">
    <value>كود الخزنة</value>
  </data>
  <data name="label2.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator3.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;BtnPrevious.Name" xml:space="preserve">
    <value>BtnPrevious</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator6.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;TxtInitialBalance.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="toolStrip1.TabIndex" type="System.Int32, mscorlib">
    <value>570</value>
  </data>
  <data name="&gt;&gt;panel1.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="&gt;&gt;toolStrip1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label5.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;TxtLocation.Name" xml:space="preserve">
    <value>TxtLocation</value>
  </data>
  <data name="label8.Size" type="System.Drawing.Size, System.Drawing">
    <value>34, 19</value>
  </data>
  <data name="statusStrip1.TabIndex" type="System.Int32, mscorlib">
    <value>571</value>
  </data>
  <data name="BtnSave.Size" type="System.Drawing.Size, System.Drawing">
    <value>84, 58</value>
  </data>
  <data name="BtnClose.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>BottomCenter</value>
  </data>
  <data name="&gt;&gt;BtnNext.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="BtnEdit.Text" xml:space="preserve">
    <value>تعديل  F2</value>
  </data>
  <data name="&gt;&gt;DTPTreasInitBal.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="label8.Text" xml:space="preserve">
    <value>الفرع</value>
  </data>
  <data name="label8.TabIndex" type="System.Int32, mscorlib">
    <value>573</value>
  </data>
  <data name="&gt;&gt;statusStrip1.Type" xml:space="preserve">
    <value>System.Windows.Forms.StatusStrip, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="label3.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;toolStripStatusLabel1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripStatusLabel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator7.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="statusStrip1.Text" xml:space="preserve">
    <value>statusStrip1</value>
  </data>
  <data name="&gt;&gt;BtnNew.Name" xml:space="preserve">
    <value>BtnNew</value>
  </data>
  <data name="&gt;&gt;TxtLocation.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;toolStripStatusLabel1.Name" xml:space="preserve">
    <value>toolStripStatusLabel1</value>
  </data>
  <data name="label4.Location" type="System.Drawing.Point, System.Drawing">
    <value>82, 254</value>
  </data>
  <data name="&gt;&gt;BtnPrevious.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="label1.Location" type="System.Drawing.Point, System.Drawing">
    <value>114, 164</value>
  </data>
  <data name="BtnNext.ImageTransparentColor" type="System.Drawing.Color, System.Drawing">
    <value>Magenta</value>
  </data>
  <data name="toolStripSeparator6.AccessibleName" xml:space="preserve">
    <value>تعديل فاتورة بيع</value>
  </data>
  <data name="BtnDelete.Font" type="System.Drawing.Font, System.Drawing">
    <value>Arial, 10pt, style=Bold</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>treasury_new</value>
  </data>
  <data name="&gt;&gt;label4.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="BtnNew.Text" xml:space="preserve">
    <value>جديد   F1</value>
  </data>
  <data name="&gt;&gt;BtnOrdersArchive.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;BtnEdit.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="TxtInitialBalance.Location" type="System.Drawing.Point, System.Drawing">
    <value>184, 251</value>
  </data>
  <data name="BtnEdit.TextImageRelation" type="System.Windows.Forms.TextImageRelation, System.Windows.Forms">
    <value>ImageAboveText</value>
  </data>
  <data name="TxtLocation.Multiline" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="TxtInitialBalance.Size" type="System.Drawing.Size, System.Drawing">
    <value>371, 25</value>
  </data>
  <data name="&gt;&gt;label4.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="&gt;&gt;statusStrip1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;CBCostCenter.Name" xml:space="preserve">
    <value>CBCostCenter</value>
  </data>
  <data name="BtnNew.ImageScaling" type="System.Windows.Forms.ToolStripItemImageScaling, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="&gt;&gt;BtnSave.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="statusStrip1.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 384</value>
  </data>
  <data name="&gt;&gt;panel1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterScreen</value>
  </data>
  <data name="BtnDelete.ImageScaling" type="System.Windows.Forms.ToolStripItemImageScaling, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator3.Name" xml:space="preserve">
    <value>toolStripSeparator3</value>
  </data>
  <data name="label2.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>TopCenter</value>
  </data>
  <data name="toolStripSeparator9.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 60</value>
  </data>
  <data name="&gt;&gt;panel1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="BtnOrdersArchive.Text" xml:space="preserve">
    <value>ارشيف البنود</value>
  </data>
  <data name="&gt;&gt;BtnDelete.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="label5.Location" type="System.Drawing.Point, System.Drawing">
    <value>116, 134</value>
  </data>
  <data name="TxtLocation.Location" type="System.Drawing.Point, System.Drawing">
    <value>184, 281</value>
  </data>
  <data name="BtnEdit.ImageScaling" type="System.Windows.Forms.ToolStripItemImageScaling, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="BtnSave.ImageTransparentColor" type="System.Drawing.Color, System.Drawing">
    <value>Magenta</value>
  </data>
  <data name="&gt;&gt;label2.Parent" xml:space="preserve">
    <value>panel1</value>
  </data>
  <data name="BtnSave.TextImageRelation" type="System.Windows.Forms.TextImageRelation, System.Windows.Forms">
    <value>ImageAboveText</value>
  </data>
  <data name="BtnNew.ImageTransparentColor" type="System.Drawing.Color, System.Drawing">
    <value>Magenta</value>
  </data>
  <data name="toolStrip1.AutoSize" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="TxtName.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;DTPTreasInitBal.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="&gt;&gt;toolStrip1.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="BtnClose.TextImageRelation" type="System.Windows.Forms.TextImageRelation, System.Windows.Forms">
    <value>ImageAboveText</value>
  </data>
  <data name="BtnClose.ImageTransparentColor" type="System.Drawing.Color, System.Drawing">
    <value>Magenta</value>
  </data>
  <data name="BtnNew.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>BottomCenter</value>
  </data>
  <data name="BtnDelete.TextImageRelation" type="System.Windows.Forms.TextImageRelation, System.Windows.Forms">
    <value>ImageAboveText</value>
  </data>
  <data name="&gt;&gt;label8.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="BtnOrdersArchive.ImageTransparentColor" type="System.Drawing.Color, System.Drawing">
    <value>Magenta</value>
  </data>
  <data name="BtnEdit.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 58</value>
  </data>
  <data name="BtnClose.ImageScaling" type="System.Windows.Forms.ToolStripItemImageScaling, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="$this.RightToLeftLayout" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="BtnEdit.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>BottomCenter</value>
  </data>
  <data name="BtnPrevious.Size" type="System.Drawing.Size, System.Drawing">
    <value>54, 58</value>
  </data>
  <data name="&gt;&gt;CBCostCenter.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="BtnSave.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>BottomCenter</value>
  </data>
  <data name="&gt;&gt;TxtLocation.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;TxtInitialBalance.Name" xml:space="preserve">
    <value>TxtInitialBalance</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator6.Name" xml:space="preserve">
    <value>toolStripSeparator6</value>
  </data>
  <data name="label1.TabIndex" type="System.Int32, mscorlib">
    <value>35</value>
  </data>
  <data name="BtnOrdersArchive.Font" type="System.Drawing.Font, System.Drawing">
    <value>Arial, 10pt, style=Bold</value>
  </data>
  <data name="label2.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top</value>
  </data>
  <data name="&gt;&gt;statusStrip1.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="label2.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;toolStrip1.Name" xml:space="preserve">
    <value>toolStrip1</value>
  </data>
  <data name="label6.TabIndex" type="System.Int32, mscorlib">
    <value>99</value>
  </data>
  <data name="BtnNew.Size" type="System.Drawing.Size, System.Drawing">
    <value>82, 58</value>
  </data>
  <data name="DTPTreasInitBal.Location" type="System.Drawing.Point, System.Drawing">
    <value>184, 221</value>
  </data>
  <data name="BtnDelete.ImageTransparentColor" type="System.Drawing.Color, System.Drawing">
    <value>Magenta</value>
  </data>
  <data name="label6.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="BtnClose.Text" xml:space="preserve">
    <value>خروج  Esc</value>
  </data>
  <data name="BtnPrevious.Font" type="System.Drawing.Font, System.Drawing">
    <value>Arial, 10pt, style=Bold</value>
  </data>
  <data name="BtnEdit.AccessibleName" xml:space="preserve">
    <value>تعديل فاتورة بيع</value>
  </data>
  <data name="&gt;&gt;CBCostCenter.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="toolStrip1.Text" xml:space="preserve">
    <value>toolStrip1</value>
  </data>
  <data name="&gt;&gt;CBCostCenter.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="BtnPrevious.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>BottomCenter</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator9.Name" xml:space="preserve">
    <value>toolStripSeparator9</value>
  </data>
  <data name="toolStripSeparator1.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 60</value>
  </data>
  <data name="&gt;&gt;label6.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label3.Name" xml:space="preserve">
    <value>label3</value>
  </data>
  <data name="label2.Text" xml:space="preserve">
    <value>اضافة خزنة</value>
  </data>
  <data name="&gt;&gt;BtnClose.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="label8.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="TxtName.Size" type="System.Drawing.Size, System.Drawing">
    <value>371, 25</value>
  </data>
  <data name="&gt;&gt;label5.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="BtnSave.ImageAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>BottomCenter</value>
  </data>
  <data name="&gt;&gt;label8.Name" xml:space="preserve">
    <value>label8</value>
  </data>
  <data name="BtnPrevious.ImageTransparentColor" type="System.Drawing.Color, System.Drawing">
    <value>Magenta</value>
  </data>
  <data name="label3.Location" type="System.Drawing.Point, System.Drawing">
    <value>137, 284</value>
  </data>
  <data name="&gt;&gt;label5.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="toolStrip1.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 35</value>
  </data>
  <data name="&gt;&gt;BtnOrdersArchive.Name" xml:space="preserve">
    <value>BtnOrdersArchive</value>
  </data>
  <data name="label1.Size" type="System.Drawing.Size, System.Drawing">
    <value>61, 19</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAICAAAAEAIACoEAAAFgAAACgAAAAgAAAAQAAAAAEAIAAAAAAAABAAACMuAAAjLgAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHb4tQBz+bIAefi5AHP6
        tAmJ+sZklfrRzpn71/WW+tblivrMimv4sxVy+L8AevnKALT//wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHv8ugBk9qoAcPiyAGru
        qwB4+bkygfjApor4yPWO+M7/jfjS/4v51v+J+dj+gPnOqWD2sRhj9rkAZPbAADTykQAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABr964Ab/ixAGb1
        qABx+bQWcfeye3b2teR59bf/ePS5/3b0u/909b//cvXD/3D1yP9x983/a/fFrU/1qRhR9LAAUfS1ADL/
        qwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABx+LMAfPq8AGX2
        qQB5/L0FavesUGn1qcZp86f/aPKm/2bxpv9j8af/YfGq/1/xrf9d8bD/WvGz/1fytv9Z9L3/VvS4rUDz
        oRhA8qYAP/KqABnMcQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACA/8UAW/WhAGf3
        qwBE7YcAZ/erK2P1pZ5h8p/0XvCb/1vvmP9Y7pf/Ve2Y/1LtmP9Q7Zn/Teya/0vsm/9I7Z3/Re2e/0Lu
        of9D8Kn/Q/KrrTHxmhgx8JwAMO+eAA6qWgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAZvarAGv3
        rwBb9KAAaviwEmH1pHJc8p3fWe+W/1Xukf9S7Y//TuyN/0vrjP9I6ov/RemK/0Hpiv8+6Yr/O+mL/zjp
        jP816Yz/MumM/y/qjf8x7Zf/M++drSXvkRgk7pEAJO2SAAaIRgAAAAAAAAAAAAAAAAAAAAAAbfewAIr9
        ygBf9aUAgP/JA2D1pUdb852+V/CV/VLtjv9P64r/S+qI/0fqhv9E6YT/QOiC/zzngP8554D/N+mF+zXs
        jdEx7pGjK+6RlSftj7El6ojnIuaA/x/mff8h6Yj/Je2RrRruiRga7IgAGuuJAAX/ugAAAAAAAAAAAFz1
        owBi9qkASe+OAGL2qCVb85+VVvCW8FHtjv9N64n/SeqG/0Xpg/9C6IH/Pud+/zrme/825Xr/NOd8/zTr
        h9RK64lwusM9VeOqFW/npw9727AZZWrYXlMY6oOvFuR2/hLicP8V5Xr/GuqGrRHsghgS6oEAEumBAAD/
        swAAAAAAVfSgAGb4rg1b9KBoVvGY2VHuj/9N64n/SeqF/0Xpg/9B6ID/Ped9/znlev815Hf/MeV3/zDo
        fu427YqRoclHU+WcBKHfkgPw24wC/9qLAv/dkQT+5p8Izbe+KVMM53mrC+Bp/wjeZP8L4nD/Eeh9rQrr
        fBgO6XwAEul+AADrcwBW9qMlVfKbrVHvkfxM7In/SOqF/0Tpgv9A6H//POZ8/zjlef805Hb/MOR1/y7m
        efwv64S4YuFxWuGjDHrfkgHg2IUA/9Z/AP/WfwD/1n8A/9aAAP/aiwL/6aQLvTveX0ME4WnqA9tc/wHb
        XP8F4Gj/Ded4rQrrexgN6XkAEeh6AE7ymbBM7Y3/SOqF/0Tpgv9A53//POZ8/zjlef805Hb/MON0/y3k
        dv8t6X/aPeuEdMizJF3jlgK/2okA/tZ/AP/VfQD/1X0A/9V9AP/VfgD/1X4A/9eDAP/loAzWedFALgDg
        ZdcA2Vf/ANhW/wDZWP8F32X/DeZ1rAnreRgN6XgARvCR4EPqg/8/533/O+Z7/zfleP8z5HX/L+Ny/yzj
        cv8r5njyLuyFmZDOT1LlmwSY3Y0B8deBAP/VfAD/1XwA/9V8AP/VfAD/1XwA/9V8AP/VfgD/3I4E/++l
        DX4G5WdNAN1d8wDWU/8A11P/ANdU/wDYVv8F3WL/DeVyqwnqeBo+8I+DOuqC+jbld/8y43P/LuJw/yri
        b/8o5HP+Kel+wFPjdF3dpA9y4JAB2diEAP/VfAD/1HsA/9R7AP/UewD/1HsA/9R7AP/UewD/1oAA/96P
        BOznow+DMd1bTADeXtEA11P/ANVQ/wDVUP8A1lH/ANZS/wDXVP8G3WH/DuZ0ojfzkgsy7YV1Luh73Crl
        dPYo5XPzJ+d40jLqgHm8tipY45QCttqGAfzVfAD/1HkA/9R5AP/UeQD/1HkA/9R5AP/UeQD/1HsA/9mG
        Av7klgi7tbgqVwrjZX0A3FvmANZR/wDUTv8A1E7/ANRO/wDUTv8A1U//ANVP/wHYVf8O5G/kNfGOAAD/
        /wAp7oQfJO2BPiPyijedxkM64pcGjtyIAe3VfAD/03YA/9J2AP/TdgD/0nYA/9J2AP/SdQD/03YA/9V9
        AP/ejATk4KERezraWFkA3169ANhT/gDTTf8A0kv/ANNL/wDTS/8A00v/ANNM/wDTTP8A1E3/BNpY/xDl
        b50y7YUAAP/LAI2/PgD/mwAI6JcEX92KAtPVewD/0nMA/9FxAP/RcQD/0XEA/9FxAP/RcQD/0XEA/9J0
        AP/YgQL745IIrpy8MlME4V+LANpV7QDTTP8A0Un/ANFI/wDRSP8A0Uj/ANFI/wDRSP8A0Un/ANNL/wTZ
        VvIN4maQFep3Ft+SAADhlgMA5J8GJd2NBK3WewH60W8A/89rAP/PawD/z2sA/89rAP/PawD/z2sA/89s
        AP/TdAH/3IUF29ieE3Am21VhANtVygDUS/8A0EX/AM9F/wDPRf8Az0X/AM9E/wDPRP8Az0T/ANBG/wLV
        TP4I3FnCEeNoSyvyjQQV6HMA4ZwBAOWkAQ3ciQGy0W8A/8xkAP/MYwD/zGMA/8xjAP/MYwD/zGMA/8xj
        AP/OZwD/1XYC9+CLCaKAvzVSAt1TmQDUSfMAzkL/AMw//wDMP/8AzD//AMw//wDMP/8AzD//AM1A/wHQ
        RP8F107hDN9cdhnncBQM4GAAHOl4ABrndADflQAA4ZgAJtZ5AOjKWwD/yVkA/8lZAP/JWQD/yVkA/8lZ
        AP/JWQD/zWMB/9h5BdLKmRVnGNhKawDVRtUAzj7/AMk5/wDJOf8AyTn/AMk5/wDJOf8AyTn/AMk5/wDL
        O/8B0kL0A9tQnw/nYi4AAAAAmaY/AOymKgDdtCoA4qorAN6UAADhngAO1ngAuMpYAP/FTQD/xU0A/8VN
        AP/FTQD/xU0A/8pZAf/YdwawcbotSADXQaYAzTj5AMcz/wDFMf8AxTH/AMUx/wDFMf8AxTH/AMUx/wDG
        Mv8Byzb/AdVCxzTTSWfYoipj6JQklOSWJqbnnimA6aorK/GUJgDisy0A4Y0AANh6AADZhAAv0GYAzcVJ
        AP/AQAD/wEAA/8BAAP/CQwD/0WUF2pSnHDgA1Da0AMct/wDBKf8AwCn/AMAp/wDAKf8AwCn/AMAp/wDB
        Kf8AxSv/AM4z5Q7WQISspSti4oIZvNx3GvvacRz/23Me/999If/lkibb6aYpReydJwDUlwAA//8AANVw
        AADVeQAwzFoAzcE8AP+8MgD/vDIA/8A7Af/UZAWqCNQxSwDJJvkAvCD/ALwg/wC8IP8AvCD/ALwg/wC8
        IP8AviH/AMYm+AHSMqpiuzFc3n8WlttuE+/WYxT/1WAW/9djGP/ZZxr/22sb/+B4Hv/pmyfE57EsEwAA
        AADSjgAA3P8AANFkAADScQAwyU8Azr0vAP+3JQD/uywB/89VBMk6vSFMAMke7AC6GP8Atxj/ALcY/wC3
        GP8AuRj/AL8a/wDKI84jyy1syoUXc9tqDtbTWw7/0lQP/9NXEf/WWxL/2F8T/9pjFf/cZxb/4HEZ/+ma
        K9rnsC4fAAAAAAAAAADYjgAA1/8AAM5bAADPaQAxxkcAzromAP+2HAH/wTcC/MhkBogJyx15AMQV6gC7
        Ev8AuRH/ALwT/wDEF+cGzSKMlpobXtpoC7TTVwr7z0sK/9BLC//STwz/1FQN/9dYDv/ZXQ//22EP/99p
        Ef/mhiH766k3fdnBHQIAAAAAAAAAAAAAAADMggAA1P8AAMxTAQDOZQExxUMBz7ohAf+3GgH/wzgC98pc
        BZVUqxNdB8oWeQHMFYQQyxxyZa0cXNNrC47TVQfqzUYG/8xBB//PRQf/0UoI/9NOCf/WUwn/2FcJ/9td
        Cv/gbhD/54oi2+unOGnmvkMJ6bQ6AAAAAAAAAAAAAAAAAAAAAADSggAA0PYCAMxRAQDOZgIxx0UCz7si
        Af+5GgH/wC0C/8tEA9zTUwSp1lkEl9ZXBbDRTgThzEIE/8o7BP/LPQT/zkIF/9FHBf/TSwX/1lAG/9hV
        Bf/cYAj/43sV8uiYK5nrtEMo74EWAOi3QQDpszwAAAAAAAAAAAAAAAAAAAAAAAAAAADMfgIAyeUHAM5V
        AQDQawMyykwCz74nAf+7HAH/viIB/8IrAv/FMQL/xjIC/8czAv/JNgL/zDsC/85AA//RRQP/00oD/9ZO
        A//ZVgP/32wL/uaKH8LppjVM6OpvBOuuPwDXy1cA47REAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AADJfgUAw9gPANFfAgDUdwQyzlcE0MMvAv/AJAH/wigB/8QtAf/HMQH/yTYB/8w7Af/OQAH/0UQB/9RJ
        Av/WTwH/3F8F/+N8FOLnmSl36bpFFOycLQDmuEUA6LNAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAADRgwgAvroWANduCADYhA4y02MN0Mg4A//FLgH/xzIB/8o3AP/MOwD/z0AA/9FF
        Af/USgH/2FYC/99vDPbljB6k6Kk0MO8zAADorjsA86Y2AM2jOgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADMgQsAuaIOANx9FQDcjxwz12wT0M1CA//LOAD/zTwA/9BB
        AP/SRgD/1k4A/9xjBv/jgBXL55woVufQTwfoojAA3b1FAOKvOQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADNhg8As5MAAOGIIQDflSEy23UUy9RS
        Bf/SRwD/1EsA/9lZAv/gdA/m5ZEggeewNhrpjBwA5LA4AOesNADNpz4AAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADMihQAsZQAAOSP
        IgDjniEq4osfp+B7Fe3gdw3w5IkbteejLED/AAAA5qcwAP+JFgDhrzcAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAKAAgQAAAAAAAAAAAAAAACAAAAYAAAAAAAAAAAAAAAIAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwAAAAAAA
        AAAAAAAALAAAAgAAAAAAAAAAAAAAAAAAAAA=
</value>
  </data>
  <data name="toolStrip1.Size" type="System.Drawing.Size, System.Drawing">
    <value>728, 60</value>
  </data>
  <data name="BtnPrevious.TextImageRelation" type="System.Windows.Forms.TextImageRelation, System.Windows.Forms">
    <value>ImageAboveText</value>
  </data>
  <data name="BtnClose.Font" type="System.Drawing.Font, System.Drawing">
    <value>Arial, 10pt, style=Bold</value>
  </data>
  <data name="label8.Location" type="System.Drawing.Point, System.Drawing">
    <value>145, 194</value>
  </data>
  <data name="&gt;&gt;DTPTreasInitBal.Name" xml:space="preserve">
    <value>DTPTreasInitBal</value>
  </data>
  <data name="label5.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;BtnEdit.Name" xml:space="preserve">
    <value>BtnEdit</value>
  </data>
  <data name="label1.Text" xml:space="preserve">
    <value>اسم الخزنة</value>
  </data>
  <data name="TxtLocation.Size" type="System.Drawing.Size, System.Drawing">
    <value>371, 64</value>
  </data>
  <data name="TxtId.TabIndex" type="System.Int32, mscorlib">
    <value>96</value>
  </data>
  <data name="toolStripSeparator7.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 60</value>
  </data>
  <data name="label6.Size" type="System.Drawing.Size, System.Drawing">
    <value>70, 19</value>
  </data>
  <data name="&gt;&gt;BtnNew.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label3.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator1.Name" xml:space="preserve">
    <value>toolStripSeparator1</value>
  </data>
  <data name="TxtName.Location" type="System.Drawing.Point, System.Drawing">
    <value>184, 161</value>
  </data>
  <data name="TxtId.Location" type="System.Drawing.Point, System.Drawing">
    <value>184, 131</value>
  </data>
  <data name="&gt;&gt;label1.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="&gt;&gt;label2.Name" xml:space="preserve">
    <value>label2</value>
  </data>
  <data name="$this.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="BtnOrdersArchive.TextImageRelation" type="System.Windows.Forms.TextImageRelation, System.Windows.Forms">
    <value>ImageAboveText</value>
  </data>
  <data name="&gt;&gt;panel1.Name" xml:space="preserve">
    <value>panel1</value>
  </data>
  <data name="label2.Location" type="System.Drawing.Point, System.Drawing">
    <value>328, 4</value>
  </data>
  <data name="BtnNext.TextImageRelation" type="System.Windows.Forms.TextImageRelation, System.Windows.Forms">
    <value>ImageAboveText</value>
  </data>
  <data name="CBCostCenter.Location" type="System.Drawing.Point, System.Drawing">
    <value>184, 191</value>
  </data>
  <data name="label1.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="CBCostCenter.TabIndex" type="System.Int32, mscorlib">
    <value>574</value>
  </data>
  <data name="panel1.TabIndex" type="System.Int32, mscorlib">
    <value>95</value>
  </data>
  <data name="&gt;&gt;label6.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="toolStrip1.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="&gt;&gt;BtnDelete.Name" xml:space="preserve">
    <value>BtnDelete</value>
  </data>
  <data name="&gt;&gt;TxtName.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label3.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="&gt;&gt;TxtName.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="label5.Size" type="System.Drawing.Size, System.Drawing">
    <value>60, 19</value>
  </data>
  <data name="BtnNew.TextImageRelation" type="System.Windows.Forms.TextImageRelation, System.Windows.Forms">
    <value>ImageAboveText</value>
  </data>
  <data name="BtnDelete.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 58</value>
  </data>
  <data name="&gt;&gt;timer1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Timer, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="BtnOrdersArchive.Size" type="System.Drawing.Size, System.Drawing">
    <value>88, 58</value>
  </data>
  <data name="label2.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="label4.Size" type="System.Drawing.Size, System.Drawing">
    <value>88, 19</value>
  </data>
  <data name="&gt;&gt;TxtInitialBalance.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="BtnSave.Text" xml:space="preserve">
    <value>حفظ  F10</value>
  </data>
  <data name="&gt;&gt;DTPTreasInitBal.Type" xml:space="preserve">
    <value>System.Windows.Forms.DateTimePicker, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;TxtName.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="label6.Location" type="System.Drawing.Point, System.Drawing">
    <value>103, 224</value>
  </data>
  <data name="label4.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label4.Text" xml:space="preserve">
    <value>الرصيد الافتتاحي</value>
  </data>
  <data name="panel1.Size" type="System.Drawing.Size, System.Drawing">
    <value>728, 35</value>
  </data>
  <data name="panel1.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Top</value>
  </data>
  <data name="TxtLocation.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="$this.Language" type="System.Globalization.CultureInfo, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>en-US</value>
  </metadata>
  <metadata name="timer1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>220, 17</value>
  </metadata>
  <metadata name="statusStrip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>238, 95</value>
  </metadata>
  <metadata name="toolStrip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>122, 95</value>
  </metadata>
</root>