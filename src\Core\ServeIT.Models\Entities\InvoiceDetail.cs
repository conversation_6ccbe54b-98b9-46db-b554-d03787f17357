using System.ComponentModel.DataAnnotations;

namespace ServeIT.Models.Entities;

public class InvoiceDetail : BaseEntity<int>
{
    [Required]
    [StringLength(50)]
    public string InvoiceId { get; set; } = string.Empty;

    public int? CategoryId { get; set; }

    [Required]
    [StringLength(50)]
    public string ProductId { get; set; } = string.Empty;

    [StringLength(200)]
    public string? ProductName { get; set; }

    [StringLength(100)]
    public string? ProductSerial { get; set; }

    public DateTime? ProductExpiry { get; set; }

    public int UnitId { get; set; }

    public decimal Price { get; set; }

    public decimal UnitPrice { get; set; }

    public decimal CostPrice { get; set; }

    public decimal Quantity { get; set; }

    public decimal QuantityOut { get; set; }

    public decimal ProductDiscount { get; set; }

    public decimal VAT { get; set; }

    public decimal VATAmount { get; set; }

    [StringLength(500)]
    public string? ProductNotes { get; set; }

    public decimal Total { get; set; }

    // Navigation Properties
    public virtual Invoice Invoice { get; set; } = null!;
    public virtual Category? Category { get; set; }
    public virtual Product Product { get; set; } = null!;
    public virtual Unit Unit { get; set; } = null!;
}
