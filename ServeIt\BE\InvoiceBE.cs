﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ServeIt.BE
{
    class InvoiceBE
    {
        public string ID { get; set; }
        public string No { get; set; }
        public string ReferenceSaleInvID { get; set; }
        public string CostCenterID { get; set; }
        public DateTime Date { get; set; }
        public DateTime TranDate { get; set; }
        public DateTime DateFrom { get; set; }
        public DateTime DateTo { get; set; }
        public string Stock_Id { get; set; }
        public string Cust_Id { get; set; }
        public string Cust_Name { get; set; }
        public int? Kind { get; set; }
        public int? Payment_Kind { get; set; }
        public int? OrderType { get; set; }
        public int? OrderStatus { get; set; }
        public int? DeliveryID { get; set; }
        public string TableID { get; set; }
        public string TableMembersCount { get; set; }
        public string PriceSystem { get; set; }
        public int? Treasury_Id { get; set; }
        public int? Sale_Rep_Id { get; set; }
        public decimal Paid { get; set; }
        public decimal CustomerReceipient { get; set; }
        public int Installment_Count { get; set; }
        public decimal Installment_Paid_Value { get; set; }
        public int Installment_Method { get; set; }
        public int Discount_Kind { get; set; }
        public decimal Discount { get; set; }
        public decimal Discount2 { get; set; }
        public decimal Sales_Tax { get; set; }
        public decimal Addition { get; set; }
        public decimal Service { get; set; }
        public decimal Net_Total { get; set; }
        public string  Notes { get; set; }
        public int Inv_Det_Id { get; set; }
        public int? Cat_Id { get; set; }
        public string Pro_Id { get; set; }
        public string ProName { get; set; }
        public string ProSerial { get; set; }
        public DateTime? ProExpirey { get; set; }
        public int Unit_Id { get; set; }
        public decimal Price { get; set; }
        public decimal CostPrice { get; set; }
        public decimal Quantity { get; set; }
        public decimal QuantityOut { get; set; }
        public decimal Pro_Discount { get; set; }
        public decimal VAT { get; set; }
        public string Pro_notes { get; set; }
        public decimal Total { get; set; }
        public int Invoice_Costing_System { get; set; }
        public string User_Name { get; set; }
        public decimal TurnOverRate { get; set; }
    }
}
