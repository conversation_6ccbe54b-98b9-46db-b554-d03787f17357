namespace ServeIT.Models.Common;

public class ServiceResult
{
    public bool IsSuccess { get; set; }
    public string Message { get; set; } = string.Empty;
    public List<string> Errors { get; set; } = new();

    public static ServiceResult Success(string message = "Operation completed successfully")
    {
        return new ServiceResult { IsSuccess = true, Message = message };
    }

    public static ServiceResult Failure(string message, List<string>? errors = null)
    {
        return new ServiceResult 
        { 
            IsSuccess = false, 
            Message = message, 
            Errors = errors ?? new List<string>() 
        };
    }

    public static ServiceResult Failure(string message, string error)
    {
        return new ServiceResult 
        { 
            IsSuccess = false, 
            Message = message, 
            Errors = new List<string> { error } 
        };
    }
}

public class ServiceResult<T> : ServiceResult
{
    public T? Data { get; set; }

    public static ServiceResult<T> Success(T data, string message = "Operation completed successfully")
    {
        return new ServiceResult<T> { IsSuccess = true, Message = message, Data = data };
    }

    public static new ServiceResult<T> Failure(string message, List<string>? errors = null)
    {
        return new ServiceResult<T> 
        { 
            IsSuccess = false, 
            Message = message, 
            Errors = errors ?? new List<string>() 
        };
    }

    public static new ServiceResult<T> Failure(string message, string error)
    {
        return new ServiceResult<T> 
        { 
            IsSuccess = false, 
            Message = message, 
            Errors = new List<string> { error } 
        };
    }
}
