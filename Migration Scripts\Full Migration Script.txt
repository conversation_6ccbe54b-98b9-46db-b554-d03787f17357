--ALTER TABLE ServeItDB53Alsamady1.dbo.Installment_paids
--ADD cust_id int
--go
--ALTER TABLE ServeItDB53Alsamady1.dbo.product
--DROP COLUMN pic
--go
--ALTER TABLE ServeItDB53Alsamady1.dbo.[customer&vendor]
--DROP COLUMN customer_pic
--go
delete from invoice_detail
delete from invoice_header
delete from installment_paids
delete from stock_permission_detail
delete from stock_permission_header
delete from product_initial_balance_detail
delete from manufacture_order_Finished_detail
delete from manufacture_order_Raw_detail
delete from manufacture_order_expenses
delete from manufacture_order_header
delete from product_unit
delete from product_images
delete from product_collected
delete from product
delete from unit
delete from category
delete from stock
delete from company
delete from treasury_permission
delete from expenses
delete from revenues_items
delete from bank_action
delete from paper
delete from customer_initial_balance
delete from treasury_initial_balance
delete from customer_vendor_picture
delete from [customer&vendor]
delete from treasury
delete from bank_account
delete from bank_branch
delete from bank
delete from sales_representative
delete from bank_action
delete from paper
delete from employee_payroll_details
delete from employee_payroll_header
delete from employee_borrowing_payments
delete from employee_borrowings
delete from employee_salary
delete from employee_vacations
delete from employee_activity
delete from attendance_departure
delete from employee
delete from salary_items
delete from work_periods
delete from jobs
delete from management
delete from Active_permissions
delete from user_log
delete from [user]
delete from Installment_paids
delete from cost_center

go

--insert into management
--select * from ServeItDB53Alsamady1.dbo.management
--go
--insert into jobs
--select * from ServeItDB53Alsamady1.dbo.jobs
--go
--insert into work_periods
--select * from ServeItDB53Alsamady1.dbo.work_periods
--go
--insert into salary_items
--select * from ServeItDB53Alsamady1.dbo.salary_items
--go
--insert into employee
--select * from ServeItDB53Alsamady1.dbo.employee
--go
--insert into attendance_departure
--select * from ServeItDB53Alsamady1.dbo.attendance_departure
--go
--insert into employee_activity
--select * from ServeItDB53Alsamady1.dbo.employee_activity
--go
--insert into employee_vacations
--select * from ServeItDB53Alsamady1.dbo.employee_vacations
--go
--insert into employee_salary
--select * from ServeItDB53Alsamady1.dbo.employee_salary
--go
--insert into employee_borrowings
--select * from ServeItDB53Alsamady1.dbo.employee_borrowings
--go
--insert into employee_borrowing_payments
--select * from ServeItDB53Alsamady1.dbo.employee_borrowing_payments
--go
--insert into employee_payroll_header
--select * from ServeItDB53Alsamady1.dbo.employee_payroll_header
--go
--insert into employee_payroll_details
--select * from ServeItDB53Alsamady1.dbo.employee_payroll_details
--go


go
insert into cost_center
select * from ServeItDB53Alsamady1.dbo.cost_center
go
insert into [user]
select * from ServeItDB53Alsamady1.dbo.[user]
go
SET IDENTITY_INSERT ServeItDB6Alsamady1.dbo.Active_permissions on

go

insert into Active_permissions(id,[user_name],permission_name)
select id,[user_name],permission_name from ServeItDB6.dbo.Active_permissions
go
SET IDENTITY_INSERT ServeItDB6Alsamady1.dbo.Active_permissions off
go
insert into company
select * from ServeItDB53Alsamady1.dbo.company
go

insert into stock
select * from ServeItDB53Alsamady1.dbo.stock
go
insert into category
select * from ServeItDB53Alsamady1.dbo.category
go

insert into unit
select * from ServeItDB53Alsamady1.dbo.unit
go
insert into product
select * from ServeItDB53Alsamady1.dbo.product
go
insert into product_unit
select * from ServeItDB53Alsamady1.dbo.product_unit
go
insert into product_images (pro_id)
select pro_id from ServeItDB53Alsamady1.dbo.product
go
insert into product_collected
select *from ServeItDB53Alsamady1.dbo.product_collected
go
insert into sales_representative
select * from ServeItDB53Alsamady1.dbo.sales_representative
go
insert into treasury
select * from ServeItDB53Alsamady1.dbo.treasury
go

SET IDENTITY_INSERT ServeItDB6Alsamady1.dbo.treasury_initial_balance on
go

insert into treasury_initial_balance(treausry_init_bal_id,treasury_id,quantity,[date])
select treausry_init_bal_id,treasury_id,quantity,[date] from ServeItDB53Alsamady1.dbo.treasury_initial_balance

go
SET IDENTITY_INSERT ServeItDB6Alsamady1.dbo.treasury_initial_balance off
go
insert into [customer&vendor]
select * from ServeItDB53Alsamady1.dbo.[customer&vendor]
go

SET IDENTITY_INSERT ServeItDB6Alsamady1.dbo.customer_initial_balance on
go


insert into customer_initial_balance (cust_init_bal_id,cust_id,quantity,[date])
select cust_init_bal_id,cust_id,quantity,[date] from ServeItDB53Alsamady1.dbo.customer_initial_balance
go

SET IDENTITY_INSERT ServeItDB6Alsamady1.dbo.customer_initial_balance off
go
insert into customer_vendor_picture (cust_id)
select cust_id from [customer&vendor]
go
insert into product_initial_balance_detail
select * from ServeItDB53Alsamady1.dbo.product_initial_balance_detail
go
insert into stock_permission_header
select * from ServeItDB53Alsamady1.dbo.stock_permission_header
go

insert into stock_permission_detail
select * from ServeItDB53Alsamady1.dbo.stock_permission_detail
go
--insert into manufacture_order_header
--select * from ServeItDB53Alsamady1.dbo.manufacture_order_header
--go
--insert into manufacture_order_expenses
--select * from ServeItDB53Alsamady1.dbo.manufacture_order_expenses
--go
--insert into manufacture_order_Raw_detail
--select * from ServeItDB53Alsamady1.dbo.manufacture_order_Raw_detail
--go
--insert into manufacture_order_Finished_detail
--select * from ServeItDB53Alsamady1.dbo.manufacture_order_Finished_detail

--go
insert into invoice_header
select * from ServeItDB53Alsamady1.dbo.invoice_header
go
insert into invoice_detail
select * from ServeItDB53Alsamady1.dbo.invoice_detail
go

insert into expenses
select * from ServeItDB53Alsamady1.dbo.expenses
go
insert into revenues_items
select * from ServeItDB53Alsamady1.dbo.revenues_items
go
insert into treasury_permission
select * from ServeItDB53Alsamady1.dbo.treasury_permission
go
insert into bank
select * from ServeItDB53Alsamady1.dbo.bank
go
insert into bank_branch
select * from ServeItDB53Alsamady1.dbo.bank_branch
go
insert into bank_account
select * from ServeItDB53Alsamady1.dbo.bank_account
go
insert into bank_action
select * from ServeItDB53Alsamady1.dbo.bank_action

go
insert into paper
select * from ServeItDB53Alsamady1.dbo.paper
go
insert into Installment_paids
select * from ServeItDB53Alsamady1.dbo.Installment_paids

SET IDENTITY_INSERT ServeItDB6Alsamady1.dbo.Active_permissions off
go
SET IDENTITY_INSERT ServeItDB6Alsamady1.dbo.customer_initial_balance off
go
SET IDENTITY_INSERT ServeItDB6Alsamady1.dbo.treasury_initial_balance off
