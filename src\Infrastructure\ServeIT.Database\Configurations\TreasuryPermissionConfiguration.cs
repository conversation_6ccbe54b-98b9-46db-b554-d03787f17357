using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ServeIT.Models.Entities;

namespace ServeIT.Database.Configurations;

public class TreasuryPermissionConfiguration : IEntityTypeConfiguration<TreasuryPermission>
{
    public void Configure(EntityTypeBuilder<TreasuryPermission> builder)
    {
        builder.HasKey(tp => tp.Id);

        builder.Property(tp => tp.Number)
            .IsRequired();

        builder.Property(tp => tp.Date)
            .IsRequired();

        builder.Property(tp => tp.Value)
            .HasColumnType("decimal(18,2)")
            .IsRequired();

        builder.Property(tp => tp.Notes)
            .HasMaxLength(1000);

        // Configure relationship with main Treasury
        builder.HasOne(tp => tp.Treasury)
            .WithMany(t => t.TreasuryPermissions)
            .HasForeignKey(tp => tp.TreasuryId)
            .OnDelete(DeleteBehavior.NoAction);

        // Configure relationship with TreasuryFrom
        builder.HasOne(tp => tp.TreasuryFrom)
            .WithMany()
            .HasForeignKey(tp => tp.TreasuryFromId)
            .OnDelete(DeleteBehavior.NoAction);

        // Configure relationship with TreasuryTo
        builder.HasOne(tp => tp.TreasuryTo)
            .WithMany()
            .HasForeignKey(tp => tp.TreasuryToId)
            .OnDelete(DeleteBehavior.NoAction);

        // Configure relationship with Customer
        builder.HasOne(tp => tp.Customer)
            .WithMany()
            .HasForeignKey(tp => tp.CustomerId)
            .OnDelete(DeleteBehavior.SetNull);

        // Configure relationship with Expense
        builder.HasOne(tp => tp.Expense)
            .WithMany()
            .HasForeignKey(tp => tp.ExpenseId)
            .OnDelete(DeleteBehavior.SetNull);

        // Configure relationship with Revenue
        builder.HasOne(tp => tp.Revenue)
            .WithMany()
            .HasForeignKey(tp => tp.RevenueId)
            .OnDelete(DeleteBehavior.SetNull);

        // Configure relationship with CostCenter
        builder.HasOne(tp => tp.CostCenter)
            .WithMany()
            .HasForeignKey(tp => tp.CostCenterId)
            .OnDelete(DeleteBehavior.SetNull);
    }
}
