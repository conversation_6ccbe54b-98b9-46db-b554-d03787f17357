html, body {
    font-family: '<PERSON><PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
    height: 100%;
}

.page {
    position: relative;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.sidebar {
    background-image: linear-gradient(180deg, rgb(5, 39, 103) 0%, #3a0647 70%);
    width: 260px;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
}

.top-row {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    justify-content: space-between;
    height: 3.5rem;
    display: flex;
    align-items: center;
}

.navbar-brand {
    font-size: 1.1rem;
    font-weight: 600;
}

.navbar-toggler {
    background-color: rgba(255, 255, 255, 0.1);
}

.nav-scrollable {
    display: none;
}

.navbar-toggler.collapsed ~ .nav-scrollable {
    display: block;
}

main {
    flex: 1;
    margin-left: 260px;
}

main .top-row {
    position: sticky;
    top: 0;
    z-index: 999;
}

.nav-item {
    font-size: 0.9rem;
    position: relative;
}

.nav-item::after {
    position: absolute;
    bottom: 0;
    left: 0;
    top: 0;
    content: "";
    width: 3px;
    background-color: #fff;
    transform: scaleY(0);
    transition: transform 150ms;
}

.nav-item:first-of-type {
    padding-top: 1rem;
}

.nav-item:last-of-type {
    padding-bottom: 1rem;
}

.nav-link {
    color: rgba(255, 255, 255, 0.8);
    border-radius: 4px;
    height: 3rem;
    display: flex;
    align-items: center;
    line-height: 3rem;
    transition: all 0.15s ease-in-out;
}

.nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
}

.nav-link.active {
    background-color: rgba(255, 255, 255, 0.25);
    color: white;
}

.nav-link.active::after {
    transform: scaleY(1);
}

.content {
    padding-top: 1.1rem;
}

.navbar-toggler {
    background-color: rgba(255, 255, 255, 0.1);
}

.valid.modified:not([type=checkbox]) {
    outline: 1px solid #26b050;
}

.invalid {
    outline: 1px solid red;
}

.validation-message {
    color: red;
}

/* Modal Enhancements */
.blazored-modal-container {
    z-index: 1055;
}

.blazored-modal-overlay {
    background-color: rgba(0, 0, 0, 0.5);
}

.blazored-modal {
    max-width: 800px;
    margin: 1.75rem auto;
}

.modal-content {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.modal-header {
    border-bottom: 1px solid #dee2e6;
    border-radius: 0.5rem 0.5rem 0 0;
}

.modal-footer {
    border-top: 1px solid #dee2e6;
    border-radius: 0 0 0.5rem 0.5rem;
}

/* Form Enhancements */
.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.form-label i {
    margin-right: 0.25rem;
    color: #6c757d;
}

.form-control:focus,
.form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Loading States */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Toast Overrides */
.success-toast-override {
    background-color: #198754 !important;
}

/* Button Enhancements */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
}

.btn i {
    margin-right: 0.25rem;
}

/* Alert Enhancements */
.alert {
    border-radius: 0.375rem;
}

.alert i {
    margin-right: 0.5rem;
}

#blazor-error-ui {
    background: lightyellow;
    bottom: 0;
    box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
    display: none;
    left: 0;
    padding: 0.6rem 1.25rem 0.7rem 1.25rem;
    position: fixed;
    width: 100%;
    z-index: 1000;
}

#blazor-error-ui .dismiss {
    cursor: pointer;
    position: absolute;
    right: 0.75rem;
    top: 0.5rem;
}

.bi-house-door-fill-nav-menu {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='white' viewBox='0 0 16 16'%3e%3cpath d='M6.5 14.5v-3.505c0-.245.25-.495.5-.495h2c.25 0 .5.25.5.5v3.5a.5.5 0 0 0 .5.5h4a.5.5 0 0 0 .5-.5v-7a.5.5 0 0 0-.146-.354L13 5.793V2.5a.5.5 0 0 0-.5-.5h-1a.5.5 0 0 0-.5.5v1.293L8.354 1.146a.5.5 0 0 0-.708 0l-6 6A.5.5 0 0 0 1.5 7.5v7a.5.5 0 0 0 .5.5h4a.5.5 0 0 0 .5-.5Z'/%3e%3c/svg%3e");
}

/* Custom styles for better UX */
.card {
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.btn {
    transition: all 0.15s ease-in-out;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.badge {
    font-size: 0.75em;
}

/* Loading spinner */
.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Responsive design */
@media (max-width: 767.98px) {
    .sidebar {
        margin-left: -260px;
        transition: margin-left 0.3s ease-in-out;
    }

    .sidebar.show {
        margin-left: 0;
    }

    main {
        margin-left: 0;
    }

    .navbar-toggler {
        display: block;
    }

    .nav-scrollable {
        display: block;
    }
}

/* Toast customizations */
.success-toast-override {
    background-color: #28a745;
}

/* Custom pagination */
.pagination .page-link {
    color: #0d6efd;
    border-color: #dee2e6;
}

.pagination .page-item.active .page-link {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.pagination .page-link:hover {
    color: #0a58ca;
    background-color: #e9ecef;
    border-color: #dee2e6;
}
