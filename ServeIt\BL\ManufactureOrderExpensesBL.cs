﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;

namespace ServeIt.BL
{
    class ManufactureOrderExpensesBL
    {
        public string ManufactureOrderID { get; set; }
        public string ManufactureOrderExpenseID { get; set; }
        public string ExpenseID { get; set; }
        public DateTime ExpenseDate { get; set; }
        public decimal ExpenseValue { get; set; }
        public string ExpenseEnterMethod { get; set; }

        public string AddOrUpdateManufactureExpense(ManufactureOrderExpensesBL manf_exp_bl)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[6];
                para[0] = new SqlParameter("@check","a");
                para[1] = new SqlParameter("@manufacture_order_id",manf_exp_bl.ManufactureOrderID);
                para[2] = new SqlParameter("@manufacture_order_expense_id",manf_exp_bl.ManufactureOrderExpenseID);
                para[3] = new SqlParameter("@expense_id",manf_exp_bl.ExpenseID);
                para[4] = new SqlParameter("@expense_value",manf_exp_bl.ExpenseValue);
                para[5] = new SqlParameter("@expense_enter_method",manf_exp_bl.ExpenseEnterMethod);
                return DAL.InsUpdDel("ManageManufactureOrderExpenses", para);
            }
        }

        public string DeleteManufactureExpenseRow(string ManufactureOrderID, string ManufactureExpenseID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[3];
                para[0] = new SqlParameter("@check", "d");
                para[1] = new SqlParameter("@manufacture_order_id", ManufactureOrderID);
                para[2] = new SqlParameter("@manufacture_order_expense_id", ManufactureExpenseID);
                return DAL.InsUpdDel("ManageManufactureOrderExpenses", para);
            }
        }

        public string DeleteManufactureExpenses(string ManufactureOrderID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "r");
                para[1] = new SqlParameter("@manufacture_order_id", ManufactureOrderID);
                return DAL.InsUpdDel("ManageManufactureOrderExpenses", para);
            }
        }

        public DataTable GetManufactureOrderExpenseByID(string ManufactureOrderID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "g");
                para[1] = new SqlParameter("@manufacture_order_id", ManufactureOrderID);
                return DAL.GetData("ManageManufactureOrderExpenses", para);
            }
        }
    }
}
