//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace ServeIt.DA
{
    using System;
    
    public partial class RPTUserWorkShiftPrint_Result
    {
        public Nullable<int> shift_id { get; set; }
        public string user_name { get; set; }
        public string treasury_name { get; set; }
        public string closing_treasury { get; set; }
        public Nullable<System.DateTime> shift_date { get; set; }
        public Nullable<System.DateTime> actual_start_time { get; set; }
        public Nullable<System.DateTime> actual_end_time { get; set; }
        public Nullable<decimal> previous_balance { get; set; }
        public Nullable<decimal> sales { get; set; }
        public Nullable<decimal> network_sales { get; set; }
        public Nullable<decimal> other_sales { get; set; }
        public Nullable<decimal> collected_installment { get; set; }
        public Nullable<decimal> sales_returns { get; set; }
        public Nullable<decimal> purchases { get; set; }
        public Nullable<decimal> purchases_return { get; set; }
        public Nullable<decimal> expenses { get; set; }
        public Nullable<decimal> cash_deposit_voucher { get; set; }
        public Nullable<decimal> payment_vouchers { get; set; }
        public Nullable<decimal> receipt_vouchers { get; set; }
        public Nullable<decimal> remittance_vouchers_in { get; set; }
        public Nullable<decimal> remittance_vouchers_out { get; set; }
        public Nullable<decimal> cash_withdrawals { get; set; }
        public Nullable<decimal> cash_remaining { get; set; }
        public Nullable<decimal> net_cash_drawer { get; set; }
    }
}
