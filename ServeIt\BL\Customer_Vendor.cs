﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ServeIt.BL
{
    class Customer_Vendor
    {


        #region Manage Customers & Vendors

        public DataTable GetCustomerKind()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetData("getcustomerkind", null);
            }
        }


        public DataTable GetAllCustomers()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetData("getallcustomer", null);
            }
        }

        public DataTable SearchCustomerOrVendors(BE.Customer_Vendor custbe)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[7];
                para[0] = new SqlParameter("@cust_name", custbe.CustomerName);
                para[1] = new SqlParameter("@sales_rep_id", custbe.CustomerRepresentativeID);
                para[2] = new SqlParameter("@cust_kind", custbe.CustomerKind);
                para[3] = new SqlParameter("@city", custbe.CustomerCity);
                para[4] = new SqlParameter("@phone", custbe.CustomerPhone);
                para[5] = new SqlParameter("@mobile_no", custbe.CustomerMobile);
                para[6] = new SqlParameter("@address", custbe.CustomerAddress);
                return DAL.GetData("SearchCustomerOrVendors", para);
            }
        }

        public DataTable GetCustomerById(string cust_id) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@cust_id", cust_id);
                return DAL.GetData("GetCustomerById", para);
            }
        }

        public DataTable GetPreviousCustomerID(string CurrentCustID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@current_cust_id", CurrentCustID);
                return DAL.GetData("GetPreviousCustomerID", para);
            }
        }

        public DataTable GetNextCustomerID(string CurrentCustID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@current_cust_id", CurrentCustID);
                return DAL.GetData("GetNextCustomerID", para);
            }
        }

        public DataTable GetCustomerPriceSystemByCustID(string CustID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@cust_id", CustID);
                return DAL.GetData("GetCustomerPriceSystemByCustID", para);
            }
        }

        public DataTable GetCustomerVendorPictureByCustID(string cust_id) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@cust_id", cust_id);
                return DAL.GetData("GetCustomerVendorPictureByCustID", para);
            }
        }

        public DataTable GetMaintenanceCustomerDetailsByCustomerById(string cust_id)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@cust_id", cust_id);
                return DAL.GetData("GetMaintenanceCustomerDetailsByCustomerById", para);
            }
        }

        public string AddOrUpdateCustoemr(BE.Customer_Vendor mycust)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[20];
                para[0] = new SqlParameter("@check", "a");
                para[1] = new SqlParameter("@cust_id", mycust.CustomerID);
                para[2] = new SqlParameter("@cust_name", mycust.CustomerName);
                para[3] = new SqlParameter("@adding_date", mycust.CustomerAddingDate);
                para[4] = new SqlParameter("@cust_kind", mycust.CustomerKind);
                para[5] = new SqlParameter("@initial_balance", mycust.CustomerInitialBalance);
                para[6] = new SqlParameter("@price_system", mycust.CustomerPriceSystem);
                para[7] = new SqlParameter("@cust_picture", mycust.CustomerPicture);
                para[8] = new SqlParameter("@city", mycust.CustomerCity);
                para[9] = new SqlParameter("@area", mycust.CustomerArea);
                para[10] = new SqlParameter("@address", mycust.CustomerAddress);
                para[11] = new SqlParameter("@mobile_no", mycust.CustomerMobile);
                para[12] = new SqlParameter("@phone", mycust.CustomerPhone);
                para[13] = new SqlParameter("@sales_rep_id", mycust.CustomerRepresentativeID);
                para[14] = new SqlParameter("@active", mycust.CustomerIsActive);
                para[15] = new SqlParameter("@default_customer", mycust.DefaultCustomer);
                para[16] = new SqlParameter("@notes", mycust.CustomerNotes);
                para[17] = new SqlParameter("@user_name", mycust.CustomerUserName);
                para[18] = new SqlParameter("@box1_value", mycust.CustomerBox1Value);
                para[19] = new SqlParameter("@box2_value", mycust.CustomerBox2Value);
                return DAL.InsUpdDel("managecustomer_vendors", para);
            }
        }


        public string DeleteCustomer(BE.Customer_Vendor mycust)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "d");
                para[1] = new SqlParameter("@cust_id", mycust.CustomerID);
                return DAL.InsUpdDel("managecustomer_vendors", para);
            }
        }


        public string GetMaxCustId()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetValue("getmaxcustid");
            }
        }

        //public string SetDefaultCustomer (string cust_id)
        //{
        //    using (DA.DataAccess DAL = new DA.DataAccess())
        //    {
        //        SqlParameter[] para = new SqlParameter[1];
        //        para[0] = new SqlParameter("", cust_id);
        //        return DAL.InsUpdDel("", para);
        //    }
        //}

        public DataTable GetDefaultCustomer() 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetData("GetDefaultCustomer", null);
            }
        }

        #endregion


        #region  Report Methods

        public DataTable GetCustBalanceMovement(BE.Customer_Vendor Cust_vend)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[3];
                para[0] = new SqlParameter("@cust_id", Cust_vend.CustomerID);
                para[1] = new SqlParameter("@datefrom", Cust_vend.CustomerDateFrom);
                para[2] = new SqlParameter("@dateto", Cust_vend.CustomerDateTo);
                return DAL.GetData("RPTGetCustBalanceMovement", para);
            }
        }

        public DataTable GetCustBalanceMovementDetail(BE.Customer_Vendor Cust_vend)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[3];
                para[0] = new SqlParameter("@cust_id", Cust_vend.CustomerID);
                para[1] = new SqlParameter("@datefrom", Cust_vend.CustomerDateFrom);
                para[2] = new SqlParameter("@dateto", Cust_vend.CustomerDateTo);
                return DAL.GetData("RPTGetCustBalanceMovementDetail", para);
            }
        }

        public DataTable GetCustomerBalance(string CustID, DateTime DateTo) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@cust_id", CustID);
                para[1] = new SqlParameter("@dateto", DateTo);
                return DAL.GetData("RPTGetCustomerBalance", para);
            }
        }

        public DataTable GetAllCustomersBalance(BE.Customer_Vendor custbe) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[6];
                para[0] = new SqlParameter("@cust_kind", custbe.CustomerKind);
                para[1] = new SqlParameter("@cust_id", custbe.CustomerID);
                para[2] = new SqlParameter("@sales_rep_id", custbe.CustomerRepresentativeID);
                para[3] = new SqlParameter("@datefrom", custbe.CustomerDateFrom);
                para[4] = new SqlParameter("@dateto", custbe.CustomerDateTo);
                para[5] = new SqlParameter("@zeroaccounts", custbe.CustomerShowZeroAccounts);
                return DAL.GetData("RPTGetAllCustomersBalance", para);
            }
        }

        public string GetTotalCustomerBalance(BE.Customer_Vendor custbe) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@cust_id", custbe.CustomerID);
                return DAL.GetValue("GetTotalCustomerBalance", para);
            }
        }

         

        #endregion
    }
}
