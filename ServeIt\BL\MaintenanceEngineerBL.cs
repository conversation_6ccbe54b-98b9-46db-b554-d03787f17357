﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;

namespace ServeIt.BL
{
    class MaintenanceEngineerBL
    {
        public string EngineerID { get; set; }
        public string EngineerName { get; set; }
        public string EngineerAddress { get; set; }
        public bool EngineerIsActive { get; set; }

        public string GetMaxMaintenanceEngineer() 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetValue("GetMaxMaintenanceEngineer");
            }
        }

        public string AddOrUpdateMaintenanceEngineer(MaintenanceEngineerBL maint_eng) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[5];
                para[0] = new SqlParameter("@check", "a");
                para[1] = new SqlParameter("@eng_id", maint_eng.EngineerID);
                para[2] = new SqlParameter("@eng_name", maint_eng.EngineerName);
                para[3] = new SqlParameter("@address", maint_eng.EngineerAddress);
                para[4] = new SqlParameter("@is_active", maint_eng.EngineerIsActive);
                return DAL.InsUpdDel("ManageMaintenanceEngineers", para);
            }
        }

        public string DeleteMaintenanceEngineer(string EngineerID)
        {
            using(DA.DataAccess DAL = new DA.DataAccess())
	        {
		        SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "d");
                para[1] = new SqlParameter("@eng_id", EngineerID);
                return DAL.InsUpdDel("ManageMaintenanceEngineers", para);
	        }
        }

        public DataTable SearchMaintenanceEngineer(string EngineerName) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@eng_name", EngineerName);
                return DAL.GetData("SearchMaintenanceEngineer", para);
            }
        }

        public DataTable GetAllMaintenanceEngineers() 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetData("GetAllMaintenanceEngineers", null);
            }
        }
    }
}
