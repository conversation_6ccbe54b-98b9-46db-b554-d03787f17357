﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Data.SqlClient;
using System.Data;

namespace ServeIt.BL
{
    class CompanyDefintion
    {

        public string AddOrUpdateCompanyDetail(BE.CompanyDefinition mycompany)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[14];
                para[0] = new SqlParameter("@check", "a");
                para[1] = new SqlParameter("@company_id", mycompany.Id);
                para[2] = new SqlParameter("@company_name", mycompany.Name);
                para[3] = new SqlParameter("@activity", mycompany.Activity);
                para[4] = new SqlParameter("@first_period_date", mycompany.FirstPeriodDate);
                para[5] = new SqlParameter("@last_period_date", mycompany.LastPeriodDate);
                para[6] = new SqlParameter("@commercial_registration", mycompany.Commercial_Registration);
                para[7] = new SqlParameter("@tax_card", mycompany.Tax_Card);
                para[8] = new SqlParameter("@address", mycompany.Address);
                para[9] = new SqlParameter("@phone", mycompany.Phone);
                para[10] = new SqlParameter("@mobile", mycompany.Mobile);
                para[11] = new SqlParameter("@email", mycompany.Email);
                para[12] = new SqlParameter("@logo", mycompany.Logo);
                para[13] = new SqlParameter("@background", mycompany.BackGround);
                return DAL.InsUpdDel("managecompany", para);
            }
        }


        public DataTable GetCompanyDetails()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetData("getcompanydetail", null);
            }
        }

    }
}
