﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ServeIt.BL
{
    class BankAccountBL
    {


        public string AddOrUpdateBankAccount(BE.BankAccountBE accountbe)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[7];
                para[0] = new SqlParameter("@check", "a");
                para[1] = new SqlParameter("@id", accountbe.Id);
                para[2] = new SqlParameter("@account_no", accountbe.AccountNo);
                para[3] = new SqlParameter("@bank_id", accountbe.BankId);
                para[4] = new SqlParameter("@branch_id", accountbe.BranchId);
                para[5] = new SqlParameter("@currency", accountbe.Currency);
                para[6] = new SqlParameter("@note", accountbe.Note);
                return DAL.InsUpdDel("managebankaccount", para);
            }
        }

        public string DeleteBankAccount(BE.BankAccountBE accountbe)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "d");
                para[1] = new SqlParameter("@id", accountbe.Id);
                return DAL.InsUpdDel("managebankaccount", para);
            }
        }

        public string GetMaxBankAccountId() 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetValue("getmaxbankaccountid");
            }
        }

        public DataTable GetAllBankAccounts() 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetData("getallbankaccounts", null);
            }
        }

        public DataTable GetAccountByBranchId(int bank_branch_id) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@branch_id", bank_branch_id);
                return DAL.GetData("GetAccountByBranchId", para);
            }
        }
    }
}
