//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace ServeIt.DA
{
    using System;
    using System.Collections.Generic;
    
    public partial class stock_permission_header
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public stock_permission_header()
        {
            this.stock_permission_detail = new HashSet<stock_permission_detail>();
        }
    
        public int per_id { get; set; }
        public Nullable<int> maintenance_id { get; set; }
        public Nullable<int> per_no { get; set; }
        public Nullable<int> cost_center_id { get; set; }
        public Nullable<int> per_kind_id { get; set; }
        public Nullable<int> stock_id { get; set; }
        public Nullable<int> stock_id_from { get; set; }
        public Nullable<int> stock_id_to { get; set; }
        public Nullable<System.DateTime> per_date { get; set; }
        public string notes { get; set; }
        public string user_name { get; set; }
    
        public virtual cost_center cost_center { get; set; }
        public virtual stock stock { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<stock_permission_detail> stock_permission_detail { get; set; }
        public virtual stock_permission_kind stock_permission_kind { get; set; }
        public virtual user user { get; set; }
        public virtual stock_permission_kind stock_permission_kind1 { get; set; }
    }
}
