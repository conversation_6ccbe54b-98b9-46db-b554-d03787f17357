﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;

namespace ServeIt.BL
{
    class EmployeeBL
    {
        public string EmployeeID { get; set; }
        public string EmployeeName { get; set; }
        public string FingerPrintID { get; set; }
        public string EmployeeFingerPrintID { get; set; }
        public string Address { get; set; }
        public string Phone { get; set; }
        public string Mobile { get; set; }
        public DateTime BirthDate { get; set; }
        public DateTime? HiringDate { get; set; }
        public string InsuranceNo { get; set; }
        public string InsuranceFacility { get; set; }
        public string NationalID { get; set; }
        public string PassportNo { get; set; }
        public string ManagementID { get; set; }
        public string DepartmentID { get; set; }
        public string Nationality { get; set; }
        public string JobID { get; set; }
        public string WorkPeriod { get; set; }
        public string DrivingLicense { get; set; }
        public bool IsRequiredForRecruitment { get; set; }
        public bool IsInsured { get; set; }
        public bool IsMarried { get; set; }
        public bool IsActive { get; set; }
        public string EducationalQualification { get; set; }
        public DateTime GraduationDate { get; set; }
        public string UniversityEvaluation{ get; set; }
        public string YearsOfExperience { get; set; }
        public string Notes { get; set; }
        public string Skills { get; set; }
        public string AnnualVacationsNo { get; set; }
        public string MonthlyVacationsNo { get; set; }
        public DateTime? HiredFromDate { get; set; }
        public DateTime? HiredToDate { get; set; }

        public string AddOrUpdateEmployee(EmployeeBL emp_bl)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[28];
                para[0] = new SqlParameter("@check", "a");
                para[1] = new SqlParameter("@employee_id", emp_bl.EmployeeID);
                para[2] = new SqlParameter("@employee_name", emp_bl.EmployeeName);
                para[3] = new SqlParameter("@fingerprint_id", emp_bl.FingerPrintID);
                para[4] = new SqlParameter("@employee_fingerprint_id", emp_bl.EmployeeFingerPrintID);
                para[5] = new SqlParameter("@employee_address", emp_bl.Address);
                para[6] = new SqlParameter("@phone", emp_bl.Phone);
                para[7] = new SqlParameter("@mobile", emp_bl.Mobile);
                para[8] = new SqlParameter("@birth_date", emp_bl.BirthDate);
                para[9] = new SqlParameter("@hiring_date", emp_bl.HiringDate);
                para[10] = new SqlParameter("@insurance_no", emp_bl.InsuranceNo);
                para[11] = new SqlParameter("@insurance_facility", emp_bl.InsuranceFacility);
                para[12] = new SqlParameter("@national_id", emp_bl.NationalID);
                para[13] = new SqlParameter("@passport_no", emp_bl.PassportNo);
                para[14] = new SqlParameter("@management_id", emp_bl.ManagementID);
                para[15] = new SqlParameter("@nationality", emp_bl.Nationality);
                para[16] = new SqlParameter("@job_id", emp_bl.JobID);
                para[17] = new SqlParameter("@work_period", emp_bl.WorkPeriod);
                para[18] = new SqlParameter("@driving_license", emp_bl.DrivingLicense);
                para[19] = new SqlParameter("@is_required_for_recruitment", emp_bl.IsRequiredForRecruitment);
                para[20] = new SqlParameter("@is_insured", emp_bl.IsInsured);
                para[21] = new SqlParameter("@is_married", emp_bl.IsMarried);
                para[22] = new SqlParameter("@is_active", emp_bl.IsActive);
                para[23] = new SqlParameter("@educational_qualification", emp_bl.EducationalQualification);
                para[24] = new SqlParameter("@graduation_date", emp_bl.GraduationDate);
                para[25] = new SqlParameter("@university_evaluation", emp_bl.UniversityEvaluation);
                para[26] = new SqlParameter("@experience_years", emp_bl.YearsOfExperience);
                para[27] = new SqlParameter("@notes", emp_bl.Notes);
                return DAL.InsUpdDel("ManageEmployee", para);
            }
        }

        public string DeleteEmployee(string EmployeeID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "d");
                para[1] = new SqlParameter("@employee_id", EmployeeID);
                return DAL.InsUpdDel("ManageEmployee", para);
            }
        }

        public string GetMaxEmployeeID()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@check", "m");
                return DAL.GetValue("ManageEmployee", para);
            }
        }

        public DataTable GetEmployeeByID(string EmployeeID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "g");
                para[1] = new SqlParameter("@employee_id", EmployeeID);
                return DAL.GetData("ManageEmployee", para);
            }
        }

        public DataTable GetEmployeeIDByFingerPrintAndEmployeeFingerPrintID(string FingerPrintID, string EmployeeFingerPrintID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@finerprint_id", FingerPrintID);
                para[1] = new SqlParameter("@employee_fingerprint_id", EmployeeFingerPrintID);
                return DAL.GetData("GetEmpoyeeIDByFingerPrintIDAndEmpFingerPrintID", para);
            }
        }

        public DataTable SearchEmployee(EmployeeBL emp_bl)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[7];
                para[0] = new SqlParameter("@check", "s");
                para[1] = new SqlParameter("@employee_name", emp_bl.EmployeeName);
                para[2] = new SqlParameter("@management_id", emp_bl.ManagementID);
                para[3] = new SqlParameter("@hiring_date", emp_bl.HiringDate);
                para[4] = new SqlParameter("@date_from", emp_bl.HiredFromDate);
                para[5] = new SqlParameter("@date_to", emp_bl.HiredToDate);
                para[6] = new SqlParameter("@is_active", emp_bl.IsActive);
                return DAL.GetData("ManageEmployee", para);
            }
        }

        public DataTable GetEmployeeSalaryItems(string EmployeeID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@employee_id", EmployeeID);
                return DAL.GetData("GetEmployeeSalaryItems", para);
            }
        }

        public DataTable GetEmployeeDefaultSalaryItems()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetData("GetEmployeeDefaultSalaryItems", null);
            }
        }
    }
}
