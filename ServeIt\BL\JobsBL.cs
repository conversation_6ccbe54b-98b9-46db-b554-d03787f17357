﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;

namespace ServeIt.BL
{
    class JobsBL
    {
        public string JobID { get; set; }
        public string JobName { get; set; }
        public string Notes { get; set; }
        public string UserName { get; set; }
        
        public string AddOrUpdateJob(JobsBL job_bl)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[5];
                para[0] = new SqlParameter("@check", "a");
                para[1] = new SqlParameter("@job_id", job_bl.JobID);
                para[2] = new SqlParameter("@job_name", job_bl.JobName);
                para[3] = new SqlParameter("@notes", job_bl.Notes);
                para[4] = new SqlParameter("@user_name", job_bl.UserName);
                return DAL.InsUpdDel("ManageJobs", para);
            }
        }

        public string DeleteJob(string JobID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "d");
                para[1] = new SqlParameter("@job_id", JobID);
                return DAL.InsUpdDel("ManageJobs", para);
            }
        }

        public string GetMaxJobID()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@check", "m");
                return DAL.GetValue("ManageJobs", para);
            }
        }

        public DataTable GetJobByID(string JobID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "g");
                para[1] = new SqlParameter("@job_name", JobID);
                return DAL.GetData("ManageJobs", para);
            }
        }

        public DataTable SearchJob(string JobName)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "s");
                para[1] = new SqlParameter("@job_name", JobName);
                return DAL.GetData("ManageJobs", para);
            }
        }
    }
}
