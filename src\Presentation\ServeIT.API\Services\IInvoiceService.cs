using ServeIT.Models.DTOs;
using ServeIT.Models.Common;

namespace ServeIT.API.Services;

public interface IInvoiceService
{
    Task<ServiceResult<IEnumerable<InvoiceDto>>> GetAllInvoicesAsync();
    Task<ServiceResult<InvoiceDto>> GetInvoiceByIdAsync(string id);
    Task<ServiceResult<PagedResult<InvoiceDto>>> GetInvoicesPagedAsync(PagingParameters parameters);
    Task<ServiceResult<InvoiceDto>> CreateInvoiceAsync(InvoiceDto invoiceDto);
    Task<ServiceResult<InvoiceDto>> UpdateInvoiceAsync(string id, InvoiceDto invoiceDto);
    Task<ServiceResult> DeleteInvoiceAsync(string id);
}
