﻿
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Drawing.Imaging;
using System.Drawing.Printing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace ServeIt.BL
{
    public static class Utilities
    {
        public enum WorkShiftStatus
        {
            NotFound,
            Opened,
            closed
        }

        public static WorkShiftStatus GetWorkShiftStatus(string UserName, DateTime MyDate)
        {
            if (Properties.Settings.Default.enable_user_work_shift_system == true)
            {
                BL.UserWorkShiftBL usr_wrk_sft_bl = new BL.UserWorkShiftBL();
                DataTable UserWorkShiftDT = new DataTable();
                UserWorkShiftDT = usr_wrk_sft_bl.CheckIfThereIsWorkShiftExistAndOpenedAndGetResult(Form1.CheckIns.UserName, MyDate);
                if (UserWorkShiftDT.Rows.Count > 0)
                {
                    if ((bool)UserWorkShiftDT.Rows[0]["shift_status"] == false)
                    {
                        return WorkShiftStatus.Opened;
                    }
                    else
                    {
                        return WorkShiftStatus.closed;
                    }
                }
                else
                {
                    return WorkShiftStatus.NotFound;
                }
            }
            else
            {
                return WorkShiftStatus.Opened;
            }
        }

        public static void ValidateNumberandFractions(object sender, KeyPressEventArgs e, string text, ToolStripStatusLabel lbl, Timer t)
        {
            if (char.IsDigit(e.KeyChar) || e.KeyChar == 8 || e.KeyChar == 46 || e.KeyChar == (char)Keys.Return)
            {
                e.Handled = false;
                if (text.Contains('.'))
                {
                    if (e.KeyChar == 46)
                    {
                        e.Handled = true;
                    }
                }
            }
            else
            {
                e.Handled = true;
                lbl.Text = "من فضلك ادخل ارقام فقط";
                t.Start();
            }
        }

        public static void ValidateNumber(object sender, KeyPressEventArgs e, ToolStripStatusLabel lbl, Timer t)
        {
            if (char.IsDigit(e.KeyChar) || e.KeyChar == 8 || e.KeyChar == (char)Keys.Return)
            {
                e.Handled = false;
            }
            else
            {
                e.Handled = true;
                lbl.Text = "من فضلك ادخل رقم صحيح";
                t.Start();
            }
        }

        public static string GetInvoiceTitleOnPrint(int InvKind)
        {
            string InvoiceTitle = string.Empty;
            switch (InvKind)
            {
                case 1:
                    InvoiceTitle = "فاتورة شراء";
                    break;
                case 2:
                    InvoiceTitle = "فاتورة مرتجع شراء";
                    break;
                case 3:
                    InvoiceTitle = Properties.Settings.Default.sale_invoice_title.ToString();
                    break;
                case 4:
                    InvoiceTitle = "فاتورة مرتجع بيع";
                    break;
                case 5:
                    InvoiceTitle = "عرض اسعار";
                    break;
                default:
                    break;
            }
            return InvoiceTitle;
        }

        public static string Tafkeet(decimal Amount)
        {
            string Tafkeet = string.Empty;
            string CurrencyID = Properties.Settings.Default.Application_currency.ToString();
            BL.CurrencyInfo.Currencies currencyEnum = (BL.CurrencyInfo.Currencies)int.Parse(CurrencyID);
            BL.CurrencyInfo currency = new BL.CurrencyInfo(currencyEnum);
            BL.ToWord toWord = new BL.ToWord(Convert.ToDecimal(Amount), currency);
            Tafkeet = Properties.Settings.Default.application_dispaly_language == "English" ? toWord.ConvertToEnglish() : toWord.ConvertToArabic();
            return Tafkeet;
        }

        public static DataTable GetBackGroundImage()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetData("GetBackGroundImage", null);
            }
        }

        public static string GetCashierInvoiceName(string UserName)
        {
            string CashierInvoiceName = string.Empty;
            BL.UserBL user_bl = new BL.UserBL();
            DataTable UserCashierInvoiceDT = user_bl.GetUserInvoiceCashierName(UserName);
            if (UserCashierInvoiceDT.Rows.Count > 0)
            {
                CashierInvoiceName = UserCashierInvoiceDT.Rows[0]["cashier_invoice_name"].ToString();
            }
            else
            {
                MessageBox.Show("لم يتم اختيار شاشة الكاشير الخاصة بالمستخدم");
            }
            return CashierInvoiceName;
        }


        public static void AcivateButtonsByUserPermissions(Form Frm)
        {
            if (Frm.HasChildren)
            {
                foreach (Control ctl in Frm.Controls)
                {
                    if (ctl.GetType() == typeof(ToolStrip))
                    {
                        ToolStrip tsCtl = (ToolStrip)ctl;
                        if (tsCtl.Name != "TSUserStatus")
                        {
                            foreach (ToolStripItem tsIctl in tsCtl.Items)
                            {
                                //if (tsCtl.Name != "toolStrip1")
                                //{
                                tsIctl.Visible = false;
                                //}
                                if (tsIctl is ToolStripDropDownButton)
                                {
                                    ToolStripDropDownButton TSDDB = (ToolStripDropDownButton)tsIctl;
                                    if (TSDDB.HasDropDown)
                                    {
                                        foreach (ToolStripDropDownItem ChTSDDB in TSDDB.DropDownItems)
                                        {

                                            ChTSDDB.Visible = false;


                                        }
                                    }

                                }
                            }
                        }

                    }
                    else if (ctl.GetType() == typeof(Panel))
                    {
                        foreach (Control chctl in ctl.Controls)
                        {
                            if (chctl.GetType() == typeof(Button))
                            {
                                ctl.Enabled = false;
                            }
                        }
                    }
                }
            }


            BL.UserBL userbl = new BL.UserBL();
            DataTable dt = userbl.GetUserPermissions(Form1.CheckIns.UserName);
            string CashierInvoiceName = GetCashierInvoiceName(Form1.CheckIns.UserName);
            if (dt.Rows.Count > 0)
            {
                foreach (DataRow row in dt.Rows)
                {
                    foreach (Control ctl in Frm.Controls)
                    {
                        if (ctl.GetType() == typeof(ToolStrip))
                        {
                            ToolStrip tsCtl = (ToolStrip)ctl;

                            foreach (ToolStripItem tsIctl in tsCtl.Items)
                            {

                                if (tsIctl.AccessibleName == row[0].ToString() || tsIctl.Name == "BtnClose"
                                                                                || tsIctl.AccessibleName == "خروج"
                                                                            || tsIctl.Text == "التقارير الاكثر استخداما")
                                {
                                    tsIctl.Visible = true;
                                }
                                if (tsIctl is ToolStripDropDownButton)
                                {
                                    ToolStripDropDownButton TSDDB = (ToolStripDropDownButton)tsIctl;
                                    if (TSDDB.HasDropDown)
                                    {
                                        foreach (ToolStripDropDownItem ChTSDDB in TSDDB.DropDownItems)
                                        {
                                            if (ChTSDDB.AccessibleName == row[0].ToString() && ChTSDDB.Tag.ToString() == CashierInvoiceName)
                                            {
                                                ChTSDDB.Visible = true;
                                                tsIctl.Visible = true;
                                            }
                                        }
                                    }

                                }
                            }
                        }
                        else if (ctl.GetType() == typeof(Panel))
                        {
                            foreach (Control chctl in ctl.Controls)
                            {
                                if (chctl.GetType() == typeof(Button))
                                {
                                    if (chctl.AccessibleName == row[0].ToString())
                                    {
                                        ctl.Enabled = true;
                                    }
                                }
                            }
                        }
                    }

                }
            }
        }

        public static void DisableOrEnableCostCenterByUserType(string UserName, ComboBox UserComboBox, ComboBox CostCenterComboBox, ComboBox TreasuryComboBox)
        {
            BL.UserBL user_bl = new UserBL();
            DataTable UserDetailDT = new DataTable();
            UserDetailDT = user_bl.GetUserDetailsByUserName(UserName);
            if (UserDetailDT.Rows[0]["user_job"].ToString() == "مدير البرنامج")
            {
                if (CostCenterComboBox != null)
                {
                    CostCenterComboBox.Enabled = true;
                }
                if (TreasuryComboBox != null)
                {
                    TreasuryComboBox.Enabled = true;
                }
                if (UserComboBox != null)
                {
                    UserComboBox.SelectedValue = UserName;
                    UserComboBox.Enabled = true;
                }
            }
            else if (UserDetailDT.Rows[0]["user_job"].ToString() == "كاشير")
            {
                if (CostCenterComboBox != null)
                {
                    CostCenterComboBox.SelectedValue = UserDetailDT.Rows[0]["cost_center_id"];
                    CostCenterComboBox.Enabled = false;
                }
                if (TreasuryComboBox != null)
                {
                    TreasuryComboBox.SelectedValue =(int) UserDetailDT.Rows[0]["treasury_id"];
                    TreasuryComboBox.Enabled = false;
                }
                if (UserComboBox != null)
                {
                    UserComboBox.SelectedValue = UserName;
                    UserComboBox.Enabled = false;
                }
            }
        }

        public static void OpenProductSearchForm(string Pro_Kind, object TxtProName, Form frm, string stock_id, int? InvCount)
        {
            if (PL.Search_Product_Form.CheckIns == null)
            {
                //PL.Search_Product_Form.CreateIns.MdiParent = this.ParentForm;
                //Point p = TxtProName.PointToScreen(Point.Empty);
                //PL.Search_Product_Form.CreateIns.SetDesktopLocation(p.X - PL.Search_Product_Form.CreateIns.Width + TxtProName.Width, p.Y + TxtProName.Height);
                //PL.Search_Product_Form.CreateIns.TxtProName.Text = TxtProName.Text;

                string StockID = string.IsNullOrEmpty(stock_id) ? "-1" : stock_id;
                PL.Search_Product_Form.CreateIns.CbStock.SelectedValue = StockID;
                PL.Search_Product_Form.CreateIns.InvCount = InvCount;
                PL.Search_Product_Form.CreateIns.SearchForProduct();
                PL.Search_Product_Form.CreateIns.TxtProName.Text += TxtProName == null ? null : TxtProName.ToString();
                PL.Search_Product_Form.CreateIns.TxtProName.Select(PL.Search_Product_Form.CreateIns.TxtProName.Text.Length, 0);
                PL.Search_Product_Form.CreateIns.TxtProName.Focus();
                PL.Search_Product_Form.CreateIns.Pro_Kind = Pro_Kind;
                PL.Search_Product_Form.CreateIns.ShowDialog(frm);

            }
            else
            {
                PL.Search_Product_Form.CreateIns.Focus();
            }
        }

        public static void OpenCustomerSearchForm(object TxtCustName, Form frm, int? WindowCount)
        {
            if (PL.Search_Product_Form.CheckIns == null)
            {
                PL.Customer_Search_Form.CreateIns.txtSearch.Text += TxtCustName == null ? null : TxtCustName.ToString();
                PL.Customer_Search_Form.CreateIns.txtSearch.Select(PL.Customer_Search_Form.CreateIns.txtSearch.Text.Length, 0);
                PL.Customer_Search_Form.CreateIns.txtSearch.Focus();
                PL.Customer_Search_Form.CreateIns.InvCount = WindowCount;
                PL.Customer_Search_Form.CreateIns.PayPerCount = WindowCount;
                PL.Customer_Search_Form.CreateIns.ShowDialog(frm);
            }
            else
            {
                PL.Customer_Search_Form.CreateIns.Focus();
            }
        }

        public static void OpenEmployeeSearchForm(object TxtCustName, Form frm)
        {
            if (PL.Search_Product_Form.CheckIns == null)
            {
                PL.Employee_Search_Form.CreateIns.txtSearch.Text += TxtCustName == null ? null : TxtCustName.ToString();
                PL.Employee_Search_Form.CreateIns.txtSearch.Select(PL.Employee_Search_Form.CreateIns.txtSearch.Text.Length, 0);
                PL.Employee_Search_Form.CreateIns.txtSearch.Focus();
                PL.Employee_Search_Form.CreateIns.ShowDialog(frm);
            }
            else
            {
                PL.Customer_Search_Form.CreateIns.Focus();
            }
        }

        //public void SetCrysRepDBCon(CrystalDecisions.CrystalReports.Engine.ReportDocument rpt)
        //{
        //    CrystalDecisions.Shared.TableLogOnInfo loginfo = new CrystalDecisions.Shared.TableLogOnInfo();
        //    foreach (CrystalDecisions.CrystalReports.Engine.Table t in rpt.Database.Tables)
        //    {
        //        loginfo = t.LogOnInfo;
        //        loginfo.ReportName = rpt.Name;
        //        loginfo.ConnectionInfo.ServerName = Properties.Settings.Default.server_name;
        //        loginfo.ConnectionInfo.DatabaseName = Properties.Settings.Default.database_name;
        //        if (Properties.Settings.Default.windows_auth == false)
        //        {
        //            loginfo.ConnectionInfo.IntegratedSecurity = false;
        //            loginfo.ConnectionInfo.UserID = Properties.Settings.Default.user_name;
        //            loginfo.ConnectionInfo.Password = Properties.Settings.Default.pass;
        //        }
        //        loginfo.TableName = t.Name;
        //        t.ApplyLogOnInfo(loginfo);
        //        t.Location = t.Name;
        //    }
        //}

        private static decimal GetItemQuantityInSmallUnit(string ProID, string UnitID, decimal Quantity)
        {
            decimal ItemQuantityInSmallUnit;
            decimal ItemUnitCount = 0;
            BL.Unit unit_bl = new BL.Unit();

            DataTable dt = unit_bl.GetProUnitDetailsByUnitIDAndProID(ProID, UnitID);
            if (dt.Rows.Count > 0)
            {
                ItemUnitCount = decimal.Parse(dt.Rows[0]["unit_count"].ToString());
            }
            ItemQuantityInSmallUnit = ItemUnitCount * Quantity;
            return ItemQuantityInSmallUnit;
        }

        public static bool CheckIfItemQuantityWillBeUnderZero(string StockID, string ProID, string UnitID, decimal Quantity)
        {
            bool UnderZero;
            // check if product is service
            BL.ProductBL pro_bl = new ProductBL();
            bool IsService = (bool)pro_bl.CheckIfItemIsService(ProID).Rows[0]["is_service"];

            //get current item balance
            decimal ItemCurrentBalance;
            BL.StockBL stck_bl = new BL.StockBL();
            string CurrentBalance = stck_bl.GetProductSmallBalanceByProIDAndStockID(ProID, StockID);
            if (!IsService && !string.IsNullOrEmpty(CurrentBalance))
            {
                ItemCurrentBalance = decimal.Parse(CurrentBalance);
            }
            else
            {
                ItemCurrentBalance = 0;
            }

            // get item quantity in small unit
            decimal ItemQuantity = GetItemQuantityInSmallUnit(ProID, UnitID, Quantity);

            // get future balance
            decimal ItemFuturedBalacne = ItemCurrentBalance - Quantity;

            if (!IsService && ItemFuturedBalacne < 0)
            {
                UnderZero = true;
            }
            else
            {
                UnderZero = false;
            }
            return UnderZero;
        }

        public static void AddUserLog(int ActionTypeID, int DocumentTypeID, string DocumentID)
        {
            BL.UserLogBL usr_log_bl = new BL.UserLogBL();
            usr_log_bl.UserName = Form1.CheckIns.UserName;
            usr_log_bl.ActionTypeID = ActionTypeID;
            usr_log_bl.DocumentTypeID = DocumentTypeID;
            usr_log_bl.DocumentID = DocumentID;
            usr_log_bl.ActionDate = DateTime.Now;
            usr_log_bl.AddUserLog(usr_log_bl);
        }

        public enum ActionType
        {
            Add = 1,
            Update = 2,
            Delete = 3,
            LogIn = 4,
            LogOut = 5
        }

        public enum DocumentType
        {
            PurchaseInvoice = 1,
            PurchaseReturnInvoice = 2,
            SaleInvoice = 3,
            SaleReturnInvoice = 4,
            StockImportPermission = 5,
            StockExportPermission = 6,
            StockRelocatePermission = 7,
            StockDamagedPermission = 8,
            CashDepositPermission = 9,
            CashPaymentPemission = 10,
            Expense = 11,
            Revenue = 12,
            RunWithIdenticalComponent = 13,
            RunWithAsymmetricComponent = 14,
            CheckIn = 15,
            CheckOut = 16,
            EmployeeActivity = 17,
            EmployeeVacation = 18,
            ChecksPaid = 19,
            ChecksReceived = 20,
            BankAction = 21,
            InstallmentPaidCOllected = 22,
            INstallmentPaidRefund = 23,
            UserLog = 24,
            UserLogin = 25,
            UserLogout = 26
        }
        public static string GetDocumentTypeByID(int DocID)
        {
            string DocumentType = string.Empty;
            switch (DocID)
            {
                case 1:
                    DocumentType = "فاتورة مشتريات";
                    break;
                case 2:
                    DocumentType = "فاتورة مرتجع مشتريات";
                    break;
                case 3:
                    DocumentType = "فاتورة مبيعات";
                    break;
                case 4:
                    DocumentType = "فاتورة مرتجع مبيعات";
                    break;
                case 5:
                    DocumentType = "اذن وارد بضاعة";
                    break;
                case 6:
                    DocumentType = "اذن صرف بضاعة";
                    break;
                case 7:
                    DocumentType = "اذن تحويل بضاعة";
                    break;
                case 8:
                    DocumentType = "اذن هالك بضاعة";
                    break;
                case 9:
                    DocumentType = "اذن قبض نقدية";
                    break;
                case 10:
                    DocumentType = "اذن دفع نقدية";
                    break;
                case 11:
                    DocumentType = "مصروف";
                    break;
                case 12:
                    DocumentType = "ايراد";
                    break;
                case 13:
                    DocumentType = "امر تشغيل بمكونات متماثلة";
                    break;
                case 14:
                    DocumentType = "امر تشغيل بمكونات غير متماثلة";
                    break;
                case 15:
                    DocumentType = "تسجيل حضور";
                    break;
                case 16:
                    DocumentType = "تسجيل انصراف";
                    break;
                case 17:
                    DocumentType = "نشاط موظف";
                    break;
                case 18:
                    DocumentType = "اجازة";
                    break;
                case 19:
                    DocumentType = "اوراق قبض";
                    break;
                case 20:
                    DocumentType = "اوراق دفع";
                    break;
                case 21:
                    DocumentType = "سحب / ايداع حساب بنكي";
                    break;
                case 22:
                    DocumentType = "سداد قسط";
                    break;
                case 23:
                    DocumentType = "ألغاء سداد قسط";
                    break;
                case 24:
                    DocumentType = "سجل المستخدمين";
                    break;
                case 25:
                    DocumentType = "تسجيل دخول";
                    break;
                case 26:
                    DocumentType = "تسجيل خروج";
                    break;
                default:
                    break;
            }
            return DocumentType;
        }

    }

}
