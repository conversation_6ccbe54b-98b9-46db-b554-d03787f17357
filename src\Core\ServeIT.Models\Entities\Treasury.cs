using System.ComponentModel.DataAnnotations;

namespace ServeIT.Models.Entities;

public class Treasury : BaseEntity<int>
{
    [Required]
    [StringLength(100)]
    public string Name { get; set; } = string.Empty;

    public int? CostCenterId { get; set; }

    public decimal InitialBalance { get; set; }

    [StringLength(200)]
    public string? Location { get; set; }

    // Navigation Properties
    public virtual CostCenter? CostCenter { get; set; }
    public virtual ICollection<Invoice> Invoices { get; set; } = new List<Invoice>();
    public virtual ICollection<TreasuryPermission> TreasuryPermissions { get; set; } = new List<TreasuryPermission>();
}
