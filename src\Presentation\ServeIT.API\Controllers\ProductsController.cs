using Microsoft.AspNetCore.Mvc;
using ServeIT.API.Services;
using ServeIT.Models.DTOs;
using ServeIT.Models.Common;

namespace ServeIT.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class ProductsController : ControllerBase
{
    private readonly IProductService _productService;

    public ProductsController(IProductService productService)
    {
        _productService = productService;
    }

    [HttpGet]
    public async Task<ActionResult<ServiceResult<IEnumerable<ProductDto>>>> GetAllProducts()
    {
        var result = await _productService.GetAllProductsAsync();
        return Ok(result);
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<ServiceResult<ProductDto>>> GetProduct(string id)
    {
        var result = await _productService.GetProductByIdAsync(id);
        
        if (!result.IsSuccess)
        {
            return NotFound(result);
        }

        return Ok(result);
    }

    [HttpGet("paged")]
    public async Task<ActionResult<ServiceResult<PagedResult<ProductDto>>>> GetProductsPaged([FromQuery] PagingParameters parameters)
    {
        var result = await _productService.GetProductsPagedAsync(parameters);
        return Ok(result);
    }

    [HttpPost]
    public async Task<ActionResult<ServiceResult<ProductDto>>> CreateProduct([FromBody] ProductDto productDto)
    {
        if (!ModelState.IsValid)
        {
            var errors = ModelState.Values
                .SelectMany(v => v.Errors)
                .Select(e => e.ErrorMessage)
                .ToList();
            
            return BadRequest(ServiceResult<ProductDto>.Failure("Validation failed", errors));
        }

        var result = await _productService.CreateProductAsync(productDto);
        
        if (!result.IsSuccess)
        {
            return BadRequest(result);
        }

        return CreatedAtAction(nameof(GetProduct), new { id = result.Data!.Id }, result);
    }

    [HttpPut("{id}")]
    public async Task<ActionResult<ServiceResult<ProductDto>>> UpdateProduct(string id, [FromBody] ProductDto productDto)
    {
        if (!ModelState.IsValid)
        {
            var errors = ModelState.Values
                .SelectMany(v => v.Errors)
                .Select(e => e.ErrorMessage)
                .ToList();
            
            return BadRequest(ServiceResult<ProductDto>.Failure("Validation failed", errors));
        }

        var result = await _productService.UpdateProductAsync(id, productDto);
        
        if (!result.IsSuccess)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    [HttpDelete("{id}")]
    public async Task<ActionResult<ServiceResult>> DeleteProduct(string id)
    {
        var result = await _productService.DeleteProductAsync(id);
        
        if (!result.IsSuccess)
        {
            return NotFound(result);
        }

        return Ok(result);
    }
}
