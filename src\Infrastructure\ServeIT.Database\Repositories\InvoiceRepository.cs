using Microsoft.EntityFrameworkCore;
using ServeIT.Database.Context;
using ServeIT.Database.Repositories.Interfaces;
using ServeIT.Models.Entities;
using ServeIT.Models.Common;
using ServeIT.Models.Enums;

namespace ServeIT.Database.Repositories;

public class InvoiceRepository : Repository<Invoice, string>, IInvoiceRepository
{
    public InvoiceRepository(ServeITDbContext context) : base(context)
    {
    }

    public async Task<IEnumerable<Invoice>> GetByCustomerAsync(int customerId)
    {
        return await _dbSet
            .Where(i => i.CustomerId == customerId && i.IsActive)
            .Include(i => i.Customer)
            .Include(i => i.SalesRepresentative)
            .Include(i => i.InvoiceDetails)
                .ThenInclude(id => id.Product)
            .OrderByDescending(i => i.InvoiceDate)
            .ToListAsync();
    }

    public async Task<IEnumerable<Invoice>> GetByDateRangeAsync(DateTime fromDate, DateTime toDate)
    {
        return await _dbSet
            .Where(i => i.IsActive && 
                i.InvoiceDate >= fromDate && 
                i.InvoiceDate <= toDate)
            .Include(i => i.Customer)
            .Include(i => i.SalesRepresentative)
            .Include(i => i.InvoiceDetails)
                .ThenInclude(id => id.Product)
            .OrderByDescending(i => i.InvoiceDate)
            .ToListAsync();
    }

    public async Task<IEnumerable<Invoice>> GetByKindAsync(InvoiceKind kind)
    {
        return await _dbSet
            .Where(i => i.IsActive && i.InvoiceKind == kind)
            .Include(i => i.Customer)
            .Include(i => i.SalesRepresentative)
            .Include(i => i.InvoiceDetails)
                .ThenInclude(id => id.Product)
            .OrderByDescending(i => i.InvoiceDate)
            .ToListAsync();
    }

    public async Task<PagedResult<Invoice>> SearchInvoicesAsync(string searchTerm, PagingParameters parameters)
    {
        var query = _dbSet
            .Include(i => i.Customer)
            .Include(i => i.SalesRepresentative)
            .Include(i => i.InvoiceDetails)
                .ThenInclude(id => id.Product)
            .Where(i => i.IsActive);

        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            searchTerm = searchTerm.ToLower();
            query = query.Where(i => 
                i.Id.ToLower().Contains(searchTerm) ||
                (i.Customer != null && i.Customer.Name.ToLower().Contains(searchTerm)) ||
                (i.SalesRepresentative != null && i.SalesRepresentative.Name.ToLower().Contains(searchTerm)) ||
                (i.Notes != null && i.Notes.ToLower().Contains(searchTerm)));
        }

        var totalCount = await query.CountAsync();

        var items = await query
            .OrderByDescending(i => i.InvoiceDate)
            .Skip((parameters.PageNumber - 1) * parameters.PageSize)
            .Take(parameters.PageSize)
            .ToListAsync();

        return new PagedResult<Invoice>(items, totalCount, parameters.PageNumber, parameters.PageSize);
    }

    public async Task<Invoice?> GetWithDetailsAsync(string invoiceId)
    {
        return await _dbSet
            .Include(i => i.Customer)
            .Include(i => i.SalesRepresentative)
            .Include(i => i.InvoiceDetails)
                .ThenInclude(id => id.Product)
                    .ThenInclude(p => p.Category)
            .Include(i => i.InvoiceDetails)
                .ThenInclude(id => id.Unit)
            .FirstOrDefaultAsync(i => i.Id == invoiceId && i.IsActive);
    }

    public async Task<IEnumerable<Invoice>> GetWithDetailsByDateRangeAsync(DateTime fromDate, DateTime toDate)
    {
        return await _dbSet
            .Where(i => i.IsActive && 
                i.InvoiceDate >= fromDate && 
                i.InvoiceDate <= toDate)
            .Include(i => i.Customer)
            .Include(i => i.SalesRepresentative)
            .Include(i => i.InvoiceDetails)
                .ThenInclude(id => id.Product)
                    .ThenInclude(p => p.Category)
            .Include(i => i.InvoiceDetails)
                .ThenInclude(id => id.Unit)
            .OrderByDescending(i => i.InvoiceDate)
            .ToListAsync();
    }

    public async Task<decimal> GetTotalSalesAsync(DateTime fromDate, DateTime toDate)
    {
        return await _dbSet
            .Where(i => i.IsActive && 
                i.InvoiceKind == InvoiceKind.Sale &&
                i.InvoiceDate >= fromDate && 
                i.InvoiceDate <= toDate)
            .SumAsync(i => i.TotalAmount);
    }

    public async Task<decimal> GetTotalPurchasesAsync(DateTime fromDate, DateTime toDate)
    {
        return await _dbSet
            .Where(i => i.IsActive && 
                i.InvoiceKind == InvoiceKind.Purchase &&
                i.InvoiceDate >= fromDate && 
                i.InvoiceDate <= toDate)
            .SumAsync(i => i.TotalAmount);
    }

    public async Task<int> GetInvoiceCountAsync()
    {
        return await _dbSet
            .Where(i => i.IsActive)
            .CountAsync();
    }

    public async Task<string> GetNextInvoiceNumberAsync(InvoiceKind kind)
    {
        var prefix = kind switch
        {
            InvoiceKind.Sale => "SAL",
            InvoiceKind.Purchase => "PUR",
            InvoiceKind.Return => "RET",
            InvoiceKind.Quote => "QUO",
            _ => "INV"
        };

        var today = DateTime.Today;
        var yearMonth = today.ToString("yyyyMM");
        
        var lastInvoice = await _dbSet
            .Where(i => i.InvoiceKind == kind && 
                i.Id.StartsWith($"{prefix}{yearMonth}"))
            .OrderByDescending(i => i.Id)
            .FirstOrDefaultAsync();

        if (lastInvoice == null)
        {
            return $"{prefix}{yearMonth}0001";
        }

        // Extract the sequence number from the last invoice
        var lastSequence = lastInvoice.Id.Substring(prefix.Length + yearMonth.Length);
        if (int.TryParse(lastSequence, out var sequenceNumber))
        {
            return $"{prefix}{yearMonth}{(sequenceNumber + 1):D4}";
        }

        return $"{prefix}{yearMonth}0001";
    }

    public override async Task<PagedResult<Invoice>> GetPagedAsync(PagingParameters parameters)
    {
        var query = _dbSet
            .Include(i => i.Customer)
            .Include(i => i.SalesRepresentative)
            .Include(i => i.InvoiceDetails)
                .ThenInclude(id => id.Product)
            .Where(i => i.IsActive);

        var totalCount = await query.CountAsync();

        var items = await query
            .OrderByDescending(i => i.InvoiceDate)
            .Skip((parameters.PageNumber - 1) * parameters.PageSize)
            .Take(parameters.PageSize)
            .ToListAsync();

        return new PagedResult<Invoice>(items, totalCount, parameters.PageNumber, parameters.PageSize);
    }

    public override async Task<Invoice?> GetByIdAsync(string id)
    {
        return await _dbSet
            .Include(i => i.Customer)
            .Include(i => i.SalesRepresentative)
            .Include(i => i.InvoiceDetails)
                .ThenInclude(id => id.Product)
            .FirstOrDefaultAsync(i => i.Id == id && i.IsActive);
    }

    public override async Task<IEnumerable<Invoice>> GetAllAsync()
    {
        return await _dbSet
            .Include(i => i.Customer)
            .Include(i => i.SalesRepresentative)
            .Include(i => i.InvoiceDetails)
                .ThenInclude(id => id.Product)
            .Where(i => i.IsActive)
            .OrderByDescending(i => i.InvoiceDate)
            .ToListAsync();
    }
}
