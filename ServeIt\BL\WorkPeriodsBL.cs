﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;

namespace ServeIt.BL
{
    class WorkPeriodsBL
    {
        public string WorkPeriodID { get; set; }
        public string WorkPeriodName { get; set; }
        public TimeSpan WorkStartTime { get; set; }
        public TimeSpan WorkEndTime { get; set; }
        public bool IsPeriodInSameDay { get; set; }
        public decimal LateRatioAllowance { get; set; }
        public decimal LateRatio { get; set; }
        public decimal AdditionRatioAllowance { get; set; }
        public decimal AdditionalRatio { get; set; }
        public bool Saturday { get; set; }
        public bool Sunday { get; set; }
        public bool Monday { get; set; }
        public bool Tuesday { get; set; }
        public bool Wednesday { get; set; }
        public bool Thursday { get; set; }
        public bool Friday { get; set; }
        public string Notes { get; set; }
        public string UserName { get; set; }


        public string AddOrUpdateWorkPeriod(WorkPeriodsBL wrk_prd_bl)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[18];
                para[0] = new SqlParameter("@check", "a");
                para[1] = new SqlParameter("@work_period_id", wrk_prd_bl.WorkPeriodID);
                para[2] = new SqlParameter("@work_period_name", wrk_prd_bl.WorkPeriodName);
                para[3] = new SqlParameter("@work_start_time", wrk_prd_bl.WorkStartTime);
                para[4] = new SqlParameter("@work_end_time", wrk_prd_bl.WorkEndTime);
                para[5] = new SqlParameter("@late_ratio_allowance", wrk_prd_bl.LateRatioAllowance);
                para[6] = new SqlParameter("@late_ratio", wrk_prd_bl.LateRatio);
                para[7] = new SqlParameter("@addition_ratio_allowance", wrk_prd_bl.AdditionRatioAllowance);
                para[8] = new SqlParameter("@addition_ratio", wrk_prd_bl.AdditionalRatio);
                para[9] = new SqlParameter("@sat", wrk_prd_bl.Saturday);
                para[10] = new SqlParameter("@sun", wrk_prd_bl.Sunday);
                para[11] = new SqlParameter("@mon", wrk_prd_bl.Monday);
                para[12] = new SqlParameter("@tue", wrk_prd_bl.Tuesday);
                para[13] = new SqlParameter("@wed", wrk_prd_bl.Wednesday);
                para[14] = new SqlParameter("@thu", wrk_prd_bl.Thursday);
                para[15] = new SqlParameter("@fri", wrk_prd_bl.Friday);
                para[16] = new SqlParameter("@notes", wrk_prd_bl.Notes);
                para[17] = new SqlParameter("@user_name", wrk_prd_bl.UserName);
                return DAL.InsUpdDel("ManageWorkPeriod", para);
            }
        }

        public string DeleteWorkPeriod(string WorkPeriodID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "d");
                para[1] = new SqlParameter("@work_period_id", WorkPeriodID);
                return DAL.InsUpdDel("ManageWorkPeriod", para);
            }
        }

        public string GetMaxWorkPeriodID()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@check", "m");
                return DAL.GetValue("ManageWorkPeriod", para);
            }
        }

        public DataTable GetWorkPeriodByID(string WorkPeriodID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "g");
                para[1] = new SqlParameter("@work_period_id", WorkPeriodID);
                return DAL.GetData("ManageWorkPeriod", para);
            }
        }

        public DataTable SearchWorkPeriod(string WorkPeriodName)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "s");
                para[1] = new SqlParameter("@work_period_name", WorkPeriodName);
                return DAL.GetData("ManageWorkPeriod", para);
            }
        }
    }
}
