﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;

namespace ServeIt.BL
{
    class TableBL
    {
        public string HallTableID { get; set; }
        public string HallID { get; set; }
        public string TableName { get; set; }
        public string Notes { get; set; }

        public string AddOrUpdateTable(TableBL table_bl)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[5];
                para[0] = new SqlParameter("@check", "a");
                para[1] = new SqlParameter("@hall_table_id", table_bl.HallTableID);
                para[2] = new SqlParameter("@hall_id", table_bl.HallID);
                para[3] = new SqlParameter("@table_name", table_bl.TableName);
                para[4] = new SqlParameter("@notes", table_bl.Notes);
                return DAL.InsUpdDel("ManageTable", para);
            }
        }

        public string GetMaxTableID()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@check", "m");
                return DAL.GetValue("ManageTable", para);
            }
        }

        public DataTable GetTableByID(string HallTableID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "g");
                para[1] = new SqlParameter("@hall_table_id", HallTableID);
                return DAL.GetData("ManageTable", para);
            }
        }

        public DataTable GetTableNameByTableID(string TableID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@table_id", TableID);
                return DAL.GetData("GetTableNameByTableID", para);
            }
        }

        public string DeleteTable(string TableID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "d");
                para[1] = new SqlParameter("@hall_table_id", TableID);
                return DAL.InsUpdDel("ManageTable", para);
            }
        }

        public DataTable SearchTable(string TableID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "s");
                para[1] = new SqlParameter("@hall_id", TableID);
                return DAL.GetData("ManageTable", para);
            }
        }

        public DataTable GetPreviousTable(string TableID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "p");
                para[1] = new SqlParameter("@hall_table_id", TableID);
                return DAL.GetData("ManageTable", para);
            }
        }

        public DataTable GetNextTable(string TableID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "n");
                para[1] = new SqlParameter("@hall_table_id", TableID);
                return DAL.GetData("ManageTable", para);
            }
        }

        public DataTable GetTablesDetailsByHallID(string HallID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@hall_id", HallID);
                return DAL.GetData("GetTablesDetailsByHallID", para);
            }
        }
    }
}
