﻿using System;
using System.Collections.Generic;
using System.Data;
using Microsoft.SqlServer.Management.Smo;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Reflection;
using Microsoft.SqlServer.Management.Common;
using System.Text.RegularExpressions;
using System.Transactions;

namespace ServeIt.BL
{
    class DatabaseManagementBL
    {
        void ExecSql(string sql, string connectionString, string dataBaseNameToPrepend)
        {
            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                conn.Open();
                Server server = new Server(new ServerConnection(conn));

                server.ConnectionContext.ExecuteNonQuery(sql);
                server.ConnectionContext.Disconnect();
                MessageBox.Show("تم انشاء قاعدة بيانات جديدة بنجاح");
            }
        }

        public bool CreateNewDatabase(string databaseName)
        {
            bool result = false;
            using (DA.DataAccess DAL = new DA.DataAccess())
            {

                SqlConnection connection = new SqlConnection();
                try
                {
                    //connection.ConnectionString = DAL.MyConnectionString().ConnectionString;
                    //connection.Open();
                    result = runSqlScriptFile(databaseName, DAL.MyConnectionString().ConnectionString);
                    //string script = Properties.Resources.Database_Script.ToString();

                    //script = script.Replace("$(DBNAME)", databaseName);
                    //ExecSql(script, connection.ConnectionString, databaseName);

                }
                catch (System.Exception ex)
                {
                    MessageBox.Show(ex.ToString(), "ServIT", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                finally
                {
                    if (connection.State == ConnectionState.Open)
                    {
                        connection.Close();
                    }
                }
            }
            return result;
        }
        private bool runSqlScriptFile(string databaseName, string connectionString)
        {

            try
            {
                string script = Properties.Resources.Database_Script.ToString();

                script = script.Replace("$(DBNAME)", databaseName);

                // split script on GO command
                System.Collections.Generic.IEnumerable<string> commandStrings = Regex.Split(script, @"^\s*GO\s*$",
                                         RegexOptions.Multiline | RegexOptions.IgnoreCase);
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    foreach (string commandString in commandStrings)
                    {
                        if (commandString.Trim() != "")
                        {
                            using (var command = new SqlCommand(commandString, connection))
                            {
                                try
                                {
                                    command.ExecuteNonQuery();
                                }
                                catch (SqlException ex)
                                {
                                    string spError = commandString.Length > 100 ? commandString.Substring(0, 100) + " ...\n..." : commandString;
                                    MessageBox.Show(string.Format("Please check the SqlServer script.\nFile: {0} \nLine: {1} \nError: {2} \nSQL Command: \n{3}", ex.LineNumber, ex.Message, spError), "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                                    return false;
                                }
                            }
                        }
                    }
                }
                return true;
            }
            catch (TransactionException ex)
            {
                MessageBox.Show(ex.Message, "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

        }
    }
}
