﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ServeIt.BL
{
    class InvoiceBL
    {

        public string GetInvoiceCount()
        {
            string result = string.Empty;
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                using (SqlConnection con = new SqlConnection())
                {
                    con.ConnectionString = DAL.MyConnectionString().ConnectionString;
                    if (con.State != ConnectionState.Open)
                    {
                        con.Open();
                    }
                    using (SqlCommand cmd = new SqlCommand())
                    {
                        cmd.Connection = con;
                        cmd.CommandText = "select count(inv_id) from invoice_header";
                        
                        try
                        {
                            result = cmd.ExecuteScalar().ToString();
                        }
                        catch (SqlException ex)
                        {

                            return ex.Message;
                        }
                    }
                }
            }
            return result;
        }

        public DataTable GetInvoicesByType(BE.InvoiceBE invbe)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@inv_kind", invbe.Kind);
                return DAL.GetData("getallinvoicebytype", para);
            }
        }

        public string GetMaxInvoiceId()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetValue("getmaxinvoiceid");
            }
        }

        public DataTable GetPreviousInvID(string InvID, string InvKind, string UserName)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[3];
                para[0] = new SqlParameter("@inv_id", InvID);
                para[1] = new SqlParameter("@inv_kind_id", InvKind);
                para[2] = new SqlParameter("@user_name", UserName);
                return DAL.GetData("GetPreviousInvID",para);
            }
        }

        public DataTable GetNextInvID(string InvID, string InvKind, string UserName)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[3];
                para[0] = new SqlParameter("@inv_id", InvID);
                para[1] = new SqlParameter("@inv_kind_id", InvKind);
                para[2] = new SqlParameter("@user_name", UserName);
                return DAL.GetData("GetNextInvID", para);
            }
        }

        public DataTable GetAllPaymentKind() 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetData("getallinvpaymentkind", null);
            }
        }


        public DataTable SearchInvoice(BE.InvoiceBE invbe)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[9];
                para[0] = new SqlParameter("@kind", invbe.Kind);
                para[1] = new SqlParameter("@cost_center_id", invbe.CostCenterID);
                para[2] = new SqlParameter("@user_name", invbe.User_Name);
                para[3] = new SqlParameter("@datefrom", invbe.DateFrom);
                para[4] = new SqlParameter("@dateto", invbe.DateTo);
                para[5] = new SqlParameter("@id", invbe.ID);
                para[6] = new SqlParameter("@no", invbe.No);
                para[7] = new SqlParameter("@cust_id", invbe.Cust_Id);
                para[8] = new SqlParameter("@treasury_id", invbe.Treasury_Id);
                
                return DAL.GetData("searchinvoices", para);
            }
        }


        public DataTable GetInvoiceHeaderByInvId(string InvID) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@inv_id", InvID);
                return DAL.GetData("GetInvoiceHeaderByInvID", para);
            }
        }


        public string AddOrUpdateInvoice(BE.InvoiceBE invbe, string procedure)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[43];
                para[0] = new SqlParameter("@check", 'a');
                para[1] = new SqlParameter("@inv_id", invbe.ID);
                para[2] = new SqlParameter("@inv_no", invbe.No);
                para[3] = new SqlParameter("@reference_sale_inv_id", invbe.ReferenceSaleInvID);
                para[4] = new SqlParameter("@cost_center_id", invbe.CostCenterID);
                para[5] = new SqlParameter("@inv_date", invbe.Date);
                para[6] = new SqlParameter("@tran_date", invbe.TranDate);
                para[7] = new SqlParameter("@stock_id", invbe.Stock_Id);
                para[8] = new SqlParameter("@cust_id", invbe.Cust_Id);
                para[9] = new SqlParameter("@inv_kind_id", invbe.Kind);
                para[10] = new SqlParameter("@payment_id", invbe.Payment_Kind);
                para[11] = new SqlParameter("@order_type", invbe.OrderType);
                para[12] = new SqlParameter("@order_status", invbe.OrderStatus);
                para[13] = new SqlParameter("@delivery_id", invbe.DeliveryID);
                para[14] = new SqlParameter("@table_id", invbe.TableID);
                para[15] = new SqlParameter("@table_members_count", invbe.TableMembersCount);
                para[16] = new SqlParameter("@treasury_id", invbe.Treasury_Id);
                para[17] = new SqlParameter("@sale_rep_id", invbe.Sale_Rep_Id);
                para[18] = new SqlParameter("@paid", invbe.Paid);
                para[19] = new SqlParameter("@cust_resceipient", invbe.CustomerReceipient);
                para[20] = new SqlParameter("@installment_count", invbe.Installment_Count);
                para[21] = new SqlParameter("@installment_value",invbe.Installment_Paid_Value);
                para[22] = new SqlParameter("@installment_method", invbe.Installment_Method);
                para[23] = new SqlParameter("@discount_kind", invbe.Discount_Kind);
                para[24] = new SqlParameter("@discount", invbe.Discount);
                para[25] = new SqlParameter("@discount2", invbe.Discount2);
                para[26] = new SqlParameter("@addition", invbe.Addition);
                para[27] = new SqlParameter("@sales_tax", invbe.Sales_Tax);
                para[28] = new SqlParameter("@service", invbe.Service);
                para[29] = new SqlParameter("@notes", invbe.Notes);
                para[30] = new SqlParameter("@inv_det_id", invbe.Inv_Det_Id);
                para[31] = new SqlParameter("@pro_id", invbe.Pro_Id);
                para[32] = new SqlParameter("@pro_serial", invbe.ProSerial);
                para[33] = new SqlParameter("@pro_expirey", invbe.ProExpirey);
                para[34] = new SqlParameter("@unit_id", invbe.Unit_Id);
                para[35] = new SqlParameter("@quantity", invbe.Quantity);
                para[36] = new SqlParameter("@price", invbe.Price);
                para[37] = new SqlParameter("@cost_price", invbe.CostPrice);
                para[38] = new SqlParameter("@quantity_out", invbe.QuantityOut);
                para[39] = new SqlParameter("@pro_discount", invbe.Pro_Discount);
                para[40] = new SqlParameter("@VAT", invbe.VAT);
                para[41] = new SqlParameter("@pro_notes", invbe.Pro_notes);
                para[42] = new SqlParameter("@user_name", invbe.User_Name);
                return DAL.InsUpdDel(procedure, para);
            }
        }

        public string AddNewProductToInvoice(BE.InvoiceBE invbe)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[13];
                para[0] = new SqlParameter("@inv_id", invbe.ID);
            
                para[1] = new SqlParameter("@inv_det_id", invbe.Inv_Det_Id);
                para[2] = new SqlParameter("@pro_id", invbe.Pro_Id);
                para[3] = new SqlParameter("@pro_serial", invbe.ProSerial);
                para[4] = new SqlParameter("@pro_expirey", invbe.ProExpirey);
                para[5] = new SqlParameter("@unit_id", invbe.Unit_Id);
                para[6] = new SqlParameter("@quantity", invbe.Quantity);
                para[7] = new SqlParameter("@price", invbe.Price);
                para[8] = new SqlParameter("@cost_price", invbe.CostPrice);
                para[9] = new SqlParameter("@quantity_out", invbe.QuantityOut);
                para[10] = new SqlParameter("@pro_discount", invbe.Pro_Discount);
                para[11] = new SqlParameter("@VAT", invbe.VAT);
                para[12] = new SqlParameter("@pro_notes", invbe.Pro_notes);
                return DAL.InsUpdDel("AddNewProductToInvoice", para);
            }
        }

        public string UpdateRestaurantOrderDeliveryData(string InvID, string DeliveryID, DateTime? DeliveryStartTime, DateTime? DeliveryEndTime)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[4];

                para[0] = new SqlParameter("@inv_id", InvID);
                para[1] = new SqlParameter("@delivery_id", DeliveryID);
                para[2] = new SqlParameter("@delivery_start_time", DeliveryStartTime);
                para[3] = new SqlParameter("@delivery_end_time", DeliveryEndTime);
              
                return DAL.InsUpdDel("UpdateRestaurantOrderDeliveryData", para);
            }
        }

        public string UpdateSaleInvoiceReferenceInvID(string InvID, string ReferenceInvID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@inv_id", InvID);
                para[1] = new SqlParameter("@reference_inv_id", ReferenceInvID);
                return DAL.InsUpdDel("UpdateSaleInvoiceReferenceInvID",para);
            }
        }

        public string UpdateInvoicePaymentDetailsAndStatus(string InvID,
                                                            string OrderStatus,
                                                            string PaymentKind,
                                                            decimal Paid,
                                                            decimal CustomerReceipient,
                                                            decimal DiscountKind,
                                                            decimal Discount,
                                                            decimal Discount2,
                                                            decimal Addition,
                                                            decimal SalesTax,
                                                            decimal Service)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[11];
                para[0] = new SqlParameter("@inv_id", InvID);
                para[1] = new SqlParameter("@order_status", OrderStatus);
                para[2] = new SqlParameter("@payment_kind", PaymentKind);
                para[3] = new SqlParameter("@paid", Paid);
                para[4] = new SqlParameter("@cust_resceipient", CustomerReceipient);
                para[5] = new SqlParameter("@discount_kind", DiscountKind);
                para[6] = new SqlParameter("@discount", Discount);
                para[7] = new SqlParameter("@discount2", Discount2);
                para[8] = new SqlParameter("@addition", Addition);
                para[9] = new SqlParameter("@sales_tax", SalesTax);
                para[10] = new SqlParameter("@service", Service);

                return DAL.InsUpdDel("UpdateInvoicePaymentDetailsAndStatus", para);
            }
        }

        public string UnlockOrLockRestaurantOrder(string InvID, string OrderStatus)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@inv_id", InvID);
                para[1] = new SqlParameter("@order_status", OrderStatus);
                return DAL.InsUpdDel("UnlockOrLockRestaurantOrder", para);
            }
        }

        //public string AddOrUpdateInvoice(BE.InvoiceBE invbe)
        //{
        //    using (DA.DataAccess DAL = new DA.DataAccess())
        //    {
        //        SqlConnection con = new SqlConnection();
        //        con.ConnectionString = DAL.MyConnectionString(Properties.Settings.Default["server_name"].ToString(),
        //                                                  Properties.Settings.Default["database_name"].ToString(),
        //                                                  (bool)Properties.Settings.Default["windows_auth"],
        //                                                  Properties.Settings.Default["user_name"].ToString(),
        //                                                  Properties.Settings.Default["pass"].ToString()).ConnectionString;
        //        SqlCommand InvoiceHeaderCMD = new SqlCommand();
        //        InvoiceHeaderCMD.Connection = con;
        //        InvoiceHeaderCMD.CommandType = CommandType.StoredProcedure;
        //        InvoiceHeaderCMD.CommandText = "ManageInvoiceHeader";

        //    }
        //}

        public string DeleteInvoiceDetailRow(BE.InvoiceBE invbe)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@inv_id", invbe.ID);
                para[1] = new SqlParameter("@inv_det_id", invbe.Inv_Det_Id);
                return DAL.InsUpdDel("deleteinvoicerow", para);
            }
        }


        public string DeleteInvoice(string InvID, string procedure)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "r");
                para[1] = new SqlParameter("@inv_id", InvID);
                return DAL.InsUpdDel(procedure, para);
            }
        }

        public DataTable GetInvoiceIDByTableID(string TableID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@table_id", TableID);
              
                return DAL.GetData("GetInvoiceIDByTableID", para);
            }
        }

        public string GetTotalOfInvoice(BE.InvoiceBE invbe)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@inv_id", invbe.ID);
                return DAL.GetValue("gettotalofinvoice", para);
            }
        }

        public DataTable GetProPrice(string pro_id,int unit_id,string pricesys) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[3];
                para[0] = new SqlParameter("@pro_id", pro_id);
                para[1] = new SqlParameter("@unit_id", unit_id);
                para[2] = new SqlParameter("@pricesys", pricesys);
                return DAL.GetData("GetProPrice", para);
            }
        }

        public string ChangeInvoiceQuantityOut(string InvID, string ProID, decimal QuantityOut)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[3];
                para[0] = new SqlParameter("@inv_id",InvID);
                para[1] = new SqlParameter("@pro_id",ProID);
                para[2] = new SqlParameter("@quantity_out",QuantityOut);
                return DAL.InsUpdDel("ChangeInvoiceQuantityOut", para);
            }
        }


        public DataTable GetDefaultStock()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetData("getdefaultstock", null);
            }
        }


        public DataTable GetInvoiceDetailByInvID(string InvID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@inv_id", InvID);
                return DAL.GetData("GetInvoiceDetailByInvID", para);
            }
        }

        public DataTable GetProductCost(string ProID, DateTime ExportDate)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@pro_id", ProID);
                para[1] = new SqlParameter("@export_date", ExportDate);
                return DAL.GetData("GetProductCost", para);
            }
        }

        public DataTable GetInvoiceStockAndDetailByInvID(string InvID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@inv_id", InvID);
                return DAL.GetData("GetInvoiceStockAndDetailByInvID", para);
            }
        }

        public DataTable SearchProductInInvocie(BE.StockBE stockbe) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[5];
                para[0] = new SqlParameter("@pro_name", stockbe.ProName);
                para[1] = new SqlParameter("@pro_id", stockbe.ProID);
                para[2] = new SqlParameter("@pro_id2", stockbe.ProID2);
                para[3] = new SqlParameter("@cat_id", stockbe.Cat_Id);
                para[4] = new SqlParameter("@stock_id", stockbe.StockId);
                return DAL.GetData("SearchProductInInvocie", para);
            }
        }

        public DataTable SearchProductsInSaleInvoiceDetails(string InvID,string ProName)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@inv_id", InvID);
                para[1] = new SqlParameter("@pro_name", ProName);
               
                return DAL.GetData("SearchProductsInInvoiceDetails", para);
            }
        }

        public DataTable GetInvoiceKinds() 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetData("GetInvoiceKinds", null);
            }
        }

        public DataTable RPTInvoiceMovement(BE.InvoiceBE invbe) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[8];
                para[0] = new SqlParameter("@kind", invbe.Kind);
                para[1] = new SqlParameter("@datefrom", invbe.DateFrom);
                para[2] = new SqlParameter("@dateto", invbe.DateTo);
                para[3] = new SqlParameter("@id", invbe.ID);
                para[4] = new SqlParameter("@no", invbe.No);
                para[5] = new SqlParameter("@cust_id", invbe.Cust_Id);
                para[6] = new SqlParameter("@pay_Kind", invbe.Payment_Kind);
                para[7] = new SqlParameter("@treasury_id", invbe.Treasury_Id);
                return DAL.GetData("RPTInvoiceMovement", para);
            }
        }

        public DataTable RPTInvoiceDetailsMovement(BE.InvoiceBE invbe)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[8];
                para[0] = new SqlParameter("@kind", invbe.Kind);
                para[1] = new SqlParameter("@datefrom", invbe.DateFrom);
                para[2] = new SqlParameter("@dateto", invbe.DateTo);
                para[3] = new SqlParameter("@id", invbe.ID);
                para[4] = new SqlParameter("@no", invbe.No);
                para[5] = new SqlParameter("@cust_id", invbe.Cust_Id);
                para[6] = new SqlParameter("@pay_Kind", invbe.Payment_Kind);
                para[7] = new SqlParameter("@treasury_id", invbe.Treasury_Id);
                return DAL.GetData("RPTInvoiceMovement", para);
            }
        }

        public DataTable RPTPurchaseInvoiceMovement(BE.InvoiceBE invbe)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[9];
                para[0] = new SqlParameter("@kind", invbe.Kind);
                para[1] = new SqlParameter("@datefrom", invbe.DateFrom);
                para[2] = new SqlParameter("@dateto", invbe.DateTo);
                para[3] = new SqlParameter("@id", invbe.ID);
                para[4] = new SqlParameter("@no", invbe.No);
                para[5] = new SqlParameter("@cust_id", invbe.Cust_Id);
                para[6] = new SqlParameter("@sale_rep_id", invbe.Sale_Rep_Id);
                para[7] = new SqlParameter("@pay_Kind", invbe.Payment_Kind);
                para[8] = new SqlParameter("@stock_id", invbe.Stock_Id);
                return DAL.GetData("RPTPurchaseInvoiceMovement", para);
            }
        }

        public DataTable RPTSalesQuantityOfProduct(BE.InvoiceBE invbe) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[6];
                para[0] = new SqlParameter("@cat_id", invbe.Cat_Id);
                para[1] = new SqlParameter("@pro_id", invbe.Pro_Id);
                para[2] = new SqlParameter("@cust_id", invbe.Cust_Id);
                para[3] = new SqlParameter("@sale_rep_id", invbe.Sale_Rep_Id);
                para[4] = new SqlParameter("@datefrom", invbe.DateFrom);
                para[5] = new SqlParameter("@dateto", invbe.DateTo);
                return DAL.GetData("RPTSalesQuantityOfProduct", para);
            }
        }

        public DataTable RPTSalesRepSalesOfCat(string SalesRepID, string CatID,string StockID,string CustID, DateTime StartDate, DateTime EndDate)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[6];
                para[0] = new SqlParameter("@sale_rep_id", SalesRepID);
                para[1] = new SqlParameter("@cat_id", CatID);
                para[2] = new SqlParameter("@stock_id", CatID);
                para[3] = new SqlParameter("@cust_id", SalesRepID);
                para[4] = new SqlParameter("@datefrom", StartDate);
                para[5] = new SqlParameter("@dateto", EndDate);
                return DAL.GetData("RPTSalesRepSalesOfCat", para);
            }
        }
        

        public DataTable RPTItemsSummaryResultPerStock(BE.InvoiceBE invbe)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[4];
                para[0] = new SqlParameter("@StockID", invbe.Stock_Id);
                para[1] = new SqlParameter("@CategoryID", invbe.Cat_Id);
                para[2] = new SqlParameter("@ProID", invbe.Pro_Id);
                para[3] = new SqlParameter("@ToDate", invbe.DateTo);
               
                return DAL.GetData("RPTItemsSummaryResultPerStock", para);
            }
        }
        public DataTable RPTSalesOfProduct(BE.InvoiceBE invbe)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[8];
                para[0] = new SqlParameter("@cat_id", invbe.Cat_Id);
                para[1] = new SqlParameter("@pro_id", invbe.Pro_Id);
                para[2] = new SqlParameter("@cust_id", invbe.Cust_Id);
                para[3] = new SqlParameter("@sale_rep_id", invbe.Sale_Rep_Id);
                para[4] = new SqlParameter("@treas_id", invbe.Treasury_Id);
                para[5] = new SqlParameter("@datefrom", invbe.DateFrom);
                para[6] = new SqlParameter("@dateto", invbe.DateTo);
                para[7] = new SqlParameter("@user_name", invbe.User_Name);
                return DAL.GetData("RPTSalesOfProduct", para);
            }
        }

        public DataTable RPTSalesTotals(string UserName, string TreasuryID, DateTime StartDate, DateTime EndDate)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[4];
                para[0] = new SqlParameter("@user_name", UserName);
                para[1] = new SqlParameter("@treasury_id", TreasuryID);
                para[2] = new SqlParameter("@start_date", StartDate);
                para[3] = new SqlParameter("@end_date", EndDate);
                return DAL.GetData("RptSalesTotals", para);
            }
        }

        public DataTable RPTSalesOfSalesRepresentativeItemsQuantity(BE.InvoiceBE invbe)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[4];
                para[0] = new SqlParameter("@stock_id", invbe.Stock_Id);
                para[1] = new SqlParameter("@sale_rep_id", invbe.Sale_Rep_Id);
                para[2] = new SqlParameter("@datefrom", invbe.DateFrom);
                para[3] = new SqlParameter("@dateto", invbe.DateTo);
                return DAL.GetData("RPTSalesOfSalesRepresentative", para);
            }
        }

        public DataTable RPTPurchaseQuantityOfProduct(BE.InvoiceBE invbe)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[6];
                para[0] = new SqlParameter("@cat_id", invbe.Cat_Id);
                para[1] = new SqlParameter("@pro_id", invbe.Pro_Id);
                para[2] = new SqlParameter("@cust_id", invbe.Cust_Id);
                para[3] = new SqlParameter("@sale_rep_id", invbe.Sale_Rep_Id);
                para[4] = new SqlParameter("@datefrom", invbe.DateFrom);
                para[5] = new SqlParameter("@dateto", invbe.DateTo);
                return DAL.GetData("RPTPurchaseQuantityOfProduct", para);
            }
        }

        public DataTable RPTPurchaseAndSaleQuantityOfProduct(BE.InvoiceBE invbe)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[6];
                para[0] = new SqlParameter("@cat_id", invbe.Cat_Id);
                para[1] = new SqlParameter("@pro_id", invbe.Pro_Id);
                para[2] = new SqlParameter("@cust_id", invbe.Cust_Id);
                para[3] = new SqlParameter("@sale_rep_id", invbe.Sale_Rep_Id);
                para[4] = new SqlParameter("@datefrom", invbe.DateFrom);
                para[5] = new SqlParameter("@dateto", invbe.DateTo);
                return DAL.GetData("RPTPurchaseAndSaleQuantityOfProduct", para);
            }
        }


        public DataTable RPTInvoiceProfit(BE.InvoiceBE invbe) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[6];
                para[0] = new SqlParameter("@inv_id", invbe.ID);
                para[1] = new SqlParameter("@inv_no", invbe.No);
                para[2] = new SqlParameter("@cust_id", invbe.Cust_Id);
                para[3] = new SqlParameter("@price_type", invbe.Invoice_Costing_System);
                para[4] = new SqlParameter("@date_from", invbe.DateFrom);
                para[5] = new SqlParameter("@date_to", invbe.DateTo);

                return DAL.GetData("RPTInvoiceProfit", para);
            }
        }


        public DataTable RPTInvoiceProfitFIFO(BE.InvoiceBE invbe)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[5];
                para[0] = new SqlParameter("@inv_id", invbe.ID);
                para[1] = new SqlParameter("@inv_no", invbe.No);
                para[2] = new SqlParameter("@cust_id", invbe.Cust_Id);
                para[3] = new SqlParameter("@date_from", invbe.DateFrom);
                para[4] = new SqlParameter("@date_to", invbe.DateTo);

                return DAL.GetData("RPTInvoiceProfitFIFO", para);
            }
        }

        public DataTable RPTGetRecessionItems(BE.InvoiceBE invbe)
        {
            using (DA.DataAccess DA = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[4];
                para[0] = new SqlParameter("@recession_rate", invbe.TurnOverRate);
                para[1] = new SqlParameter("@datefrom", invbe.DateFrom);
                para[2] = new SqlParameter("@dateto", invbe.DateTo);
                para[3] = new SqlParameter("@stock_id", invbe.Stock_Id);
                return DA.GetData("RPTGetRecessionItems", para);
            }
        }

        public DataTable RPTGetMostSellingItems(BE.InvoiceBE invbe) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@datefrom", invbe.DateFrom);
                para[1] = new SqlParameter("@dateto", invbe.DateTo);
                return DAL.GetData("RPTGetMostSellingItems", para);
            }
        }

        public DataTable RPTGetLeastSellingItems(BE.InvoiceBE invbe)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@datefrom", invbe.DateFrom);
                para[1] = new SqlParameter("@dateto", invbe.DateTo);
                return DAL.GetData("RPTGetLeastSellingItems", para);
            }
        }

        public string ConvertFromPriceListToSaleInvoice(int inv_id) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@inv_id", inv_id);
                return DAL.InsUpdDel("ConvertFromPriceListToSaleInvoice", para);
            }
        }

        public DataTable GetInvoiceToPrint(string InvID, string Inv_Print_Footer_Txt, bool PrintPreviousBalance) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[3];
                para[0] = new SqlParameter("@inv_id", InvID);
                para[1] = new SqlParameter("@inv_print_footer_txt", Inv_Print_Footer_Txt);
                para[2] = new SqlParameter("@print_previos_balance", PrintPreviousBalance);
                return DAL.GetData("RPTInvoicePrint", para);
            }
        }

        public DataTable SearchRestaurantOrders( string InvKind,string UserName,string DeliveryID, string InvID,string Paid, String OrderStatus, string OrderType,DateTime DateFrom,DateTime DateTo)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[9];
                para[0] = new SqlParameter("@inv_kind",InvKind);
                para[1] = new SqlParameter("@user_name", UserName);
                para[2] = new SqlParameter("@delivery_id", DeliveryID); 
                para[3] = new SqlParameter("@inv_id", InvID);
                para[4] = new SqlParameter("@paid",Paid);
                para[5] = new SqlParameter("@order_status",OrderStatus);
                para[6] = new SqlParameter("@order_type",OrderType);
                para[7] = new SqlParameter("@date_from",DateFrom);
                para[8] = new SqlParameter("@date_to", DateTo);
                return DAL.GetData("SearchRestaurantOrders", para);
            }
        }

        public string MoveOrderToAnotherTable(string InvID, string NewTable)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@inv_id", InvID);
                para[1] = new SqlParameter("@table_id", NewTable);

                return DAL.InsUpdDel("MoveOrderToAnotherTable", para);
            }
        }
    }
}
