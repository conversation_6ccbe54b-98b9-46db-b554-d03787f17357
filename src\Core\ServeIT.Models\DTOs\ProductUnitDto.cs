using System.ComponentModel.DataAnnotations;

namespace ServeIT.Models.DTOs;

public class ProductUnitDto
{
    public int Id { get; set; }

    [Required]
    public string ProductId { get; set; } = string.Empty;

    public int UnitId { get; set; }
    public string? UnitName { get; set; }

    [Range(0.01, double.MaxValue, ErrorMessage = "Unit count must be greater than 0")]
    public decimal UnitCount { get; set; }

    [Range(0, double.MaxValue, ErrorMessage = "Purchase price must be positive")]
    public decimal PurchasePrice { get; set; }

    [Range(0, double.MaxValue, ErrorMessage = "Wholesale price must be positive")]
    public decimal WholesalePrice { get; set; }

    [Range(0, double.MaxValue, ErrorMessage = "Half wholesale price must be positive")]
    public decimal HalfWholesalePrice { get; set; }

    [Range(0, double.MaxValue, ErrorMessage = "Retail price must be positive")]
    public decimal RetailPrice { get; set; }

    [Range(0, double.MaxValue, ErrorMessage = "Lowest price must be positive")]
    public decimal LowestPrice { get; set; }

    [Range(0, double.MaxValue, ErrorMessage = "Highest price must be positive")]
    public decimal HighestPrice { get; set; }

    [Range(0, 100, ErrorMessage = "Discount must be between 0 and 100")]
    public decimal Discount { get; set; }

    public bool IsDefaultPurchaseUnit { get; set; }
    public bool IsDefaultSaleUnit { get; set; }
}
