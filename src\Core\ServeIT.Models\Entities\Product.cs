using System.ComponentModel.DataAnnotations;

namespace ServeIT.Models.Entities;

public class Product : BaseEntity<string>
{
    [Required]
    [StringLength(50)]
    public override string Id { get; set; } = string.Empty;

    [StringLength(50)]
    public string? ProductId2 { get; set; }

    [StringLength(50)]
    public string? ProductNumber { get; set; }

    [Required]
    [StringLength(200)]
    public string Name { get; set; } = string.Empty;

    [StringLength(100)]
    public string? Serial { get; set; }

    public int? CategoryId { get; set; }

    [StringLength(100)]
    public string? ManufactureCountry { get; set; }

    [StringLength(200)]
    public string? StorePlace { get; set; }

    public decimal VAT { get; set; }

    public byte[]? Image { get; set; }

    public decimal RequestLimit { get; set; }

    public int? DefaultStockId { get; set; }

    public int ProductKind { get; set; }

    [StringLength(50)]
    public string? InternationalBarcode { get; set; }

    [StringLength(50)]
    public string? SystemBarcode { get; set; }

    [StringLength(1000)]
    public string? Notes { get; set; }

    // Navigation Properties
    public virtual Category? Category { get; set; }
    public virtual Stock? DefaultStock { get; set; }
    public virtual ICollection<ProductUnit> ProductUnits { get; set; } = new List<ProductUnit>();
    public virtual ICollection<InvoiceDetail> InvoiceDetails { get; set; } = new List<InvoiceDetail>();
}
