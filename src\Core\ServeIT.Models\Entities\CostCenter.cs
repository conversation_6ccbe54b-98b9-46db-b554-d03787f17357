using System.ComponentModel.DataAnnotations;

namespace ServeIT.Models.Entities;

public class CostCenter : BaseEntity<int>
{
    [Required]
    [StringLength(100)]
    public string Name { get; set; } = string.Empty;

    [StringLength(1000)]
    public string? Notes { get; set; }

    // Navigation Properties
    public virtual ICollection<Stock> Stocks { get; set; } = new List<Stock>();
    public virtual ICollection<Treasury> Treasuries { get; set; } = new List<Treasury>();
    public virtual ICollection<Invoice> Invoices { get; set; } = new List<Invoice>();
}
