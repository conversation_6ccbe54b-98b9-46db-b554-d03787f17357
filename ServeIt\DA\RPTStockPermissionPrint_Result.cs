//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace ServeIt.DA
{
    using System;
    
    public partial class RPTStockPermissionPrint_Result
    {
        public Nullable<int> per_id { get; set; }
        public Nullable<int> per_no { get; set; }
        public string per_kind { get; set; }
        public Nullable<System.DateTime> per_date { get; set; }
        public string stock_name { get; set; }
        public string stock_name_from { get; set; }
        public string stock_name_to { get; set; }
        public string notes { get; set; }
        public Nullable<long> pro_id { get; set; }
        public string pro_name { get; set; }
        public string unit_name { get; set; }
        public Nullable<decimal> quantity { get; set; }
        public Nullable<decimal> price { get; set; }
        public string user_nam { get; set; }
        public int company_id { get; set; }
        public string company_name { get; set; }
        public string activity { get; set; }
        public Nullable<System.DateTime> first_period_date { get; set; }
        public Nullable<System.DateTime> last_period_date { get; set; }
        public string commercial_registration { get; set; }
        public string tax_card { get; set; }
        public string address { get; set; }
        public string phone { get; set; }
        public string mobile { get; set; }
        public string Email { get; set; }
        public byte[] Logo { get; set; }
        public byte[] background { get; set; }
    }
}
