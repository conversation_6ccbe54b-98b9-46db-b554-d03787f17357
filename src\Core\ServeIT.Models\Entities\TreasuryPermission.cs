using System.ComponentModel.DataAnnotations;

namespace ServeIT.Models.Entities;

public class TreasuryPermission : BaseEntity<int>
{
    public int Number { get; set; }

    public TreasuryPermissionKind Kind { get; set; }

    public DateTime Date { get; set; }

    public int? CustomerId { get; set; }

    public int? ExpenseId { get; set; }

    public bool IsManufactureExpense { get; set; }

    public int? RevenueId { get; set; }

    public int? CostCenterId { get; set; }

    public bool IsDiscount { get; set; }

    public int TreasuryId { get; set; }

    public int? TreasuryFromId { get; set; }

    public int? TreasuryToId { get; set; }

    public decimal Value { get; set; }

    [StringLength(1000)]
    public string? Notes { get; set; }

    // Navigation Properties
    public virtual Customer? Customer { get; set; }
    public virtual Expense? Expense { get; set; }
    public virtual Revenue? Revenue { get; set; }
    public virtual CostCenter? CostCenter { get; set; }
    public virtual Treasury Treasury { get; set; } = null!;
    public virtual Treasury? TreasuryFrom { get; set; }
    public virtual Treasury? TreasuryTo { get; set; }
}

public enum TreasuryPermissionKind
{
    Receipt = 1,
    Payment = 2,
    Transfer = 3
}
