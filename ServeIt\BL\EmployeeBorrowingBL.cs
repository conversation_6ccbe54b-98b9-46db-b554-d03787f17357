﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;

namespace ServeIt.BL
{
    class EmployeeBorrowingBL
    {
        public string EmployeeBorrowingID { get; set; }
        public string EmployeeID { get; set; }
        public DateTime RequestDate { get; set; }
        public DateTime StartDate { get; set; }
        public bool IsAccepted { get; set; }
        public decimal BorrowingValue { get; set; }
        public int PaymentsCount { get; set; }
        public string Notes { get; set; }
        public DateTime PaymentDefaultDate { get; set; }
        public decimal PaymentValue { get; set; }
        public DateTime? PaymentActualDate { get; set; }
        public bool IsPaid { get; set; }


        public string AddOrUpdateEmployeeBorrowing(EmployeeBorrowingBL emp_borw_bl)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[9];
                para[0] = new SqlParameter("@check","a");
                para[1] = new SqlParameter("@employee_borrowing_id",emp_borw_bl.EmployeeBorrowingID);
                para[2] = new SqlParameter("@employee_id",emp_borw_bl.EmployeeID);
                para[3] = new SqlParameter("@request_date",emp_borw_bl.RequestDate);
                para[4] = new SqlParameter("@start_date",emp_borw_bl.StartDate);
                para[5] = new SqlParameter("@is_accepted",emp_borw_bl.IsAccepted);
                para[6] = new SqlParameter("@value",emp_borw_bl.BorrowingValue);
                para[7] = new SqlParameter("@payment_count",emp_borw_bl.PaymentsCount);
                para[8] = new SqlParameter("@notes",emp_borw_bl.Notes);

                return DAL.InsUpdDel("ManageEmployeeBorrowings", para);
            }
        }

        public string DeleteEmployeeBorrowing(string EmployeeBorrowingID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "d");
                para[1] = new SqlParameter("@employee_borrowing_id", EmployeeBorrowingID);

                return DAL.InsUpdDel("ManageEmployeeBorrowings", para);
            }
        }

        public string GetMaxEmployeeBorrowingID()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@check", "m");
               

                return DAL.GetValue("ManageEmployeeBorrowings", para);
            }
        }

        public DataTable GetEmployeeBorrowingByID(string EmployeeBorrowingID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "g");
                para[1] = new SqlParameter("@employee_borrowing_id",EmployeeBorrowingID);
              
                return DAL.GetData("ManageEmployeeBorrowings", para);
            }
        }

        public DataTable SearchEmployeeBorrowing(string EmployeeName)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "s");
                para[1] = new SqlParameter("@employee_name", EmployeeName);

                return DAL.GetData("ManageEmployeeBorrowings", para);
            }
        }

        public string AddOrUpdateEmployeeBorrowingPayments(EmployeeBorrowingBL emp_borw_bl)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[6];
                para[0] = new SqlParameter("@check", "a");
                para[1] = new SqlParameter("@employee_borrowing_id", emp_borw_bl.EmployeeBorrowingID);
                para[2] = new SqlParameter("@payment_default_date", emp_borw_bl.PaymentDefaultDate);
                para[3] = new SqlParameter("@payment_value", emp_borw_bl.PaymentValue);
                para[4] = new SqlParameter("@payment_actual_date", emp_borw_bl.PaymentActualDate);
                para[5] = new SqlParameter("@is_paid", emp_borw_bl.IsPaid);

                return DAL.InsUpdDel("ManageEmployeeBorrowingPayments", para);
            }
        }

        public string DeleteEmployeeBorrowingPayment(string EmployeeBorrowingID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "d");
                para[1] = new SqlParameter("@employee_borrowing_id", EmployeeBorrowingID);

                return DAL.InsUpdDel("ManageEmployeeBorrowingPayments", para);
            }
        }

        public DataTable GetEmployeeBorrowingPaymentsByID(string EmployeeBorrowingID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "g");
                para[1] = new SqlParameter("@employee_borrowing_id", EmployeeBorrowingID);

                return DAL.GetData("ManageEmployeeBorrowingPayments", para);
            }
        }


    }
}
