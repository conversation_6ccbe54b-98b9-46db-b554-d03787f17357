﻿using Microsoft.Reporting.WinForms;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.Drawing.Printing;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace ServeIt.BL
{
    class PrintBL
    {



        private int m_currentPageIndex;
        private IList<Stream> m_streams;


        // Routine to provide to the report renderer, in order to
        //    save an image for each page of the report.
        private Stream CreateStream(string name,
          string fileNameExtension, Encoding encoding,
          string mimeType, bool willSeek)
        {
            Stream stream = new MemoryStream();
            m_streams.Add(stream);
            return stream;
        }

        // Export the given report as an EMF (Enhanced Metafile) file.
        private void Export(LocalReport report)
        {

            string paperSize = Properties.Settings.Default.inv_print_paper_size;
            string pageWidth = paperSize == "A4" ? "8.27in" : paperSize == "A5" ? "5.83in" : paperSize == "8سم" ? "3in" : paperSize == "7سم" ? "2.75in" : "2in";
            string pageHeight = paperSize == "A4" ? "11.69in" : paperSize == "A5" ? "8.27in" : paperSize == "8سم" ? "10in" : paperSize == "7سم" ? "10in" : "10in";
            string deviceInfo = string.Format(@"<DeviceInfo>
                <OutputFormat>EMF</OutputFormat>
                <PageWidth>{0}</PageWidth>
                <PageHeight>{1}</PageHeight>
                <MarginTop>0</MarginTop>
                <MarginLeft>0</MarginLeft>
                <MarginRight>0</MarginRight>
                <MarginBottom>0</MarginBottom>
            </DeviceInfo>", pageWidth,
                          pageHeight);

            Warning[] warnings;
            m_streams = new List<Stream>();
            report.Render("Image", deviceInfo, CreateStream,
               out warnings);

            foreach (Stream stream in m_streams)
                stream.Position = 0;
        }

        private string MyInvPaperSize = Properties.Settings.Default.inv_print_paper_size;
        // Handler for PrintPageEvents
        private void PrintPage(object sender, PrintPageEventArgs ev)
        {
            Metafile pageImage = new
               Metafile(m_streams[m_currentPageIndex]);

            //Adjust rectangular area with printer margins.
            //int x = MyInvPaperSize == "A4"?0:MyInvPaperSize == "A5"?118:0;

            Rectangle adjustedRect = new Rectangle(
                0,
                0,
                ev.PageBounds.Width,
                ev.PageBounds.Height);


            // Draw a white background for the report
            ev.Graphics.FillRectangle(Brushes.White, adjustedRect);

            // Draw the report content
            ev.Graphics.DrawImage(pageImage, adjustedRect);


            // Prepare for the next page. Make sure we haven't hit the end.
            m_currentPageIndex++;
            ev.HasMorePages = (m_currentPageIndex < m_streams.Count);
        }

        private void Print(string PrinterName)
        {
            if (m_streams == null || m_streams.Count == 0)
                throw new Exception("Error: no stream to print.");
            PrintDocument printDoc = new PrintDocument();
            printDoc.PrinterSettings.PrinterName = PrinterName;

            PaperSize papSize = new PaperSize();
            if (MyInvPaperSize == "A4")
            {
                //printDoc.DefaultPageSettings.PaperSize = new PaperSize("A4", 1170, 830);
                papSize.PaperName = "A4";
                papSize.Width = 827;
                papSize.Height = 1169;
            }
            else if (MyInvPaperSize == "A5")
            {

                papSize.PaperName = "A5";
                papSize.Width = 583;
                papSize.Height = 827;

            }
            else if (MyInvPaperSize == "8سم")
            {

                papSize.RawKind = (int)PaperKind.Custom;
                papSize.Width = 300;
                papSize.Height = 830;
            }
            else if (MyInvPaperSize == "7سم")
            {
                papSize.RawKind = (int)PaperKind.Custom;
                papSize.Width = 275;
                papSize.Height = 830;
            }
            else if (Properties.Settings.Default.inv_print_paper_size == "5سم")
            {
                papSize.RawKind = (int)PaperKind.Custom;
                papSize.Width = 220;
                papSize.Height = 830;
            }
            //printDoc.DefaultPageSettings.Margins = new Margins(int.Parse(Properties.Settings.Default.inv_print_margin_left),
            //                                           int.Parse(Properties.Settings.Default.inv_print_margin_right),
            //                                            int.Parse(Properties.Settings.Default.inv_print_margin_top),
            //                                           int.Parse(Properties.Settings.Default.inv_print_margin_bottom));
            printDoc.DefaultPageSettings.PaperSize = papSize;

            if (!printDoc.PrinterSettings.IsValid)
            {
                MessageBox.Show("من فضلك اختر الطابعة من اعدادات طباعة الفواتير اولا");
            }
            else
            {
                printDoc.PrintPage += new PrintPageEventHandler(PrintPage);
                m_currentPageIndex = 0;
                printDoc.Print();
            }
        }
        // Create a local report for Report.rdlc, load the data,
        //    export the report to an .emf file, and print it.
        public void InvoiceSilentPrint(LocalReport report)
        {
            Export(report);
            Print(Properties.Settings.Default.inv_printer_name);
        }

        public void RestaurantOrderSilentPrint(LocalReport report, String PrinterName)
        {

            Export(report);
            Print(PrinterName);
        }

        public string GetReportInvoicePath(bool IsRestauarantInvoice, bool IsTaxationInvoice)
        {
            string ReportPath = string.Empty;
            if (IsRestauarantInvoice)
            {
                if (IsTaxationInvoice)
                {
                    ReportPath = "ServeIt.PL.invoices.RPTInvoiceRollTaxPrint8cm.rdlc";
                }
                else
                {
                    if (Properties.Settings.Default.hide_inv_print_company_details == true)
                    {
                        ReportPath = "ServeIt.PL.Restaurant.RPTRestaurantInvoiceRollPrint8cmWithoutComDetails.rdlc";
                    }
                    else
                    {
                        ReportPath = "ServeIt.PL.Restaurant.RPTRestaurantInvoiceRollPrint8cm.rdlc";
                    }
                }
            }
            else
            {
                if (MyInvPaperSize == "A4")
                {
                    if (IsTaxationInvoice)
                    {
                        if (Properties.Settings.Default.hide_inv_print_qty_summary == true)
                        {
                            ReportPath = "ServeIt.PL.invoices.RPTInvoicePaperTaxPrint.rdlc";
                        }
                        else
                        {
                            ReportPath = "ServeIt.PL.invoices.RPTInvoicePaperTaxPrintWithQty.rdlc";
                        }
                    }
                    else
                    {
                        if (Properties.Settings.Default.hide_inv_print_company_details == true)
                        {
                            if (Properties.Settings.Default.hide_inv_print_qty_summary == true)
                            {
                                ReportPath = "ServeIt.PL.invoices.RPTInvoicePaperPrintWithoutComDetails.rdlc";
                            }
                            else
                            {
                                ReportPath = "ServeIt.PL.invoices.RPTInvoicePaperPrintWithoutComDetailsWithQty.rdlc";
                            }
                        }
                        else
                        {
                            if (Properties.Settings.Default.hide_inv_print_qty_summary == true)
                            {
                                ReportPath = "ServeIt.PL.invoices.RPTInvoicePaperPrint.rdlc";
                            }
                            else
                            {
                                ReportPath = "ServeIt.PL.invoices.RPTInvoicePaperPrintWithQty.rdlc";
                            }
                        }
                    }
                }
                else if (MyInvPaperSize == "A5")
                {
                    if (IsTaxationInvoice)
                    {
                        ReportPath = "ServeIt.PL.invoices.RPTInvoicePaperTaxPrintA5.rdlc";
                    }
                    else
                    {

                        if (Properties.Settings.Default.inv_total_orders_verticaly)
                        {
                            if (Properties.Settings.Default.hide_inv_print_company_details == true)
                            {
                                ReportPath = "ServeIt.PL.invoices.RPTInvoicePaperPrintWithoutComDetailsA5.rdlc";
                            }
                            else
                            {
                                ReportPath = "ServeIt.PL.invoices.RPTInvoicePaperPrintA5.rdlc";
                            }
                        }
                        else
                        {
                            if (Properties.Settings.Default.hide_inv_print_company_details == true)
                            {
                                ReportPath = "ServeIt.PL.invoices.RPTInvoicePaperPrintA5TotalsHorizontallyWithoutComDet.rdlc";
                            }
                            else
                            {
                                ReportPath = "ServeIt.PL.invoices.RPTInvoicePaperPrintA5TotalsHorizontally.rdlc";
                            }

                        }

                    }
                }
                else if (MyInvPaperSize == "8سم")
                {
                    if (IsTaxationInvoice)
                    {
                        ReportPath = "ServeIt.PL.invoices.RPTInvoiceRollTaxPrint8cm.rdlc";
                    }
                    else
                    {
                        if (Properties.Settings.Default.hide_inv_print_company_details == true)
                        {
                            if (Properties.Settings.Default.show_unit_in_inv_roll)
                            {
                                ReportPath = "ServeIt.PL.invoices.RPTInvoiceRollPrint8cmWithoutComDetailsWithUnit.rdlc";
                            }
                            else
                            {
                                ReportPath = "ServeIt.PL.invoices.RPTInvoiceRollPrint8cmWithoutComDetails.rdlc";
                            }
                        }
                        else
                        {
                            if (Properties.Settings.Default.show_unit_in_inv_roll)
                            {
                                ReportPath = "ServeIt.PL.invoices.RPTInvoiceRollPrint8cmWithUnit.rdlc";
                            }
                            else
                            {
                                ReportPath = "ServeIt.PL.invoices.RPTInvoiceRollPrint8cm.rdlc";
                            }
                            
                        }
                    }
                }
                else if (MyInvPaperSize == "7سم")
                {
                    if (IsTaxationInvoice)
                    {
                        ReportPath = "ServeIt.PL.invoices.RPTInvoiceRollTaxPrint7cm.rdlc";
                    }
                    else
                    {
                        if (Properties.Settings.Default.hide_inv_print_company_details == true)
                        {
                            ReportPath = "ServeIt.PL.invoices.RPTInvoiceRollPrint7cmWithoutComDetails.rdlc";
                        }
                        else
                        {
                            ReportPath = "ServeIt.PL.invoices.RPTInvoiceRollPrint7cm.rdlc";
                        }
                    }
                }
                else if (MyInvPaperSize == "5سم")
                {
                    if (IsTaxationInvoice)
                    {
                        ReportPath = "ServeIt.PL.invoices.RPTInvoiceRollTaxPrint5cm.rdlc";
                    }
                    else
                    {
                        if (Properties.Settings.Default.hide_inv_print_company_details == true)
                        {
                            ReportPath = "ServeIt.PL.invoices.RPTInvoiceRollPrint5cmWithoutComDetails.rdlc";
                        }
                        else
                        {
                            ReportPath = "ServeIt.PL.invoices.RPTInvoiceRollPrint5cm.rdlc";
                        }
                    }
                }
            }

            return ReportPath;
        }

        public void Dispose()
        {
            if (m_streams != null)
            {
                foreach (Stream stream in m_streams)
                    stream.Close();
                m_streams = null;
            }
        }
    }
}
