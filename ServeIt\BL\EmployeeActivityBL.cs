﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;

namespace ServeIt.BL
{
    class EmployeeActivityBL
    {
        public string ActivityID { get; set; }
        public string EmployeeID { get; set; }
        public string SalaryItemID { get; set; }
        public decimal Value { get; set; }
        public DateTime ActivityDate { get; set; }
        public string Notes { get; set; }
        public string UserName { get; set; }
        public string EmployeeName { get; set; }
        public string ManagementID { get; set; }
        public DateTime? DateFrom { get; set; }
        public DateTime? DateTo { get; set; }

        public string AddOrUpdateEmployeeActivity(EmployeeActivityBL emp_actv_bl)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[8];
                para[0] = new SqlParameter("@check", "a");
                para[1] = new SqlParameter("@activity_id", emp_actv_bl.ActivityID);
                para[2] = new SqlParameter("@employee_id", emp_actv_bl.EmployeeID);
                para[3] = new SqlParameter("@salary_item_id",emp_actv_bl.SalaryItemID);
                para[4] = new SqlParameter("@value", emp_actv_bl.Value);
                para[5] = new SqlParameter("@activity_date", emp_actv_bl.ActivityDate);
                para[6] = new SqlParameter("@notes",emp_actv_bl.Notes);
                para[7] = new SqlParameter("@user_name", emp_actv_bl.UserName);
                return DAL.InsUpdDel("ManageEmployeeActivity", para);
            }
        }

        public string GetMaxEmployeeActivity()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@check", "m");
                return DAL.GetValue("ManageEmployeeActivity", para);
            }
        }

        public string DeleteEmployeeActivity(string ActivityID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check","d");
                para[1] = new SqlParameter("@activity_id",ActivityID);
                return DAL.InsUpdDel("ManageEmployeeActivity", para);
            }
        }

        public DataTable GetEmployeeActivityByActivityID(string ActivityID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check","g");
                para[1] = new SqlParameter("@activity_id",ActivityID);
                return DAL.GetData("ManageEmployeeActivity", para);
            }
        }

        public DataTable SearchEmployeeActivity(EmployeeActivityBL emp_actv_bl)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[5];
                para[0] = new SqlParameter("@check", "s");
                para[1] = new SqlParameter("@management_id",emp_actv_bl.ManagementID);
                para[2] = new SqlParameter("@employee_name",emp_actv_bl.EmployeeName);
                para[3] = new SqlParameter("@date_from",emp_actv_bl.DateFrom);
                para[4] = new SqlParameter("@date_to",emp_actv_bl.DateTo);
                return DAL.GetData("ManageEmployeeActivity", para);
            }
        }
    }
}
