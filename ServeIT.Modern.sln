Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1

Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}"
EndProject

Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Core", "Core", "{A1B2C3D4-5E6F-7890-ABCD-EF1234567890}"
EndProject

Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Infrastructure", "Infrastructure", "{B2C3D4E5-6F78-9012-BCDE-F23456789012}"
EndProject

Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Presentation", "Presentation", "{C3D4E5F6-7890-1234-CDEF-345678901234}"
EndProject

Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ServeIT.Models", "src\Core\ServeIT.Models\ServeIT.Models.csproj", "{D4E5F6G7-8901-2345-DEF0-456789012345}"
EndProject

Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ServeIT.Database", "src\Infrastructure\ServeIT.Database\ServeIT.Database.csproj", "{E5F6G7H8-9012-3456-EF01-567890123456}"
EndProject

Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ServeIT.API", "src\Presentation\ServeIT.API\ServeIT.API.csproj", "{F6G7H8I9-0123-4567-F012-678901234567}"
EndProject

Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ServeIT.BlazorServer", "src\Presentation\ServeIT.BlazorServer\ServeIT.BlazorServer.csproj", "{G7H8I9J0-1234-5678-0123-789012345678}"
EndProject

Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{D4E5F6G7-8901-2345-DEF0-456789012345}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D4E5F6G7-8901-2345-DEF0-456789012345}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D4E5F6G7-8901-2345-DEF0-456789012345}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D4E5F6G7-8901-2345-DEF0-456789012345}.Release|Any CPU.Build.0 = Release|Any CPU
		{E5F6G7H8-9012-3456-EF01-567890123456}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E5F6G7H8-9012-3456-EF01-567890123456}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E5F6G7H8-9012-3456-EF01-567890123456}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E5F6G7H8-9012-3456-EF01-567890123456}.Release|Any CPU.Build.0 = Release|Any CPU
		{F6G7H8I9-0123-4567-F012-678901234567}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F6G7H8I9-0123-4567-F012-678901234567}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F6G7H8I9-0123-4567-F012-678901234567}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F6G7H8I9-0123-4567-F012-678901234567}.Release|Any CPU.Build.0 = Release|Any CPU
		{G7H8I9J0-1234-5678-0123-789012345678}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{G7H8I9J0-1234-5678-0123-789012345678}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{G7H8I9J0-1234-5678-0123-789012345678}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{G7H8I9J0-1234-5678-0123-789012345678}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{A1B2C3D4-5E6F-7890-ABCD-EF1234567890} = {8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}
		{B2C3D4E5-6F78-9012-BCDE-F23456789012} = {8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}
		{C3D4E5F6-7890-1234-CDEF-345678901234} = {8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}
		{D4E5F6G7-8901-2345-DEF0-456789012345} = {A1B2C3D4-5E6F-7890-ABCD-EF1234567890}
		{E5F6G7H8-9012-3456-EF01-567890123456} = {B2C3D4E5-6F78-9012-BCDE-F23456789012}
		{F6G7H8I9-0123-4567-F012-678901234567} = {C3D4E5F6-7890-1234-CDEF-345678901234}
		{G7H8I9J0-1234-5678-0123-789012345678} = {C3D4E5F6-7890-1234-CDEF-345678901234}
	EndGlobalSection
EndGlobal
