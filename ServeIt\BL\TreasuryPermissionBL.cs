﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ServeIt.BL
{
    class TreasuryPermissionBL
    {
        public string PerId { get; set; }
        public int? PerNo { get; set; }
        public int PerKind { get; set; }
        public int? CostCenteID { get; set; }
        public DateTime Per_Date { get; set; }
        public DateTime? DateFrom { get; set; }
        public DateTime? DateTo { get; set; }
        public int? CustId { get; set; }
        public int? ExpenseID { get; set; }
        public bool ManufactureExpense { get; set; }
        public int? RevenueID { get; set; }
        public string CustName { get; set; }
        public int? TreasID { get; set; }
        public int? TreasForm { get; set; }
        public int? TreasTo { get; set; }
        public bool IsDiscount { get; set; }
        public decimal Value { get; set; }
        public string Notes { get; set; }
        public string User_Name { get; set; }


        public string GetTreasuryPermissionCount()
        {
            string result = string.Empty;
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                using (SqlConnection con = new SqlConnection())
                {
                    con.ConnectionString = DAL.MyConnectionString().ConnectionString;
                    if (con.State != ConnectionState.Open)
                    {
                        con.Open();
                    }
                    using (SqlCommand cmd = new SqlCommand())
                    {
                        cmd.Connection = con;
                        cmd.CommandText = "select count(per_id) from treasury_permission";

                        try
                        {
                            result = cmd.ExecuteScalar().ToString();
                        }
                        catch (SqlException ex)
                        {
                            return ex.Message;
                        }
                    }

                }
            }
            return result;
        }

        public DataTable SearchTreasuryPermission(TreasuryPermissionBL treas_per_bl)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[6];
                para[0] = new SqlParameter("@per_kind", treas_per_bl.PerKind);
                para[1] = new SqlParameter("@per_id", treas_per_bl.PerId);
                para[2] = new SqlParameter("@per_no", treas_per_bl.PerNo);
                para[3] = new SqlParameter("@cust_id", treas_per_bl.CustId);
                para[4] = new SqlParameter("@datefrom", treas_per_bl.DateFrom);
                para[5] = new SqlParameter("@dateto", treas_per_bl.DateTo);
                return DAL.GetData("SearchTreasuryPermissionByKind", para);
            }
        }
        
        public DataTable SearchTreasuryExpenses(TreasuryPermissionBL treas_per_bl)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[7];
                para[0] = new SqlParameter("@per_kind", treas_per_bl.PerKind);
                para[1] = new SqlParameter("@cost_center_id", treas_per_bl.CostCenteID);
                para[2] = new SqlParameter("@expense_id", treas_per_bl.ExpenseID);
                para[3] = new SqlParameter("@treasury_id", treas_per_bl.TreasID);
                para[4] = new SqlParameter("@user_name", treas_per_bl.User_Name);
                para[5] = new SqlParameter("@datefrom", treas_per_bl.DateFrom);
                para[6] = new SqlParameter("@dateto", treas_per_bl.DateTo);
                return DAL.GetData("SearchTreasuryExpenses", para);
            }
        }

        public DataTable SearchTreasuryRevenues(TreasuryPermissionBL treas_per_bl)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[7];
                para[0] = new SqlParameter("@per_kind", treas_per_bl.PerKind);
                para[1] = new SqlParameter("@cost_center_id", treas_per_bl.CostCenteID);
                para[2] = new SqlParameter("@revenue_id", treas_per_bl.RevenueID);
                para[3] = new SqlParameter("@treasury_id", treas_per_bl.TreasID);
                para[4] = new SqlParameter("@user_name", treas_per_bl.User_Name);
                para[5] = new SqlParameter("@datefrom", treas_per_bl.DateFrom);
                para[6] = new SqlParameter("@dateto", treas_per_bl.DateTo);
                return DAL.GetData("SearchTreasuryRevenue", para);
            }
        }

        public DataTable SearchTreasuryWithdrawAndDepositPermission(TreasuryPermissionBL treas_per_bl)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[5];
                para[0] = new SqlParameter("@per_kind", treas_per_bl.PerKind);
                para[1] = new SqlParameter("@per_id", treas_per_bl.PerId);
                para[2] = new SqlParameter("@per_no", treas_per_bl.PerNo);
                para[3] = new SqlParameter("@datefrom", treas_per_bl.DateFrom);
                para[4] = new SqlParameter("@dateto", treas_per_bl.DateTo);
                return DAL.GetData("SearchTreasuryWithdrawAndDepositPermission", para);
            }
        }

        public string AddOrUpdateCustomerPaymentAndDeposit(BE.TreasuryPermissionBE treasperbe) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[11];
                para[0] = new SqlParameter("@check","a");
                para[1] = new SqlParameter("@per_id", treasperbe.PerId);
                para[2] = new SqlParameter("@per_no", treasperbe.PerNo);
                para[3] = new SqlParameter("@per_kind", treasperbe.PerKind);
                para[4] = new SqlParameter("@per_date", treasperbe.Per_Date);
                para[5] = new SqlParameter("@cust_id", treasperbe.CustId);
                para[6] = new SqlParameter("@treas_id", treasperbe.TreasID);
                para[7] = new SqlParameter("@isdiscount", treasperbe.IsDiscount);
                para[8] = new SqlParameter("@value", treasperbe.Value);
                para[9] = new SqlParameter("@notes", treasperbe.Notes);
                para[10] = new SqlParameter("@user_name", treasperbe.User_Name);
                return DAL.InsUpdDel("ManageTreasuryPermission", para);
            }
        }

        public string AddOrUpdateTreasuryPermission(TreasuryPermissionBL treas_per_bl)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[17];
                para[0] = new SqlParameter("@check", "a");
                para[1] = new SqlParameter("@per_id", treas_per_bl.PerId);
                para[2] = new SqlParameter("@per_no", treas_per_bl.PerNo);
                para[3] = new SqlParameter("@per_kind", treas_per_bl.PerKind);
                para[4] = new SqlParameter("@per_date", treas_per_bl.Per_Date);
                para[5] = new SqlParameter("@cust_id", treas_per_bl.CustId);
                para[6] = new SqlParameter("@expense_id", treas_per_bl.ExpenseID);
                para[7] = new SqlParameter("@manufacture_expense", treas_per_bl.ManufactureExpense);
                para[8] = new SqlParameter("@revenue_id", treas_per_bl.RevenueID);
                para[9] = new SqlParameter("@cost_center_id", treas_per_bl.CostCenteID);
                para[10] = new SqlParameter("@treas_id", treas_per_bl.TreasID);
                para[11] = new SqlParameter("@treas_from",treas_per_bl.TreasForm);
                para[12] = new SqlParameter("@treas_to", treas_per_bl.TreasTo); 
                para[13] = new SqlParameter("@isdiscount", treas_per_bl.IsDiscount);
                para[14] = new SqlParameter("@value", treas_per_bl.Value);
                para[15] = new SqlParameter("@notes", treas_per_bl.Notes);
                para[16] = new SqlParameter("@user_name", treas_per_bl.User_Name);
                return DAL.InsUpdDel("ManageTreasuryPermission", para);
            }
        }

        public string AddOrUpdateTreasuryWithdrawOrDeposit(TreasuryPermissionBL treas_per_bl) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[12];
                para[0] = new SqlParameter("@check", "a");
                para[1] = new SqlParameter("@per_id", treas_per_bl.PerId);
                para[2] = new SqlParameter("@per_no", treas_per_bl.PerNo);
                para[3] = new SqlParameter("@per_kind", treas_per_bl.PerKind);
                para[4] = new SqlParameter("@per_date", treas_per_bl.Per_Date);
                para[5] = new SqlParameter("@cust_id", treas_per_bl.CustId);
                para[6] = new SqlParameter("@treas_id", treas_per_bl.TreasID);
                para[7] = new SqlParameter("@treas_from", treas_per_bl.TreasForm);
                para[8] = new SqlParameter("@treas_to", treas_per_bl.TreasTo);
                para[9] = new SqlParameter("@value", treas_per_bl.Value);
                para[10] = new SqlParameter("@notes", treas_per_bl.Notes);
                para[11] = new SqlParameter("@user_name", treas_per_bl.User_Name);
                return DAL.InsUpdDel("ManageTreasuryWithdrawAndDeposit", para);
            }
        }

        public DataTable PrintTreasuryPermission(string PerID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@per_id", PerID);
                return DAL.GetData("PrintTreasuryPermission", para);
            }
        }


        public string DeleteTreasuryPermission(string TreasuryPerID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check","d");
                para[1] = new SqlParameter("@per_id", TreasuryPerID);
                return DAL.InsUpdDel("ManageTreasuryPermission", para);
            }
        }



        public string GetMaxTreasuryPermission()
        {
            using (DA.DataAccess DAl = new DA.DataAccess())
            {
                return DAl.GetValue("GetMaxTreasuryPermssion");
            }
        }

        public DataTable GetTreasuryPermissionById(string PerId) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@per_id", PerId);
                return DAL.GetData("GetTreasuryPermissionById", para);
            }
        }

        public DataTable GetNextCustomerDepositTreasuryPer(string CurrentID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@current_id", CurrentID);
                return DAL.GetData("GetNextCustomerDepositTreasuryPer", para);
            }
        }

        public DataTable GetNextCustomerPaymentTreasuryPer(string CurrentID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@current_id", CurrentID);
                return DAL.GetData("GetNextCustomerPaymentTreasuryPer", para);
            }
        }

        public DataTable GetNextDepositTreasuryPer(string CurrentID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@current_id", CurrentID);
                return DAL.GetData("GetNextDepositTreasuryPer", para);
            }
        }

        public DataTable GetNextWithdrawTreasuryPer(string CurrentID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@current_id", CurrentID);
                return DAL.GetData("GetNextWithdrawTreasuryPer", para);
            }
        }

        public DataTable GetNextRelocateTreasuryPer(string CurrentID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@current_id", CurrentID);
                return DAL.GetData("GetNextRelocateTreasuryPer", para);
            }
        }
        public DataTable GetPreviousCustomerDepositTreasuryPerID(string CurrentID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@current_id", CurrentID);
                return DAL.GetData("GetPreviousCustomerDepositTreasuryPerID", para);
            }
        }

        public DataTable GetPreviousCustomerPaymentTreasuryPerID(string CurrentID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@current_id", CurrentID);
                return DAL.GetData("GetPreviousCustomerPaymentTreasuryPerID", para);
            }
        }

        public DataTable GetPreviousDepositTreasuryPerID(string CurrentID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@current_id", CurrentID);
                return DAL.GetData("GetPreviousDepositTreasuryPerID", para);
            }
        }

        public DataTable GetPreviousWithdrawTreasuryPerID(string CurrentID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@current_id", CurrentID);
                return DAL.GetData("GetPreviousWithdrawTreasuryPerID", para);
            }
        }

        public DataTable GetPreviousRelocateTreasuryPerID(string CurrentID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@current_id", CurrentID);
                return DAL.GetData("GetPreviousRelocateTreasuryPerID", para);
            }
        }

    }
}
