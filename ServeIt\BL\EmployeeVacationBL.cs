﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;

namespace ServeIt.BL
{
    class EmployeeVacationBL
    {
        public string VacationID{ get; set; }
        public string EmployeeID { get; set; }
        public string EmployeeName { get; set; }
        public string VacationTypeID { get; set; }
        public DateTime AddDate { get; set; }
        public DateTime? DateFrom { get; set; }
        public DateTime? DateTo { get; set; }
        public string UserName { get; set; }
        public string Notes { get; set; }

        public string AddOrUpdateEmployeeVacation (EmployeeVacationBL EmpVacationBL)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[9];
                para[0] = new SqlParameter("@check","a");
                para[1] = new SqlParameter("@vacation_id",EmpVacationBL.VacationID);
                para[2] = new SqlParameter("@employee_id",EmpVacationBL.EmployeeID);
                para[3] = new SqlParameter("@vacation_type_id",EmpVacationBL.VacationTypeID);
                para[4] = new SqlParameter("@add_date", EmpVacationBL.AddDate);
                para[5] = new SqlParameter("@vacation_date_from",EmpVacationBL.DateFrom);
                para[6] = new SqlParameter("@vacation_date_to",EmpVacationBL.DateTo);
                para[7] = new SqlParameter("@user_name", EmpVacationBL.UserName);
                para[8] = new SqlParameter("@notes", EmpVacationBL.Notes);
                return DAL.InsUpdDel("ManageEmployeeVacations", para);
            }
        }

        public string GetMaxEmployeeVacationID()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@check","m");
                return DAL.GetValue("ManageEmployeeVacations", para);
            }
        }

        public string DeleteEmployeeVacation(string VacationID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check","d");
                para[1] = new SqlParameter("@vacation_id", VacationID);
                return DAL.InsUpdDel("ManageEmployeeVacations", para);
            }
        }

        public DataTable GetEmployeeVacationByID(string VacaTionID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check","g");
                para[1] = new SqlParameter("@vacation_id",VacaTionID);
               
                return DAL.GetData("ManageEmployeeVacations", para);
            }
        }

        public DataTable SearchEmployeeVacations(EmployeeVacationBL EmpVacationBL)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[5];
                para[0] = new SqlParameter("@check", "s");
                para[1] = new SqlParameter("@employee_name",EmpVacationBL.EmployeeName);
                para[2] = new SqlParameter("@vacation_type_id",EmpVacationBL.VacationTypeID);
                para[3] = new SqlParameter("@vacation_date_from",EmpVacationBL.DateFrom);
                para[4] = new SqlParameter("@vacation_date_to", EmpVacationBL.DateTo);
                return DAL.GetData("ManageEmployeeVacations", para);
            }
        }

        public DataTable GetVacationTypes()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetData("GetVacationAllTypes", null);
            }
        }
    }
}
