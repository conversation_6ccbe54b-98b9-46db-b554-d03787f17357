﻿using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Text;

namespace ServeIt.BL
{
    class EmployeeSalaryItemBL
    {
        public string EmployeeID { get; set; }
        public string EmployeeSalaryItemID { get; set; }
        public string SalaryItemID { get; set; }
        public decimal SalaryItemValue { get; set; }

        public string AddOrUpdateEmployeeSalaryItem(EmployeeSalaryItemBL emp_salary_item_bl)
        {
            using (DA.DataAccess DAL =new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[5];
                para[0] = new SqlParameter("@check","a");
                para[1] = new SqlParameter("@employee_id", emp_salary_item_bl.EmployeeID);
                para[2] = new SqlParameter("@employee_salary_item_id", emp_salary_item_bl.EmployeeSalaryItemID);
                para[3] = new SqlParameter("@salary_item_id", emp_salary_item_bl.SalaryItemID);
                para[4] = new SqlParameter("@salary_item_value", emp_salary_item_bl.SalaryItemValue);
                return DAL.InsUpdDel("ManageEmployeeSalaryItems", para);
            }
        }
    }
}
