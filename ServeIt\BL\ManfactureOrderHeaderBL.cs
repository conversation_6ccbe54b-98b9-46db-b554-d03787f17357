﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;

namespace ServeIt.BL
{
    class ManfactureOrderHeaderBL
    {
        public string ManufactureOrderID{ get; set; }
        public string ManufactureOrderTypeID { get; set; }
        public string CostCenterID { get; set; }
        public string VendorID { get; set; }
        public DateTime ManfactureDate { get; set; }
        public string Notes { get; set; }
        public string UserName { get; set; }
       
        public DateTime? DateFrom { get; set; }
        public DateTime? DateTo { get; set; }


        public string GetManufacturingOrdersCount()
        {
            string result = string.Empty;
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                using (SqlConnection con = new SqlConnection())
                {
                    con.ConnectionString = DAL.MyConnectionString().ConnectionString;
                    if (con.State != ConnectionState.Open)
                    {
                        con.Open();
                    }
                    using (SqlCommand cmd = new SqlCommand())
                    {
                        cmd.Connection = con;
                        cmd.CommandText = "select count(manufacture_order_id) from manufacture_order_header";
                        try
                        {
                            result = cmd.ExecuteScalar().ToString();
                        }
                        catch (SqlException ex)
                        {
                            return ex.Message;
                        }
                    }
                }
            }
            return result;
        }

        public string AddOrUpdateManufacturingOrderHeader(ManfactureOrderHeaderBL manf_bl)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[8];
                para[0] = new SqlParameter("@check","a");
                para[1] = new SqlParameter("@manufacture_order_id",manf_bl.ManufactureOrderID);
                para[2] = new SqlParameter("@manufacture_order_type_id",manf_bl.ManufactureOrderTypeID);
                para[3] = new SqlParameter("@cost_center_id",manf_bl.CostCenterID);
                para[4] = new SqlParameter("@vend_id", manf_bl.VendorID);
                para[5] = new SqlParameter("@manufacture_date",manf_bl.ManfactureDate);
                para[6] = new SqlParameter("@notes",manf_bl.Notes);
                para[7] = new SqlParameter("@user_name",manf_bl.UserName);

                return DAL.InsUpdDel("ManageManufactureOrderHeader", para);
            }
        }

        public DataTable GetManufactureOrderHeaderByOrderID(string ManufactureOrderID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@manufacture_order_id", ManufactureOrderID);

                return DAL.GetData("GetManufactureOrderHeaderByOrderID", para);
            }
        }

        public string GetMaxManufacturingOrderHeaderID()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@check", "m");

                return DAL.GetValue("ManageManufactureOrderHeader", para);
            }
        }

        public DataTable GetPreviousManufacture(string CurrentManufactureID, string ManufactureTypeID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@manf_id",CurrentManufactureID);
                para[1] = new SqlParameter("@manf_type_id",ManufactureTypeID);
                return DAL.GetData("GetPreviousManufacture", para);
            }
        }

        public DataTable GetNextManufacture(string CurrentManufactureID, string ManufactureTypeID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@manf_id", CurrentManufactureID);
                para[1] = new SqlParameter("@manf_type_id", ManufactureTypeID);
                return DAL.GetData("GetNextManufacture", para);
            }
        }

        public string DeleteManufacturingOrderHeader(string ManufactureOrderID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "d");
                para[1] = new SqlParameter("@manufacture_order_id", ManufactureOrderID);

                return DAL.InsUpdDel("ManageManufactureOrderHeader", para);
            }
        }

        public DataTable SearchManufactureOrders(ManfactureOrderHeaderBL manf_bl)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[4];
                para[0] = new SqlParameter("@manufacture_order_id",manf_bl.ManufactureOrderID );
                para[1] = new SqlParameter("@manufacture_order_type_id", manf_bl.ManufactureOrderTypeID);
                para[2] = new SqlParameter("@datefrom", manf_bl.DateFrom);
                para[3] = new SqlParameter("@dateto", manf_bl.DateTo);
                return DAL.GetData("SearchManfactureOrders", para);
            }
        }
    }

}
