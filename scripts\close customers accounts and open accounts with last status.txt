delete from customer_initial_balance

insert into customer_initial_balance (cust_id,quantity,date)
select cust_id,SUM( value),GETDATE() from dbo.CustomerTransaction(null) where cust_id is not null group by cust_id

insert into customer_initial_balance (cust_id,quantity,date)
select c.cust_id,0,GETDATE() 
from [customer&vendor] c 
	 left join customer_initial_balance b 
		on c.cust_id = b.cust_id
		where b.cust_id is null