using System.ComponentModel.DataAnnotations;

namespace ServeIT.Models.Entities;

public class ProductUnit : BaseEntity<int>
{
    [Required]
    [StringLength(50)]
    public string ProductId { get; set; } = string.Empty;

    public int UnitId { get; set; }

    public decimal UnitCount { get; set; }

    public decimal PurchasePrice { get; set; }

    public decimal WholesalePrice { get; set; }

    public decimal HalfWholesalePrice { get; set; }

    public decimal RetailPrice { get; set; }

    public decimal LowestPrice { get; set; }

    public decimal HighestPrice { get; set; }

    public decimal Discount { get; set; }

    public bool IsDefaultPurchaseUnit { get; set; }

    public bool IsDefaultSaleUnit { get; set; }

    [StringLength(50)]
    public string? InternationalBarcode { get; set; }

    [StringLength(50)]
    public string? SystemBarcode { get; set; }

    // Navigation Properties
    public virtual Product Product { get; set; } = null!;
    public virtual Unit Unit { get; set; } = null!;
}
