using System.ComponentModel.DataAnnotations;

namespace ServeIT.Models.Entities;

public class Unit : BaseEntity<int>
{
    [Required]
    [StringLength(50)]
    public string Name { get; set; } = string.Empty;

    [StringLength(50)]
    public string? Kind { get; set; }

    // Navigation Properties
    public virtual ICollection<ProductUnit> ProductUnits { get; set; } = new List<ProductUnit>();
    public virtual ICollection<InvoiceDetail> InvoiceDetails { get; set; } = new List<InvoiceDetail>();
}
