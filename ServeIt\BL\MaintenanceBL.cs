﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;

namespace ServeIt.BL
{
    class MaintenanceBL
    {
        public string MaintenanceID { get; set; }
        public string CostCenterID { get; set; }
        public string CustomerID { set; get; }
        public string CustomerName { set; get; }
        public string CustomerStatus { get; set; }
        public string CustomerPhone { get; set; }
        public string CustomerMobile { get; set; }
        public string CustomerCity { get; set; }
        public string CustomerArea { get; set; }
        public string CustomerAddress { get; set; }
        public DateTime? CommunicationDate { get; set; }
        public DateTime? GoDate { get; set; }
        public DateTime? DrawDate { get; set; }
        public DateTime? DeliveryDate { get; set; }
        public DateTime? MaintenanceEntryDate { get; set; }
        public string ItemCategory { get; set; }
        public string Item { get; set; }
        public string ItemSerial { get; set; }
        public string ItemColor { get; set; }
        public string ItemAccessories { get; set; }
        public string DamageCategory { get; set; }
        public string Damage { get; set; }
        public string CustomerComplaint { get; set; }
        public decimal MaintenanceLimit { get; set; }
        public decimal MaintenanceIndexation { get; set; }
        public decimal GoCost { get; set; }
        public decimal DrawCost { get; set; }
        public decimal DeliveryCost { get; set; }
        public decimal OtherCosts { get; set; }
        public string TreasuryID { get; set; }
        public string ExpenseID { get; set; }
        public string RevenueID { get; set; }
        public string TreasuryExpensePerID { get; set; }
        public string TreasuryRevenuePerID { get; set; }
        public string VisitEngineer { get; set; }
        public string MaintenanceEngineer { get; set; }
        public decimal MaintenanceEngineerRatio { get; set; }
        public string WorkShopID { get; set; }
        public string IsPaid { get; set; }
        public string MaintenanceStatus { get; set; }
        public string IsFixed { get; set; }
        public string VisitEngineerReport { get; set; }
        public string MaintenanceEngineerReport { get; set; }
        public string MaintenanceReprot { get; set; }
        public string UserName { get; set; }

        public string GetMaintenanceCount()
        {
            string result = string.Empty;
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                using (SqlConnection con = new SqlConnection())
                {
                    con.ConnectionString = DAL.MyConnectionString().ConnectionString;
                    if (con.State != ConnectionState.Open)
                    {
                        con.Open();
                    }
                    using (SqlCommand cmd = new SqlCommand())
                    {
                        cmd.Connection = con;
                        cmd.CommandText = "select count(maintenance_id) from maintenance";

                        try
                        {
                            result = cmd.ExecuteScalar().ToString();
                        }
                        catch (SqlException ex)
                        {

                            return ex.Message;
                        }
                    }

                }
            }
            return result;
        }

        public string AddOrUpdateMaintenanceProcess (MaintenanceBL mainten_bl)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[35];
                para[0] = new SqlParameter("@check", 'a');
                para[1] = new SqlParameter("@maintenance_id", mainten_bl.MaintenanceID);
                para[2] = new SqlParameter("@cost_center_id", mainten_bl.CostCenterID);
                para[3] = new SqlParameter("@cust_id", mainten_bl.CustomerID);
                para[4] = new SqlParameter("@cust_name", mainten_bl.CustomerName);
                para[5] = new SqlParameter("@cust_phone", mainten_bl.CustomerPhone);
                para[6] = new SqlParameter("@cust_mobile", mainten_bl.CustomerMobile);
                para[7] = new SqlParameter("@cust_city", mainten_bl.CustomerCity);
                para[8] = new SqlParameter("@cust_area", mainten_bl.CustomerArea);
                para[9] = new SqlParameter("@cust_address", mainten_bl.CustomerAddress);
                para[10] = new SqlParameter("@cust_status", mainten_bl.CustomerStatus);
                para[11] = new SqlParameter("@com_date", mainten_bl.CommunicationDate);
                para[12] = new SqlParameter("@go_date", mainten_bl.GoDate);
                para[13] = new SqlParameter("@draw_date", mainten_bl.DrawDate);
                para[14] = new SqlParameter("@delivery_date", mainten_bl.DeliveryDate);
                para[15] = new SqlParameter("@maintenance_entry_date", mainten_bl.MaintenanceEntryDate);
                para[16] = new SqlParameter("@item_category", mainten_bl.ItemCategory);
                para[17] = new SqlParameter("@item", mainten_bl.Item);
                para[18] = new SqlParameter("@item_serial", mainten_bl.ItemSerial);
                para[19] = new SqlParameter("@item_color", mainten_bl.ItemColor);
                para[20] = new SqlParameter("@item_accessories", mainten_bl.ItemAccessories);
                para[21] = new SqlParameter("@damage_category", mainten_bl.DamageCategory);
                para[22] = new SqlParameter("@damage", mainten_bl.Damage);
                para[23] = new SqlParameter("@customer_complaint", mainten_bl.CustomerComplaint);
                para[24] = new SqlParameter("@maintenance_limit", mainten_bl.MaintenanceLimit);
                para[25] = new SqlParameter("@visit_eng_id", mainten_bl.VisitEngineer);
                para[26] = new SqlParameter("@maintenance_eng_id", mainten_bl.MaintenanceEngineer);
                para[27] = new SqlParameter("@workshop_id", mainten_bl.WorkShopID);
                para[28] = new SqlParameter("@is_paid", mainten_bl.IsPaid);
                para[29] = new SqlParameter("@maintenance_status", mainten_bl.MaintenanceStatus);
                para[30] = new SqlParameter("@is_fixed", mainten_bl.IsFixed);
                para[31] = new SqlParameter("@visit_eng_report", mainten_bl.VisitEngineerReport);
                para[32] = new SqlParameter("@maintenance_eng_report", mainten_bl.MaintenanceEngineerReport);
                para[33] = new SqlParameter("@maintenance_report", mainten_bl.MaintenanceReprot);
                para[34] = new SqlParameter("@user_name", mainten_bl.UserName);
                return DAL.InsUpdDel("ManageMaintenance", para);
            }
        }

        public string AddOrUpdateMaintenanceExpenses(MaintenanceBL maint_bl) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[12];
                para[0] = new SqlParameter("@check", "e");
                para[1] = new SqlParameter("@maintenance_id", maint_bl.MaintenanceID);
                para[2] = new SqlParameter("@cost_center_id", maint_bl.CostCenterID);
                para[3] = new SqlParameter("@maintenance_indexation", maint_bl.MaintenanceIndexation);
                para[4] = new SqlParameter("@go_cost", maint_bl.GoCost);
                para[5] = new SqlParameter("@draw_cost", maint_bl.DrawCost);
                para[6] = new SqlParameter("@delivery_cost", maint_bl.DeliveryCost);
                para[7] = new SqlParameter("@other_cost", maint_bl.OtherCosts);
                para[8] = new SqlParameter("@maintenance_eng_ratio", maint_bl.MaintenanceEngineerRatio);
                para[9] = new SqlParameter("@treas_id", maint_bl.TreasuryID);
                para[10] = new SqlParameter("@sub_expense_id", maint_bl.ExpenseID);
                para[11] = new SqlParameter("@revenue_id", maint_bl.RevenueID);
                return DAL.InsUpdDel("ManageMaintenance", para);
            }
        }

        public string DeleteMaintenanceProcess(string MaintenanceID) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check","d");
                para[1] = new SqlParameter("@maintenance_id", MaintenanceID);
                return DAL.InsUpdDel("ManageMaintenance", para);
            }
        }

        public string GetMaxMaintenanceID() 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetValue("GetMaxMaintenanceID");
            }
        }

        public DataTable GetDistinctMaintenanceCustomers() 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetData("GetDistinctMaintenanceCustomers",null);
            }
        }

        public DataTable GetMaintenanceCustomerDetailsByCustomerName(string CustName) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@cust_name", CustName);
                return DAL.GetData("GetMaintenanceCustomerDetailsByCustomerName", para);
            }
        }

        public DataTable GetStockPermissionIDByMaintenanceID(string MaintenanceID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@maintenance_id", MaintenanceID);
                return DAL.GetData("GetStockPermissionIDByMaintenanceID", para);
            }
        }

        public string CheckMaintenanceDeliveryDate(string AlertDate) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@AlertDate", AlertDate);
                return DAL.GetValue("CheckMaintenanceDeliveryDate", para);
            }
        }

        public DataTable RPTMaintenanceDeliveryAlert(string AlertDate) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@AlertDate",AlertDate);
                return DAL.GetData("RPTMaintenanceDeliveryAlert", para);
            }
        }

        public DataTable SearchMaintenance(MaintenanceBL mainte_bl) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[17];
                para[0] = new SqlParameter("@maintenance_id", mainte_bl.MaintenanceID);
                para[1] = new SqlParameter("@cost_center_id", mainte_bl.CostCenterID);
                para[2] = new SqlParameter("@customer_name", mainte_bl.CustomerName);
                para[3] = new SqlParameter("@mobile", mainte_bl.CustomerMobile);
                para[4] = new SqlParameter("@area", mainte_bl.CustomerArea);
                para[5] = new SqlParameter("@item_cat", mainte_bl.ItemCategory);
                para[6] = new SqlParameter("@item", mainte_bl.Item);
                para[7] = new SqlParameter("@visit_eng", mainte_bl.VisitEngineer);
                para[8] = new SqlParameter("@maintenance_eng", mainte_bl.MaintenanceEngineer);
                para[9] = new SqlParameter("@maintenance_workshop", mainte_bl.WorkShopID);
                para[10] = new SqlParameter("@is_fixed", mainte_bl.IsFixed);
                para[11] = new SqlParameter("@is_paid", mainte_bl.IsPaid);
                para[12] = new SqlParameter("@maintenance_status", mainte_bl.MaintenanceStatus);
                para[13] = new SqlParameter("@com_date", mainte_bl.CommunicationDate);
                para[14] = new SqlParameter("@go_date", mainte_bl.GoDate);
                para[15] = new SqlParameter("@draw_date", mainte_bl.DrawDate);
                para[16] = new SqlParameter("@delivery_date", mainte_bl.DeliveryDate);
                return DAL.GetData("SearchMaintenance", para);
            }
        }

        public DataTable GetMaintenanceByMaintenanceID(string MaintenanceID) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@maintenance_id", MaintenanceID);
                return DAL.GetData("GetMaintenanceByMaintenanceID", para);
            }
        }

        public DataTable GetDistinctItemCategoryInMaintenance() 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetData("GetDistinctItemCategoryInMaintenance", null);
            }
        }

        public DataTable GetDistinctItemInMaintenance()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetData("GetDistinctItemInMaintenance", null);
            }
        }

        public DataTable GetDistinctDamageCategory()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetData("GetDistinctDamageCategory", null);
            }
        }
    }
}
