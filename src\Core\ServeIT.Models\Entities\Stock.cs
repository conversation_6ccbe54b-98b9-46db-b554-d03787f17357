using System.ComponentModel.DataAnnotations;

namespace ServeIT.Models.Entities;

public class Stock : BaseEntity<int>
{
    [Required]
    [StringLength(100)]
    public string Name { get; set; } = string.Empty;

    public int? CostCenterId { get; set; }

    [StringLength(200)]
    public string? Location { get; set; }

    [StringLength(100)]
    public string? StockEmployee { get; set; }

    public bool IsDefaultStock { get; set; }

    // Navigation Properties
    public virtual CostCenter? CostCenter { get; set; }
    public virtual ICollection<Product> Products { get; set; } = new List<Product>();
    public virtual ICollection<Invoice> Invoices { get; set; } = new List<Invoice>();
}
