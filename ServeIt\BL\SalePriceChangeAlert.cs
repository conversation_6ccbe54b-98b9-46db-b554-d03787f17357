﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;

namespace ServeIt.BL
{
    class SalePriceChangeAlert
    {
        public string TranID { get; set; }
        public string InvID { get; set; }
        public string ProID { get; set; }
        public decimal ProPrice { get; set; }
        public decimal SellPrice { get; set; }
        public string UserName { get; set; }
        public bool Seen { get; set; }




        public string AddPriceChangeAlert(BL.SalePriceChangeAlert prce_chng_alrt)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[6];
                para[0] = new SqlParameter("@check", "a");
                para[1] = new SqlParameter("@inv_id", prce_chng_alrt.InvID);
                para[2] = new SqlParameter("@pro_id", prce_chng_alrt.ProID);
                para[3] = new SqlParameter("@pro_price", prce_chng_alrt.ProPrice);
                para[4] = new SqlParameter("@sell_price", prce_chng_alrt.SellPrice);
                para[5] = new SqlParameter("@user_name", prce_chng_alrt.UserName);


                return DAL.InsUpdDel("ManageSalePriceChangeAlert", para);
            }
        }

        public string SetALertsSeen(string TranID) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[3];
                para[0] = new SqlParameter("@check", "u");
                para[1] = new SqlParameter("@tran_id", TranID);
                para[2] = new SqlParameter("@seen", true);


                return DAL.InsUpdDel("ManageSalePriceChangeAlert", para);
            }
        }

        public string DeleteAlerts(string TranID) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "d");
                para[1] = new SqlParameter("@tran_id", TranID);

                return DAL.InsUpdDel("ManageSalePriceChangeAlert", para);
            }
        }

        public DataTable GetAllAlers() 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetData("GetAllSalePriceChangeAlerts", null);
            }
        }

        public string CheckForSalePriceChangeAlert() 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetValue("CheckForSalePriceChangeAlert", null);
            }
        }
    }
}
