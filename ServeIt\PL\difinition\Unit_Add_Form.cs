﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace ServeIt.PL.difinition
{
    public partial class Unit_Add_Form : Form
    {

        BE.Unit unitbe;
        BL.Unit unitbl;

        public Unit_Add_Form()
        {
            InitializeComponent();
        }

        private void Unit_Add_Form_Load(object sender, EventArgs e)
        {

            PL.PLUtilities pl_utl = new PL.PLUtilities();
            pl_utl.ALteringNavigationControlWhenChangeLanguage(BtnPrevious, BtnNext);

            if (!label1.Text.Contains("تعديل"))
            {
                NewItemUnit();
            }
            else
            {
                LoadUnitByID(TxtId.Text);
            }
        }

        private void LoadUnitByID(string UnitID)
        {
            unitbl = new BL.Unit();
            DataTable UnitDT = unitbl.GetUnitDetailsByUnitID(UnitID);
            if (UnitDT.Rows.Count > 0)
            {
                TxtUnitName.Text = UnitDT.Rows[0]["unit_name"].ToString();
                LockUnit();
            }
        }

        private void SaveItemUnit()
        {
            if (string.IsNullOrEmpty(TxtUnitName.Text))
            {
                toolStripStatusLabel1.Text = "لم يتم ادخال وحدة المنتج, من فضلك ادخل وحدة المنتج اولا";
                timer1.Start();
            }
            else
            {
                string result;

                unitbe = new BE.Unit();
                unitbl = new BL.Unit();
                if (string.IsNullOrEmpty(TxtId.Text))
                {
                    unitbe.Unit_Id = int.Parse(unitbl.GetMaxUnitId()) + 1;
                }
                else
                {
                    unitbe.Unit_Id = int.Parse(TxtId.Text);
                }
                unitbe.Unit_Name = TxtUnitName.Text;
                unitbe.User_Name = Form1.CheckIns.UserName;
                result = unitbl.AddOrUpdateUnit(unitbe);
                if (result == "success")
                {
                    toolStripStatusLabel1.Text = "تم الحفظ بنجاح";
                    timer1.Start();

                    NewItemUnit();

                    if (Owner.Name == "item_unit")
                    {
                        item_unit.CheckIns.toolStripStatusLabel1.Text = "تم الحفظ بنجاح";
                        item_unit.CheckIns.FillDataGrid();
                        item_unit.CheckIns.timer1.Start();
                    }
                    else if (Owner.Name == "item_new")
                    {
                        BL.Unit UnitBL = new BL.Unit();
                        foreach (Form ProForms in Application.OpenForms)
                        {
                            if (ProForms.Name.Equals("item_new"))
                            {
                                item_new itm_nw = (item_new)ProForms;
                                itm_nw.PopulateUnitsComboBoxes();
                            }
                        }
                    }
                }
                else
                {
                    MessageBox.Show("فشل الحفظ : " + result);
                }
            }
        }
        private void btnSave_Click(object sender, EventArgs e)
        {
            SaveItemUnit();
        }

        void NewItemUnit()
        {
            TxtId.Text = string.Empty;
            TxtUnitName.Text = string.Empty;
            BtnEdit.Enabled = false;
            BtnDelete.Enabled = false;
            label1.Text = "تعريف وحدة قياس : جديد";
            this.Text = "تعريف وحدة قياس : جديد";
            UnlocUnit();
        }
        private void timer1_Tick(object sender, EventArgs e)
        {
            toolStripStatusLabel1.Text = "";
            timer1.Stop();
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void BtnNew_Click(object sender, EventArgs e)
        {
            NewItemUnit();
        }

        private void DeleteItemUnit()
        {
            if (!string.IsNullOrEmpty(TxtId.Text))
            {
                if (MessageBox.Show("هل انت متأكد من الحذف؟", "عملية الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Exclamation) == DialogResult.Yes)
                {
                    unitbe = new BE.Unit();
                    unitbl = new BL.Unit();
                    unitbe.Unit_Id = int.Parse(TxtId.Text);
                    string result = unitbl.DeleteUnit(unitbe);
                    if (result == "success")
                    {
                        if (item_unit.CheckIns != null)
                        {
                            item_unit.CheckIns.FillDataGrid();
                        }
                        NewItemUnit();
                        toolStripStatusLabel1.Text = "تم حذف الوحدة بنجاح";

                        timer1.Start();
                    }
                    else
                    {
                        MessageBox.Show("فشل الحذف : " + result);
                    }
                }
            }
        }

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            using (PL.ALter_Delete_Password_Form.CreateIns)
            {
                if (Properties.Settings.Default.application_dispaly_language == "العربية")
                {
                    PL.ALter_Delete_Password_Form.CheckIns.label1.Text = "كلمة سر الحذف";
                }
                else if (Properties.Settings.Default.application_dispaly_language == "English")
                {
                    PL.ALter_Delete_Password_Form.CheckIns.label1.Text = "Delete Password";
                }
                if (PL.ALter_Delete_Password_Form.CheckIns.ShowDialog() == DialogResult.OK)
                {
                    DeleteItemUnit();
                }
            }
        }

        public void LoadPreviousUnit()
        {
            string CurrentID = string.IsNullOrEmpty(TxtId.Text) ? null : TxtId.Text;
            string PreviousID = string.Empty;

            unitbl = new BL.Unit();

            DataTable IDDT = unitbl.GetPreviousUnitID(CurrentID);
            if (IDDT.Rows.Count > 0)
            {
                label1.Text = "تعريف قسم : تعديل";
                this.Text = "تعريف قسم : تعديل";
                PreviousID = IDDT.Rows[0]["previous_id"].ToString();
                if (!string.IsNullOrEmpty(PreviousID))
                {
                    TxtId.Text = PreviousID;
                    LoadUnitByID(PreviousID);
                    BtnNext.Enabled = true;
                }
                else
                {
                    MessageBox.Show("عفوا, لا يوجد وحدات قياس سابقة");
                    BtnPrevious.Enabled = false;
                }
            }
            BtnNext.Enabled = true;
        }

        public void LoadNextUnit()
        {
            string CurrentID = string.IsNullOrEmpty(TxtId.Text) ? null : TxtId.Text;
            string NextID = string.Empty;

            unitbl = new BL.Unit();

            DataTable IDDT = unitbl.GetNextUnitID(CurrentID);
            if (IDDT.Rows.Count > 0)
            {
                label1.Text = "تعريف وحدة قياس : تعديل";
                this.Text = "تعريف وحدة قياس : تعديل";

                NextID = IDDT.Rows[0]["next_id"].ToString();
                if (!string.IsNullOrEmpty(NextID))
                {
                    TxtId.Text = NextID;
                    LoadUnitByID(NextID);

                    BtnPrevious.Enabled = true;
                }
                else
                {
                    MessageBox.Show("عفوا, لا يوجد وحدات قياس تالية");
                    BtnNext.Enabled = false;
                }
            }
        }

        private void UnlocUnit()
        {
            Pnl.Enabled = true;
            BtnSave.Enabled = true;
            BtnEdit.Enabled = false;
            BtnNext.Enabled = true;
        }

        private void LockUnit()
        {
            Pnl.Enabled = false;
            BtnSave.Enabled = false;
            BtnEdit.Enabled = true;
            //BtnNext.Enabled = false;
            BtnDelete.Enabled = true;
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            using (PL.ALter_Delete_Password_Form.CreateIns)
            {
                if (Properties.Settings.Default.application_dispaly_language == "العربية")
                {
                    PL.ALter_Delete_Password_Form.CheckIns.label1.Text = "كلمة سر التعديل";
                }
                else if (Properties.Settings.Default.application_dispaly_language == "English")
                {
                    PL.ALter_Delete_Password_Form.CheckIns.label1.Text = "Alter Password";
                }
                if (PL.ALter_Delete_Password_Form.CheckIns.ShowDialog() == DialogResult.OK)
                {
                    UnlocUnit();
                }
            }
        }

        private void BtnOrdersArchive_Click(object sender, EventArgs e)
        {
            if (item_unit.CheckIns == null)
            {
                item_unit.CreateIns.Show();
            }
            else
            {
                item_unit.CheckIns.Focus();
            }
        }

        private void BtnPrevious_Click(object sender, EventArgs e)
        {
            LoadPreviousUnit();
        }

        private void BtnNext_Click(object sender, EventArgs e)
        {
            LoadNextUnit();
        }

        protected override bool ProcessCmdKey(ref Message msg, Keys keyData)
        {
            switch (keyData)
            {
                case Keys.F10:
                    if (BtnSave.Enabled)
                    {
                        SaveItemUnit();
                    }
                    break;
                case Keys.F1:
                    if (BtnNew.Enabled)
                    {
                        NewItemUnit();
                    }

                    break;
                case Keys.F3:
                    if (BtnDelete.Enabled)
                    {
                        DeleteItemUnit();
                    }

                    break;
                case Keys.Escape:
                    this.Close();
                    break;
                default:
                    break;
            }
            return base.ProcessCmdKey(ref msg, keyData);
        }
    }
}
