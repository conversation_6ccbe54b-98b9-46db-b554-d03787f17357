﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ServeIt.BE
{
    class TreasuryPermissionBE
    {
        public string PerId { get; set; }
        public int? PerNo { get; set; }
        public int PerKind { get; set; }
        public DateTime Per_Date { get; set; }
        public DateTime? DateFrom { get; set; }
        public DateTime? DateTo { get; set; }
        public int? CustId { get; set; }
        public string CustName { get; set; }
        public int? TreasID { get; set; }
        public int? TreasForm { get; set; }
        public int? TreasTo { get; set; }
        public bool IsDiscount { get; set; }
        public decimal Value { get; set; }
        public string Notes { get; set; }
        public string User_Name { get; set; }
    }
}
