using ServeIT.Models.DTOs;
using ServeIT.Models.Common;

namespace ServeIT.BlazorServer.Services;

public interface IInvoiceApiService
{
    Task<ServiceResult<IEnumerable<InvoiceDto>>> GetAllInvoicesAsync();
    Task<ServiceResult<InvoiceDto>> GetInvoiceByIdAsync(string id);
    Task<ServiceResult<PagedResult<InvoiceDto>>> GetInvoicesPagedAsync(PagingParameters parameters);
    Task<ServiceResult<InvoiceDto>> CreateInvoiceAsync(InvoiceDto invoice);
    Task<ServiceResult<InvoiceDto>> UpdateInvoiceAsync(string id, InvoiceDto invoice);
    Task<ServiceResult> DeleteInvoiceAsync(string id);
}
