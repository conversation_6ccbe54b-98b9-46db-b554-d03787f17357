using Microsoft.AspNetCore.Mvc;
using ServeIT.API.Services;
using ServeIT.Models.DTOs;
using ServeIT.Models.Common;

namespace ServeIT.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class InvoicesController : ControllerBase
{
    private readonly IInvoiceService _invoiceService;

    public InvoicesController(IInvoiceService invoiceService)
    {
        _invoiceService = invoiceService;
    }

    [HttpGet]
    public async Task<ActionResult<ServiceResult<IEnumerable<InvoiceDto>>>> GetAllInvoices()
    {
        var result = await _invoiceService.GetAllInvoicesAsync();
        return Ok(result);
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<ServiceResult<InvoiceDto>>> GetInvoice(string id)
    {
        var result = await _invoiceService.GetInvoiceByIdAsync(id);
        
        if (!result.IsSuccess)
        {
            return NotFound(result);
        }

        return Ok(result);
    }

    [HttpGet("paged")]
    public async Task<ActionResult<ServiceResult<PagedResult<InvoiceDto>>>> GetInvoicesPaged([FromQuery] PagingParameters parameters)
    {
        var result = await _invoiceService.GetInvoicesPagedAsync(parameters);
        return Ok(result);
    }

    [HttpPost]
    public async Task<ActionResult<ServiceResult<InvoiceDto>>> CreateInvoice([FromBody] InvoiceDto invoiceDto)
    {
        if (!ModelState.IsValid)
        {
            var errors = ModelState.Values
                .SelectMany(v => v.Errors)
                .Select(e => e.ErrorMessage)
                .ToList();
            
            return BadRequest(ServiceResult<InvoiceDto>.Failure("Validation failed", errors));
        }

        var result = await _invoiceService.CreateInvoiceAsync(invoiceDto);
        
        if (!result.IsSuccess)
        {
            return BadRequest(result);
        }

        return CreatedAtAction(nameof(GetInvoice), new { id = result.Data!.Id }, result);
    }

    [HttpPut("{id}")]
    public async Task<ActionResult<ServiceResult<InvoiceDto>>> UpdateInvoice(string id, [FromBody] InvoiceDto invoiceDto)
    {
        if (!ModelState.IsValid)
        {
            var errors = ModelState.Values
                .SelectMany(v => v.Errors)
                .Select(e => e.ErrorMessage)
                .ToList();
            
            return BadRequest(ServiceResult<InvoiceDto>.Failure("Validation failed", errors));
        }

        var result = await _invoiceService.UpdateInvoiceAsync(id, invoiceDto);
        
        if (!result.IsSuccess)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    [HttpDelete("{id}")]
    public async Task<ActionResult<ServiceResult>> DeleteInvoice(string id)
    {
        var result = await _invoiceService.DeleteInvoiceAsync(id);
        
        if (!result.IsSuccess)
        {
            return NotFound(result);
        }

        return Ok(result);
    }
}
