using Microsoft.EntityFrameworkCore;
using ServeIT.Database.Context;
using ServeIT.Database.Repositories.Interfaces;
using ServeIT.Models.Entities;
using ServeIT.Models.Common;

namespace ServeIT.Database.Repositories;

public class ProductRepository : Repository<Product, string>, IProductRepository
{
    public ProductRepository(ServeITDbContext context) : base(context)
    {
    }

    public async Task<IEnumerable<Product>> GetActiveProductsAsync()
    {
        return await _dbSet
            .Where(p => p.IsActive)
            .Include(p => p.Category)
            .Include(p => p.DefaultStock)
            .Include(p => p.ProductUnits)
                .ThenInclude(pu => pu.Unit)
            .OrderBy(p => p.Name)
            .ToListAsync();
    }

    public async Task<Product?> GetByBarcodeAsync(string barcode)
    {
        if (string.IsNullOrWhiteSpace(barcode))
            return null;

        // Search in both international and system barcodes
        var product = await _dbSet
            .Include(p => p.Category)
            .Include(p => p.DefaultStock)
            .Include(p => p.ProductUnits)
                .ThenInclude(pu => pu.Unit)
            .FirstOrDefaultAsync(p => p.IsActive && 
                (p.InternationalBarcode == barcode || p.SystemBarcode == barcode));

        if (product != null)
            return product;

        // Also search in ProductUnit barcodes (from legacy system analysis)
        return await _dbSet
            .Include(p => p.Category)
            .Include(p => p.DefaultStock)
            .Include(p => p.ProductUnits)
                .ThenInclude(pu => pu.Unit)
            .FirstOrDefaultAsync(p => p.IsActive && 
                p.ProductUnits.Any(pu => 
                    pu.InternationalBarcode == barcode || pu.SystemBarcode == barcode));
    }

    public async Task<IEnumerable<Product>> GetByCategoryAsync(int categoryId)
    {
        return await _dbSet
            .Where(p => p.IsActive && p.CategoryId == categoryId)
            .Include(p => p.Category)
            .Include(p => p.DefaultStock)
            .Include(p => p.ProductUnits)
                .ThenInclude(pu => pu.Unit)
            .OrderBy(p => p.Name)
            .ToListAsync();
    }

    public async Task<PagedResult<Product>> SearchProductsAsync(string searchTerm, PagingParameters parameters)
    {
        var query = _dbSet
            .Include(p => p.Category)
            .Include(p => p.DefaultStock)
            .Include(p => p.ProductUnits)
                .ThenInclude(pu => pu.Unit)
            .Where(p => p.IsActive);

        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            searchTerm = searchTerm.ToLower();
            query = query.Where(p => 
                p.Name.ToLower().Contains(searchTerm) ||
                (p.ProductId2 != null && p.ProductId2.ToLower().Contains(searchTerm)) ||
                (p.ProductNumber != null && p.ProductNumber.ToLower().Contains(searchTerm)) ||
                (p.Serial != null && p.Serial.ToLower().Contains(searchTerm)) ||
                (p.InternationalBarcode != null && p.InternationalBarcode.Contains(searchTerm)) ||
                (p.SystemBarcode != null && p.SystemBarcode.Contains(searchTerm)) ||
                (p.Category != null && p.Category.Name.ToLower().Contains(searchTerm)));
        }

        var totalCount = await query.CountAsync();

        var items = await query
            .OrderBy(p => p.Name)
            .Skip((parameters.PageNumber - 1) * parameters.PageSize)
            .Take(parameters.PageSize)
            .ToListAsync();

        return new PagedResult<Product>(items, totalCount, parameters.PageNumber, parameters.PageSize);
    }

    public async Task<bool> IsBarcodeUniqueAsync(string barcode, string? excludeProductId = null)
    {
        if (string.IsNullOrWhiteSpace(barcode))
            return true;

        var query = _dbSet.Where(p => p.IsActive && 
            (p.InternationalBarcode == barcode || p.SystemBarcode == barcode));
        
        if (!string.IsNullOrEmpty(excludeProductId))
        {
            query = query.Where(p => p.Id != excludeProductId);
        }

        var productExists = await query.AnyAsync();
        
        if (productExists)
            return false;

        // Also check ProductUnit barcodes
        var productUnitQuery = _context.Set<ProductUnit>()
            .Where(pu => pu.Product.IsActive && 
                (pu.InternationalBarcode == barcode || pu.SystemBarcode == barcode));

        if (!string.IsNullOrEmpty(excludeProductId))
        {
            productUnitQuery = productUnitQuery.Where(pu => pu.ProductId != excludeProductId);
        }

        return !await productUnitQuery.AnyAsync();
    }

    public async Task<IEnumerable<Product>> GetProductsUnderRequestLimitAsync()
    {
        return await _dbSet
            .Where(p => p.IsActive && p.RequestLimit > 0)
            .Include(p => p.Category)
            .Include(p => p.DefaultStock)
            .Include(p => p.ProductUnits)
                .ThenInclude(pu => pu.Unit)
            .OrderBy(p => p.Name)
            .ToListAsync();
    }

    public async Task<IEnumerable<Product>> GetProductsWithUnitsAsync()
    {
        return await _dbSet
            .Where(p => p.IsActive)
            .Include(p => p.Category)
            .Include(p => p.DefaultStock)
            .Include(p => p.ProductUnits)
                .ThenInclude(pu => pu.Unit)
            .Where(p => p.ProductUnits.Any())
            .OrderBy(p => p.Name)
            .ToListAsync();
    }

    public async Task<Product?> GetWithUnitsAsync(string productId)
    {
        return await _dbSet
            .Include(p => p.Category)
            .Include(p => p.DefaultStock)
            .Include(p => p.ProductUnits)
                .ThenInclude(pu => pu.Unit)
            .FirstOrDefaultAsync(p => p.Id == productId && p.IsActive);
    }

    public override async Task<PagedResult<Product>> GetPagedAsync(PagingParameters parameters)
    {
        var query = _dbSet
            .Include(p => p.Category)
            .Include(p => p.DefaultStock)
            .Include(p => p.ProductUnits)
                .ThenInclude(pu => pu.Unit)
            .Where(p => p.IsActive);

        var totalCount = await query.CountAsync();

        var items = await query
            .OrderBy(p => p.Name)
            .Skip((parameters.PageNumber - 1) * parameters.PageSize)
            .Take(parameters.PageSize)
            .ToListAsync();

        return new PagedResult<Product>(items, totalCount, parameters.PageNumber, parameters.PageSize);
    }

    public override async Task<Product?> GetByIdAsync(string id)
    {
        return await _dbSet
            .Include(p => p.Category)
            .Include(p => p.DefaultStock)
            .Include(p => p.ProductUnits)
                .ThenInclude(pu => pu.Unit)
            .FirstOrDefaultAsync(p => p.Id == id && p.IsActive);
    }

    public override async Task<IEnumerable<Product>> GetAllAsync()
    {
        return await _dbSet
            .Include(p => p.Category)
            .Include(p => p.DefaultStock)
            .Include(p => p.ProductUnits)
                .ThenInclude(pu => pu.Unit)
            .Where(p => p.IsActive)
            .OrderBy(p => p.Name)
            .ToListAsync();
    }
}
