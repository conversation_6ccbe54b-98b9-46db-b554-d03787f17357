﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;

namespace ServeIt.BL
{
    class AttendanceDepartureBL
    {
        public string AttendanceDepartureID { get; set; }
        public string ManagementID { get; set; }
        public string WorkPeriodID { get; set; }
        public string EmployeeID { get; set; }
        public string EmployeeName { get; set; }
        public DateTime Day { get; set; }
        public TimeSpan Time { get; set; }
        public DateTime? DateFrom { get; set; }
        public DateTime? DateTo { get; set; }
        public string CheckState { get; set; }

        public string AddOrUpdateEmployeeAttendance(AttendanceDepartureBL atend_dep_bl)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[6];
                para[0] = new SqlParameter("@check","a");
                para[1] = new SqlParameter("@attendance_departure_id",atend_dep_bl.AttendanceDepartureID);
                para[2] = new SqlParameter("@employee_id",atend_dep_bl.EmployeeID);
                para[3] = new SqlParameter("@day",atend_dep_bl.Day);
                para[4] = new SqlParameter("@time",atend_dep_bl.Time);
                para[5] = new SqlParameter("@check_state_id", atend_dep_bl.CheckState);
                return DAL.InsUpdDel("ManageEmployeeAttendanceDeparture", para);
            }
        }

        public string GetMaxAttendanceDeparture()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@check", "m");
                
                return DAL.GetValue("ManageEmployeeAttendanceDeparture", para);
            }
        }

        public string DeleteAttendanceDeparture(string AttendanceDepartureID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "d");
                para[1] = new SqlParameter("@attendance_departure_id", AttendanceDepartureID);

                return DAL.InsUpdDel("ManageEmployeeAttendanceDeparture", para);
            }
        }

        public DataTable GetAttendanceDepartureByID(string AttendanceDepartureID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "g");
                para[1] = new SqlParameter("@attendance_departure_id", AttendanceDepartureID);

                return DAL.GetData("ManageEmployeeAttendanceDeparture", para);
            }
        }

        public DataTable SearchAttendanceDeparture(AttendanceDepartureBL atend_dep_bl)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[7];
                para[0] = new SqlParameter("@check", "s");
                para[1] = new SqlParameter("@management_id", atend_dep_bl.ManagementID);
                para[2] = new SqlParameter("@work_period_id", atend_dep_bl.WorkPeriodID);
                para[3] = new SqlParameter("@employee_name", atend_dep_bl.EmployeeName);
                para[4] = new SqlParameter("@date_from", atend_dep_bl.DateFrom);
                para[5] = new SqlParameter("@date_to", atend_dep_bl.DateTo);
                para[6] = new SqlParameter("@check_state_id", atend_dep_bl.CheckState);
                return DAL.GetData("ManageEmployeeAttendanceDeparture", para);
            }
        }
    }
}
