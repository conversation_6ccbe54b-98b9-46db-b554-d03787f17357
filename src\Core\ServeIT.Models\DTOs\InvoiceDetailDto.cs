using System.ComponentModel.DataAnnotations;

namespace ServeIT.Models.DTOs;

public class InvoiceDetailDto
{
    public int Id { get; set; }

    [Required]
    public string InvoiceId { get; set; } = string.Empty;

    public int? CategoryId { get; set; }
    public string? CategoryName { get; set; }

    [Required]
    public string ProductId { get; set; } = string.Empty;
    public string? ProductName { get; set; }

    [StringLength(100)]
    public string? ProductSerial { get; set; }

    public DateTime? ProductExpiry { get; set; }

    [Required]
    public int UnitId { get; set; }
    public string? UnitName { get; set; }

    [Range(0.01, double.MaxValue, ErrorMessage = "Price must be greater than 0")]
    public decimal Price { get; set; }

    [Range(0, double.MaxValue)]
    public decimal CostPrice { get; set; }

    [Range(0.01, double.MaxValue, ErrorMessage = "Quantity must be greater than 0")]
    public decimal Quantity { get; set; }

    [Range(0, double.MaxValue)]
    public decimal QuantityOut { get; set; }

    [Range(0, 100)]
    public decimal ProductDiscount { get; set; }

    [Range(0, 100)]
    public decimal VAT { get; set; }

    [StringLength(500)]
    public string? ProductNotes { get; set; }

    [Range(0, double.MaxValue)]
    public decimal Total { get; set; }
}
