using System.ComponentModel.DataAnnotations;

namespace ServeIT.Models.Entities;

public class Expense : BaseEntity<int>
{
    public int? ParentId { get; set; }

    [Required]
    [StringLength(100)]
    public string Name { get; set; } = string.Empty;

    [StringLength(1000)]
    public string? Notes { get; set; }

    // Navigation Properties
    public virtual Expense? Parent { get; set; }
    public virtual ICollection<Expense> Children { get; set; } = new List<Expense>();
    public virtual ICollection<TreasuryPermission> TreasuryPermissions { get; set; } = new List<TreasuryPermission>();
}
