﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;

namespace ServeIt.BL
{
    class KitchenBL
    {
        public string KitchenID { get; set; }
        public string KitchenName { get; set; }
        public string PrinterName { get; set; }

        public string AddOrUpdateKitchen(KitchenBL kchn_bl)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[4];
                para[0] = new SqlParameter("@check", "a");
                para[1] = new SqlParameter("@kitchen_id", kchn_bl.KitchenID);
                para[2] = new SqlParameter("@kitchen_name", kchn_bl.KitchenName);
                para[3] = new SqlParameter("@printer_name", kchn_bl.PrinterName);

                return DAL.InsUpdDel("ManageKitchen", para);
            }
        }

        public string DeleteKitchen(string KitchenID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "d");
                para[1] = new SqlParameter("@kitchen_id", KitchenID);


                return DAL.InsUpdDel("ManageKitchen", para);
            }
        }

        public string GetMaxKitchenID()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@check", "m");


                return DAL.GetValue("ManageKitchen", para);
            }
        }

        public DataTable GetKitchenDetailsByID(string KitchenID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "g");
                para[1] = new SqlParameter("@kitchen_id", KitchenID);


                return DAL.GetData("ManageKitchen", para);
            }
        }

        public DataTable GetAllKitchens()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@check", "s");

                return DAL.GetData("ManageKitchen", para);
            }
        }

        public DataTable GetKitchenPrinterByKitchenID(string KitchenID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@check", "s");

                return DAL.GetData("GetKitchenPrinterByKitchenID", para);
            }
        }
    }
}
