using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using ServeIT.Models.DTOs;
using ServeIT.Models.Common;

namespace ServeIT.BlazorServer.Services;

public class CustomerApiService : ICustomerApiService
{
    private readonly HttpClient _httpClient;
    private readonly JsonSerializerOptions _jsonOptions;

    public CustomerApiService(IHttpClientFactory httpClientFactory)
    {
        _httpClient = httpClientFactory.CreateClient("ServeITAPI");
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        };
    }

    public async Task<ServiceResult<IEnumerable<CustomerDto>>> GetAllCustomersAsync()
    {
        try
        {
            var response = await _httpClient.GetAsync("api/customers");
            var content = await response.Content.ReadAsStringAsync();
            
            if (response.IsSuccessStatusCode)
            {
                var result = JsonSerializer.Deserialize<ServiceResult<IEnumerable<CustomerDto>>>(content, _jsonOptions);
                return result ?? ServiceResult<IEnumerable<CustomerDto>>.Failure("Failed to deserialize response");
            }
            
            return ServiceResult<IEnumerable<CustomerDto>>.Failure($"API call failed: {response.StatusCode}");
        }
        catch (Exception ex)
        {
            return ServiceResult<IEnumerable<CustomerDto>>.Failure("Network error", ex.Message);
        }
    }

    public async Task<ServiceResult<CustomerDto>> GetCustomerByIdAsync(int id)
    {
        try
        {
            var response = await _httpClient.GetAsync($"api/customers/{id}");
            var content = await response.Content.ReadAsStringAsync();
            
            if (response.IsSuccessStatusCode)
            {
                var result = JsonSerializer.Deserialize<ServiceResult<CustomerDto>>(content, _jsonOptions);
                return result ?? ServiceResult<CustomerDto>.Failure("Failed to deserialize response");
            }
            
            return ServiceResult<CustomerDto>.Failure($"API call failed: {response.StatusCode}");
        }
        catch (Exception ex)
        {
            return ServiceResult<CustomerDto>.Failure("Network error", ex.Message);
        }
    }

    public async Task<ServiceResult<PagedResult<CustomerDto>>> GetCustomersPagedAsync(PagingParameters parameters)
    {
        try
        {
            var queryString = $"?PageNumber={parameters.PageNumber}&PageSize={parameters.PageSize}";
            if (!string.IsNullOrEmpty(parameters.SearchTerm))
                queryString += $"&SearchTerm={Uri.EscapeDataString(parameters.SearchTerm)}";
            if (!string.IsNullOrEmpty(parameters.SortBy))
                queryString += $"&SortBy={Uri.EscapeDataString(parameters.SortBy)}";
            if (parameters.SortDescending)
                queryString += "&SortDescending=true";

            var response = await _httpClient.GetAsync($"api/customers/paged{queryString}");
            var content = await response.Content.ReadAsStringAsync();
            
            if (response.IsSuccessStatusCode)
            {
                var result = JsonSerializer.Deserialize<ServiceResult<PagedResult<CustomerDto>>>(content, _jsonOptions);
                return result ?? ServiceResult<PagedResult<CustomerDto>>.Failure("Failed to deserialize response");
            }
            
            return ServiceResult<PagedResult<CustomerDto>>.Failure($"API call failed: {response.StatusCode}");
        }
        catch (Exception ex)
        {
            return ServiceResult<PagedResult<CustomerDto>>.Failure("Network error", ex.Message);
        }
    }

    public async Task<ServiceResult<PagedResult<CustomerDto>>> SearchCustomersAsync(string searchTerm, PagingParameters parameters)
    {
        try
        {
            var queryString = $"?searchTerm={Uri.EscapeDataString(searchTerm)}&PageNumber={parameters.PageNumber}&PageSize={parameters.PageSize}";
            
            var response = await _httpClient.GetAsync($"api/customers/search{queryString}");
            var content = await response.Content.ReadAsStringAsync();
            
            if (response.IsSuccessStatusCode)
            {
                var result = JsonSerializer.Deserialize<ServiceResult<PagedResult<CustomerDto>>>(content, _jsonOptions);
                return result ?? ServiceResult<PagedResult<CustomerDto>>.Failure("Failed to deserialize response");
            }
            
            return ServiceResult<PagedResult<CustomerDto>>.Failure($"API call failed: {response.StatusCode}");
        }
        catch (Exception ex)
        {
            return ServiceResult<PagedResult<CustomerDto>>.Failure("Network error", ex.Message);
        }
    }

    public async Task<ServiceResult<CustomerDto>> CreateCustomerAsync(CustomerDto customer)
    {
        try
        {
            var json = JsonSerializer.Serialize(customer, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PostAsync("api/customers", content);
            var responseContent = await response.Content.ReadAsStringAsync();
            
            if (response.IsSuccessStatusCode)
            {
                var result = JsonSerializer.Deserialize<ServiceResult<CustomerDto>>(responseContent, _jsonOptions);
                return result ?? ServiceResult<CustomerDto>.Failure("Failed to deserialize response");
            }
            
            return ServiceResult<CustomerDto>.Failure($"API call failed: {response.StatusCode}");
        }
        catch (Exception ex)
        {
            return ServiceResult<CustomerDto>.Failure("Network error", ex.Message);
        }
    }

    public async Task<ServiceResult<CustomerDto>> UpdateCustomerAsync(int id, CustomerDto customer)
    {
        try
        {
            var json = JsonSerializer.Serialize(customer, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PutAsync($"api/customers/{id}", content);
            var responseContent = await response.Content.ReadAsStringAsync();
            
            if (response.IsSuccessStatusCode)
            {
                var result = JsonSerializer.Deserialize<ServiceResult<CustomerDto>>(responseContent, _jsonOptions);
                return result ?? ServiceResult<CustomerDto>.Failure("Failed to deserialize response");
            }
            
            return ServiceResult<CustomerDto>.Failure($"API call failed: {response.StatusCode}");
        }
        catch (Exception ex)
        {
            return ServiceResult<CustomerDto>.Failure("Network error", ex.Message);
        }
    }

    public async Task<ServiceResult> DeleteCustomerAsync(int id)
    {
        try
        {
            var response = await _httpClient.DeleteAsync($"api/customers/{id}");
            var content = await response.Content.ReadAsStringAsync();
            
            if (response.IsSuccessStatusCode)
            {
                var result = JsonSerializer.Deserialize<ServiceResult>(content, _jsonOptions);
                return result ?? ServiceResult.Failure("Failed to deserialize response");
            }
            
            return ServiceResult.Failure($"API call failed: {response.StatusCode}");
        }
        catch (Exception ex)
        {
            return ServiceResult.Failure("Network error", ex.Message);
        }
    }

    public async Task<ServiceResult<bool>> CheckMobileUniqueAsync(string mobile, int? excludeId = null)
    {
        try
        {
            var queryString = excludeId.HasValue ? $"?excludeId={excludeId}" : "";
            var response = await _httpClient.GetAsync($"api/customers/check-mobile/{Uri.EscapeDataString(mobile)}{queryString}");
            var content = await response.Content.ReadAsStringAsync();
            
            if (response.IsSuccessStatusCode)
            {
                var result = JsonSerializer.Deserialize<ServiceResult<bool>>(content, _jsonOptions);
                return result ?? ServiceResult<bool>.Failure("Failed to deserialize response");
            }
            
            return ServiceResult<bool>.Failure($"API call failed: {response.StatusCode}");
        }
        catch (Exception ex)
        {
            return ServiceResult<bool>.Failure("Network error", ex.Message);
        }
    }

    public async Task<ServiceResult<IEnumerable<CustomerDto>>> GetActiveCustomersAsync()
    {
        try
        {
            var response = await _httpClient.GetAsync("api/customers/active");
            var content = await response.Content.ReadAsStringAsync();
            
            if (response.IsSuccessStatusCode)
            {
                var result = JsonSerializer.Deserialize<ServiceResult<IEnumerable<CustomerDto>>>(content, _jsonOptions);
                return result ?? ServiceResult<IEnumerable<CustomerDto>>.Failure("Failed to deserialize response");
            }
            
            return ServiceResult<IEnumerable<CustomerDto>>.Failure($"API call failed: {response.StatusCode}");
        }
        catch (Exception ex)
        {
            return ServiceResult<IEnumerable<CustomerDto>>.Failure("Network error", ex.Message);
        }
    }
}
