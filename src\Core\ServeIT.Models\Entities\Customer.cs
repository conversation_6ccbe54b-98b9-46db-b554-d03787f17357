using System.ComponentModel.DataAnnotations;

namespace ServeIT.Models.Entities;

public class Customer : BaseEntity<int>
{
    [Required]
    [StringLength(100)]
    public string Name { get; set; } = string.Empty;

    [StringLength(50)]
    public string? CustomerKind { get; set; }

    public decimal InitialBalance { get; set; }

    [StringLength(50)]
    public string? PriceSystem { get; set; }

    public byte[]? Picture { get; set; }

    [StringLength(100)]
    public string? City { get; set; }

    [StringLength(100)]
    public string? Area { get; set; }

    [StringLength(500)]
    public string? Address { get; set; }

    [StringLength(20)]
    public string? Mobile { get; set; }

    [StringLength(20)]
    public string? Phone { get; set; }

    public int? SalesRepresentativeId { get; set; }

    public bool IsDefaultCustomer { get; set; }

    [StringLength(1000)]
    public string? Notes { get; set; }

    [StringLength(100)]
    public string? Box1Value { get; set; }

    [StringLength(100)]
    public string? Box2Value { get; set; }

    // Navigation Properties
    public virtual SalesRepresentative? SalesRepresentative { get; set; }
    public virtual ICollection<Invoice> Invoices { get; set; } = new List<Invoice>();
    public virtual ICollection<TreasuryPermission> TreasuryPermissions { get; set; } = new List<TreasuryPermission>();
}
