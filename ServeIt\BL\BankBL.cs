﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ServeIt.BL
{
    class BankBL
    {

        public string AddOrUpdateNewBank(BE.BankBE bankbe) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[3];
                para[0] = new SqlParameter("@check", "a");
                para[1] = new SqlParameter("@id", bankbe.Id);
                para[2] = new SqlParameter("@name", bankbe.Name);
                return DAL.InsUpdDel("managebank", para);
            }
        }


        public string DeleteBank(BE.BankBE bankbe)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "d");
                para[1] = new SqlParameter("@id", bankbe.Id);
                return DAL.InsUpdDel("managebank", para);
            }
        }


        public string GetMaxBankId()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetValue("getmaxbankid");
            }
        }

        public DataTable GetAllBanks() 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetData("getallbanks", null);
            }
        }

    }
}
