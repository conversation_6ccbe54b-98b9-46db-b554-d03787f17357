using System.ComponentModel.DataAnnotations;
using ServeIT.Models.Entities;

namespace ServeIT.Models.DTOs;

public class InvoiceDto
{
    [Required]
    public string Id { get; set; } = string.Empty;

    [StringLength(50)]
    public string? Number { get; set; }

    public int? CostCenterId { get; set; }
    public string? CostCenterName { get; set; }

    [Required]
    public DateTime Date { get; set; }

    public DateTime TransactionDate { get; set; }

    public int? StockId { get; set; }
    public string? StockName { get; set; }

    [Required]
    public int CustomerId { get; set; }
    public string? CustomerName { get; set; }

    public InvoiceKind Kind { get; set; }
    public PaymentKind PaymentKind { get; set; }
    public OrderType? OrderType { get; set; }
    public OrderStatus? OrderStatus { get; set; }

    public int? TreasuryId { get; set; }
    public string? TreasuryName { get; set; }

    public int? SalesRepresentativeId { get; set; }
    public string? SalesRepresentativeName { get; set; }

    [Range(0, double.MaxValue)]
    public decimal Paid { get; set; }

    [Range(0, double.MaxValue)]
    public decimal CustomerRecipient { get; set; }

    [Range(0, double.MaxValue)]
    public decimal Discount { get; set; }

    [Range(0, double.MaxValue)]
    public decimal SalesTax { get; set; }

    [Range(0, double.MaxValue)]
    public decimal Addition { get; set; }

    [Range(0, double.MaxValue)]
    public decimal Service { get; set; }

    [Range(0, double.MaxValue)]
    public decimal NetTotal { get; set; }

    [StringLength(1000)]
    public string? Notes { get; set; }

    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; }
    public string? CreatedBy { get; set; }

    public List<InvoiceDetailDto> InvoiceDetails { get; set; } = new();
}
