﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;

namespace ServeIt.BL
{
    class HallBL
    {
        public string HallID { get; set; }
        public string HallName { get; set; }
        public string Notes { get; set; }

        public string AddOrUpdateHall(HallBL hall_bl)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[4];
                para[0] = new SqlParameter("@check", "a");
                para[1] = new SqlParameter("@hall_id", hall_bl.HallID);
                para[2] = new SqlParameter("@hall_name", hall_bl.HallName);
                para[3] = new SqlParameter("@notes", hall_bl.Notes);
                return DAL.InsUpdDel("ManageHalls", para);
            }
        }

        public string GetMaxHallID()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@check", "m");
                return DAL.GetValue("ManageHalls", para);
            }
        }

        public DataTable GetHallByID(string HallID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "g");
                para[1] = new SqlParameter("@hall_id", HallID);
                return DAL.GetData("ManageHalls", para);
            }
        }

        public string DeleteHall(string HallID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "d");
                para[1] = new SqlParameter("@hall_id", HallID);
                return DAL.InsUpdDel("ManageHalls", para);
            }
        }

        public DataTable SearchHall(string HallName)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "s");
                para[1] = new SqlParameter("@hall_name", HallName);
                return DAL.GetData("ManageHalls", para);
            }
        }

        public DataTable GetPreviousHall(string HallID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "p");
                para[1] = new SqlParameter("@hall_id", HallID);
                return DAL.GetData("ManageHalls", para);
            }
        }

        public DataTable GetNextHall(string HallID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "n");
                para[1] = new SqlParameter("@hall_id", HallID);
                return DAL.GetData("ManageHalls", para);
            }
        }

    }
}
