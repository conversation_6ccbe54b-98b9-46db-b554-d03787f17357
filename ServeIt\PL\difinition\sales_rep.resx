﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="BtnNew.ImageTransparentColor" type="System.Drawing.Color, System.Drawing">
    <value>Magenta</value>
  </data>
  <data name="label1.Text" xml:space="preserve">
    <value>مناديب المبيعات</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>934, 561</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAEBAAAAEAIABoBAAAFgAAACgAAAAQAAAAIAAAAAEAIAAAAAAAAAQAACMuAAAjLgAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAB1+bUIh/nFgJH50eyM+dXVe/nIMgAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAH/5vABq9qxIbvSs227zr/9q87T/ZvO8/2L1we5T9LQzAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAGP2px1e8p2sWe+W/1Ptkv9N65H/R+uS/0HrlP8865X/Oe6d7jLw
        nDMAAAAAAAAAAAAAAAAAAAAAafetBVrynHRU75LzTOuI/0Tpg/8953//OOeA+UfngqB9012MS+B0kB3m
        fe4a53/uGeuHMwAAAAAAAAAAVvOeNVLvk9NL64j/Q+iB/zvme/8z5Xf/NOd9wa+0LozbjALq2IQA/9uO
        BPmExj96B95k/gjha+4N6HozAAAAAEjujt5B6ID/OeZ6/zLkdf8u5XjnbtJbityPA8TWgAD/1X0A/9V9
        AP/XggH/xq8cbQDbW/oA2FX/Bd5j7g3ndDM57YZ6MOV2+yrkcvk64nGhzZwSndeCAfrUegD/1HoA/9R6
        AP/YhQLzsKsgjgLbWcEA1U//ANVQ/wDWUf8H32TbAAAAAC3sghDKnxdh2IEB49J1AP/SdAD/0nMA/9N3
        AP/YjQi5NdFMkAHWUPMA0kr/ANJK/wDSSv8B1U7+CuBjjN6mAgDZggKl0GwA/85nAP/OZwD/zmgA/9R3
        AuuWqySKA9VMyQDPQ/8AzkL/AM5C/wDOQ/8E1k3ZDN9gRQDqcwDenQAF0GkA8cdTAP/HUwD/yFUA/813
        B7IiyzmZAMs5+gDHNf8AxzX/AMc1/wHMOvYpz0WU1KEsWemmKycAAAAAAAAAANNvAEfERgD3vjkA/8hR
        A+cHzS2IAL8l/wC+JP8AviT/AMEm/wnLMLqskSGX2W4X89lqG//geyD76aEpRQAAAAAAAAAAzFoASLwv
        APe/NAL2NLMZiAC7Ff4AuBX/AcAa4WCnHY/UXg3T0VIN/9VXD//aYBL/4HAX/+qjMV8AAAAAAAAAAAAA
        AADIUAFIvCcB98EyAuWWcwqVfo0NksZZCK/NRAX+zkQG/9NNB//YVwj/4XQU5+mZLVrdrjcAAAAAAAAA
        AAAAAAAAAAAAAM1ZAknCMAL3wCUB/8UvAf/JNgH/zkAC/9NKAv/bXwf85IIak+mwPxAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAA1nEQSctDBvfKOAD/z0EA/9ZPAf/fcQ7J5pgnMwAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADeghhG2WQN39xnCuTjhhpo2qgyAgAAAAAAAAAAAAAAAAAA
        AAAAAAAA/B8AAPgPAADgBwAAgAMAAAABAAAAAAAAAAAAAIAAAACAAQAAAAEAAIAAAADAAAAA4AEAAPAD
        AAD4DwAA/B8AAA==
</value>
  </data>
  <data name="label12.Location" type="System.Drawing.Point, System.Drawing">
    <value>881, 17</value>
  </data>
  <data name="label12.Size" type="System.Drawing.Size, System.Drawing">
    <value>28, 19</value>
  </data>
  <data name="&gt;&gt;statusStrip1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;dataGridView1.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="toolStripSeparator3.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 50</value>
  </data>
  <data name="&gt;&gt;label12.Parent" xml:space="preserve">
    <value>PnlSalesRep</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator3.Name" xml:space="preserve">
    <value>toolStripSeparator3</value>
  </data>
  <data name="BtnClose.Size" type="System.Drawing.Size, System.Drawing">
    <value>108, 48</value>
  </data>
  <data name="&gt;&gt;panel1.Name" xml:space="preserve">
    <value>panel1</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>sales_rep</value>
  </data>
  <data name="&gt;&gt;label1.Name" xml:space="preserve">
    <value>label1</value>
  </data>
  <data name="BtnDelete.Size" type="System.Drawing.Size, System.Drawing">
    <value>101, 48</value>
  </data>
  <data name="label12.Text" xml:space="preserve">
    <value>بحث</value>
  </data>
  <data name="TxtSearch.Size" type="System.Drawing.Size, System.Drawing">
    <value>249, 25</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="dataGridView1.TabIndex" type="System.Int32, mscorlib">
    <value>80</value>
  </data>
  <data name="&gt;&gt;statusStrip1.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="&gt;&gt;BtnEdit.Name" xml:space="preserve">
    <value>BtnEdit</value>
  </data>
  <data name="label1.Font" type="System.Drawing.Font, System.Drawing">
    <value>Ara Hamah Kilania, 16pt, style=Bold</value>
  </data>
  <data name="panel1.Size" type="System.Drawing.Size, System.Drawing">
    <value>934, 35</value>
  </data>
  <data name="BtnClose.Font" type="System.Drawing.Font, System.Drawing">
    <value>Droid Arabic Kufi, 7pt, style=Bold</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>مناديب المبيعات</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="BtnEdit.ImageScaling" type="System.Windows.Forms.ToolStripItemImageScaling, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="toolStrip1.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="BtnDelete.AccessibleName" xml:space="preserve">
    <value>حذف مندوب</value>
  </data>
  <data name="panel1.TabIndex" type="System.Int32, mscorlib">
    <value>60</value>
  </data>
  <data name="&gt;&gt;statusStrip1.Type" xml:space="preserve">
    <value>System.Windows.Forms.StatusStrip, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterScreen</value>
  </data>
  <data name="&gt;&gt;label1.Parent" xml:space="preserve">
    <value>panel1</value>
  </data>
  <data name="&gt;&gt;label1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;TxtSearch.Parent" xml:space="preserve">
    <value>PnlSalesRep</value>
  </data>
  <data name="&gt;&gt;label1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="BtnDelete.ImageTransparentColor" type="System.Drawing.Color, System.Drawing">
    <value>Magenta</value>
  </data>
  <data name="&gt;&gt;PnlSalesRep.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="BtnEdit.AccessibleName" xml:space="preserve">
    <value>تعديل مندوب</value>
  </data>
  <data name="PnlSalesRep.TabIndex" type="System.Int32, mscorlib">
    <value>562</value>
  </data>
  <data name="BtnClose.ImageScaling" type="System.Windows.Forms.ToolStripItemImageScaling, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="toolStrip1.TabIndex" type="System.Int32, mscorlib">
    <value>563</value>
  </data>
  <data name="label1.Size" type="System.Drawing.Size, System.Drawing">
    <value>129, 28</value>
  </data>
  <data name="PnlSalesRep.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="BtnNew.ImageScaling" type="System.Windows.Forms.ToolStripItemImageScaling, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="BtnClose.Text" xml:space="preserve">
    <value>خروج  Esc</value>
  </data>
  <data name="toolStripStatusLabel1.Font" type="System.Drawing.Font, System.Drawing">
    <value>Segoe UI, 10pt</value>
  </data>
  <data name="toolStripStatusLabel1.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 25</value>
  </data>
  <data name="&gt;&gt;PnlSalesRep.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="label12.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Right</value>
  </data>
  <data name="&gt;&gt;label12.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="toolStripSeparator2.AccessibleName" xml:space="preserve">
    <value>حذف مندوب</value>
  </data>
  <data name="BtnDelete.ImageScaling" type="System.Windows.Forms.ToolStripItemImageScaling, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="&gt;&gt;dataGridView1.Type" xml:space="preserve">
    <value>System.Windows.Forms.DataGridView, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="statusStrip1.TabIndex" type="System.Int32, mscorlib">
    <value>94</value>
  </data>
  <data name="BtnNew.Size" type="System.Drawing.Size, System.Drawing">
    <value>104, 48</value>
  </data>
  <data name="BtnEdit.Size" type="System.Drawing.Size, System.Drawing">
    <value>106, 48</value>
  </data>
  <data name="toolStrip1.Size" type="System.Drawing.Size, System.Drawing">
    <value>934, 50</value>
  </data>
  <data name="PnlSalesRep.Size" type="System.Drawing.Size, System.Drawing">
    <value>934, 50</value>
  </data>
  <data name="&gt;&gt;toolStripStatusLabel1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripStatusLabel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="dataGridView1.Size" type="System.Drawing.Size, System.Drawing">
    <value>934, 395</value>
  </data>
  <data name="label1.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="BtnNew.Font" type="System.Drawing.Font, System.Drawing">
    <value>Droid Arabic Kufi, 7pt, style=Bold</value>
  </data>
  <data name="toolStripSeparator1.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 50</value>
  </data>
  <data name="$this.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="&gt;&gt;BtnNew.Name" xml:space="preserve">
    <value>BtnNew</value>
  </data>
  <data name="toolStrip1.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 35</value>
  </data>
  <data name="PnlSalesRep.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 85</value>
  </data>
  <data name="&gt;&gt;dataGridView1.Name" xml:space="preserve">
    <value>dataGridView1</value>
  </data>
  <data name="TxtSearch.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Right</value>
  </data>
  <data name="BtnDelete.Text" xml:space="preserve">
    <value>حذف  F3</value>
  </data>
  <data name="panel1.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Top</value>
  </data>
  <data name="&gt;&gt;TxtSearch.Name" xml:space="preserve">
    <value>TxtSearch</value>
  </data>
  <data name="BtnEdit.Text" xml:space="preserve">
    <value>تعديل  F2</value>
  </data>
  <data name="&gt;&gt;dataGridView1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="statusStrip1.Size" type="System.Drawing.Size, System.Drawing">
    <value>934, 30</value>
  </data>
  <data name="$this.Font" type="System.Drawing.Font, System.Drawing">
    <value>Droid Arabic Kufi, 7pt, style=Bold</value>
  </data>
  <data name="&gt;&gt;panel1.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="TxtSearch.Location" type="System.Drawing.Point, System.Drawing">
    <value>629, 14</value>
  </data>
  <data name="&gt;&gt;TxtSearch.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;statusStrip1.Name" xml:space="preserve">
    <value>statusStrip1</value>
  </data>
  <data name="&gt;&gt;BtnClose.Name" xml:space="preserve">
    <value>BtnClose</value>
  </data>
  <data name="label1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top</value>
  </data>
  <data name="label1.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="BtnNew.Text" xml:space="preserve">
    <value>جديد   F1</value>
  </data>
  <data name="statusStrip1.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 531</value>
  </data>
  <data name="&gt;&gt;BtnClose.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label12.Name" xml:space="preserve">
    <value>label12</value>
  </data>
  <data name="TxtSearch.TabIndex" type="System.Int32, mscorlib">
    <value>78</value>
  </data>
  <data name="&gt;&gt;timer1.Name" xml:space="preserve">
    <value>timer1</value>
  </data>
  <data name="&gt;&gt;PnlSalesRep.Name" xml:space="preserve">
    <value>PnlSalesRep</value>
  </data>
  <data name="label1.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="toolStripSeparator1.AccessibleName" xml:space="preserve">
    <value>تعديل مندوب</value>
  </data>
  <data name="&gt;&gt;toolStrip1.Name" xml:space="preserve">
    <value>toolStrip1</value>
  </data>
  <data name="BtnEdit.ImageTransparentColor" type="System.Drawing.Color, System.Drawing">
    <value>Magenta</value>
  </data>
  <data name="&gt;&gt;label12.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="label12.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label1.Location" type="System.Drawing.Point, System.Drawing">
    <value>436, 4</value>
  </data>
  <data name="&gt;&gt;BtnEdit.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="dataGridView1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left, Right</value>
  </data>
  <data name="&gt;&gt;BtnNew.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;panel1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="toolStrip1.AutoSize" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;toolStrip1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="toolStrip1.Text" xml:space="preserve">
    <value>toolStrip1</value>
  </data>
  <data name="&gt;&gt;panel1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="toolStripSeparator2.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 50</value>
  </data>
  <data name="BtnClose.ImageTransparentColor" type="System.Drawing.Color, System.Drawing">
    <value>Magenta</value>
  </data>
  <data name="BtnEdit.Font" type="System.Drawing.Font, System.Drawing">
    <value>Droid Arabic Kufi, 7pt, style=Bold</value>
  </data>
  <data name="statusStrip1.AutoSize" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="BtnNew.AccessibleName" xml:space="preserve">
    <value>اضافة مندوب</value>
  </data>
  <data name="&gt;&gt;TxtSearch.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="panel1.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator1.Name" xml:space="preserve">
    <value>toolStripSeparator1</value>
  </data>
  <data name="&gt;&gt;toolStrip1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="label1.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>TopCenter</value>
  </data>
  <data name="dataGridView1.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 135</value>
  </data>
  <data name="&gt;&gt;PnlSalesRep.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;timer1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Timer, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator2.Name" xml:space="preserve">
    <value>toolStripSeparator2</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator2.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="BtnEdit.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAO
        wwAADsMBx2+oZAAAAbxJREFUWEe1l9uRgjAYhSnBEvYN9wLYgco2YAm+7cyGGUsQ2QIsgRIsgWHhnRIs
        gRLcnEzAOAZy3TNzHhTyfyd/mBACldbHdpUWbU99M3S/PTU5L2MnB/jo7am98HJmeobX3edPs9FxWvzu
        cP99bLvnZfUkmzmdScUva2l9rBZp0VzZ2Lwp+d9qTbXdNACEMUZj59bcPkDdoRv4HSaHlzAhG5jdIEr1
        wNkE2BTteYTH2XkZZ7fR7+T6lnyv2I0qOGwTYNAyJuUDfHCUVVpw2DbAJJw7oMWVcNhmU1HBYQTgkLrD
        LGXGWvKa2tKBsyUYAmAD4WOdpQMPo6xLksPCewAjOOQzgDEc8hXACg75CGANh1wDOMEhlwBhRHIZUPQs
        HHIKEH71MuhgJRxyCSCDDhbhOKCwATLZBBhfqxMdEOE4jGA3ZQNlsgqAVyvdRiloPwfHFo7aXgOg+H3m
        pHz9yHYIwwLRYOKaA+w9wNOTTw8WWBJ++UH/EgBA1mrWBVJOwSHvAbDmAKMLYqun5D3AeIbTlPcApjIK
        YHPqmZP2hwlNdxFDoBOuNvo00z0V2xoT5KhpIYTYCU/W+DwPgj9YGMXllie2jQAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="BtnDelete.Font" type="System.Drawing.Font, System.Drawing">
    <value>Droid Arabic Kufi, 7pt, style=Bold</value>
  </data>
  <data name="&gt;&gt;BtnDelete.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;BtnDelete.Name" xml:space="preserve">
    <value>BtnDelete</value>
  </data>
  <data name="&gt;&gt;toolStripStatusLabel1.Name" xml:space="preserve">
    <value>toolStripStatusLabel1</value>
  </data>
  <data name="label12.TabIndex" type="System.Int32, mscorlib">
    <value>77</value>
  </data>
  <data name="statusStrip1.Text" xml:space="preserve">
    <value>statusStrip1</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator3.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;toolStrip1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStrip, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="$this.Language" type="System.Globalization.CultureInfo, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>en-US</value>
  </metadata>
  <metadata name="timer1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>133, 17</value>
  </metadata>
  <metadata name="statusStrip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>220, 17</value>
  </metadata>
  <metadata name="toolStrip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>633, 17</value>
  </metadata>
</root>