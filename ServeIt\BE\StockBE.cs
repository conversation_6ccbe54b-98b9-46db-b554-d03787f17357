﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ServeIt.BE
{
    class StockBE : ProductBE
    {
        public string StockId { set; get; }
        public string CostCenterID { get; set; }
        public string Name { set; get; }
        public string StockEmpName { set; get; }
        public string location { set; get; }
        public bool Active { set; get; }
        public bool Default { set; get; }
        public DateTime? DateFrom { get; set; }
        public DateTime? DateTo { get; set; }
        public DateTime? ExpireyDate { get; set; }
        public int PriceSystem { get; set; }
        public string User_Name { get; set; }
        public bool ZeroAccounts { get; set; }
        public int ExpireyDaysLimit { get; set; }
    }
}
