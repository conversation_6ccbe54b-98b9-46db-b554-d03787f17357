using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ServeIT.Models.Entities;

namespace ServeIT.Database.Configurations;

public class TreasuryConfiguration : IEntityTypeConfiguration<Treasury>
{
    public void Configure(EntityTypeBuilder<Treasury> builder)
    {
        builder.HasKey(t => t.Id);

        builder.Property(t => t.Name)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(t => t.Location)
            .HasMaxLength(200);

        builder.Property(t => t.InitialBalance)
            .HasColumnType("decimal(18,2)");

        // Configure relationship with CostCenter
        builder.HasOne(t => t.CostCenter)
            .WithMany()
            .HasForeignKey(t => t.CostCenterId)
            .OnDelete(DeleteBehavior.SetNull);

        // Configure relationship with Invoices
        builder.HasMany(t => t.Invoices)
            .WithOne(i => i.Treasury)
            .HasForeignKey(i => i.TreasuryId)
            .OnDelete(DeleteBehavior.SetNull);

        // Configure relationship with TreasuryPermissions
        builder.HasMany(t => t.TreasuryPermissions)
            .WithOne(tp => tp.Treasury)
            .HasForeignKey(tp => tp.TreasuryId)
            .OnDelete(DeleteBehavior.NoAction);
    }
}
