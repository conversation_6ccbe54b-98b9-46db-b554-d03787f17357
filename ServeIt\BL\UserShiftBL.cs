﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ServeIt.BL
{
    class UserWorkShiftBL
    {
        public string ShiftID { get; set; }
        public string UserName { get; set; }
        public string TreasuryID { get; set; }
        public string CloseTreasuryID { get; set; }
        public DateTime ShiftDate { get; set; }
        public bool? ShiftStatus { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public decimal PreviousBalance { get; set; }
        public decimal Sales { get; set; }
        public decimal NetworkSales { get; set; }
        public decimal OtherSales { get; set; }
        public decimal CollectedInstallment { get; set; }
        public decimal SalesReturns { get; set; }
        public decimal Purchases { get; set; }
        public decimal PurchasesReturn { get; set; }
        public decimal Expenses { get; set; }
        public decimal CashDeposit { get; set; }
        public decimal PaymentVouchers { get; set; }
        public decimal ReceiptVouchers { get; set; }
        public decimal RemittanceVouchersIn { get; set; }
        public decimal RemittanceVouchersOut { get; set; }
        public decimal CashWithdrawals { get; set; }
        public decimal CashRemaining { get; set; }
        public decimal NetCashDrawer { get; set; }
        public DateTime? DateForm { get; set; }
        public DateTime? DateTo { get; set; }

        public string AddOrUpdateUserWorkShift(UserWorkShiftBL usr_shft_bl)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[26];
                para[0] = new SqlParameter("@check","a");
                para[1] = new SqlParameter("@shift_id",usr_shft_bl.ShiftID);
                para[2] = new SqlParameter("@user_name",usr_shft_bl.UserName);
                para[3] = new SqlParameter("@treasury_id",usr_shft_bl.TreasuryID);
                para[4] = new SqlParameter("@close_teasury_id",usr_shft_bl.CloseTreasuryID);
                para[5] = new SqlParameter("@shift_date",usr_shft_bl.ShiftDate);
                para[6] = new SqlParameter("@shift_status",usr_shft_bl.ShiftStatus);
                para[7] = new SqlParameter("@start_date",usr_shft_bl.StartDate);
                para[8] = new SqlParameter("@end_date",usr_shft_bl.EndDate);
                para[9] = new SqlParameter("@previous_balances",usr_shft_bl.PreviousBalance);
                para[10] = new SqlParameter("@sales",usr_shft_bl.Sales);
                para[11] = new SqlParameter("@network_sales",usr_shft_bl.NetworkSales);
                para[12] = new SqlParameter("@other_sales", usr_shft_bl.OtherSales);
                para[13] = new SqlParameter("@collected_installments",usr_shft_bl.CollectedInstallment);
                para[14] = new SqlParameter("@sales_return",usr_shft_bl.SalesReturns);
                para[15] = new SqlParameter("@purchases",usr_shft_bl.Purchases);
                para[16] = new SqlParameter("@purchases_return",usr_shft_bl.PurchasesReturn);
                para[17] = new SqlParameter("@expenses",usr_shft_bl.Expenses);
                para[18] = new SqlParameter("@cash_deposit_voucher", usr_shft_bl.CashDeposit);
                para[19] = new SqlParameter("@payment_vouchers",usr_shft_bl.PaymentVouchers);
                para[20] = new SqlParameter("@receipt_vouchers",usr_shft_bl.ReceiptVouchers);
                para[21] = new SqlParameter("@remittance_vouchers_in",usr_shft_bl.RemittanceVouchersIn);
                para[22] = new SqlParameter("@remittance_vouchers_out", usr_shft_bl.RemittanceVouchersOut);
                para[23] = new SqlParameter("@cash_withdrawals", usr_shft_bl.CashWithdrawals);
                para[24] = new SqlParameter("@cash_remaining",usr_shft_bl.CashRemaining);
                para[25] = new SqlParameter("@net_cash_drawer",usr_shft_bl.NetCashDrawer);

                return DAL.InsUpdDel("ManageUserWorkShift", para);
            }
        }

        public string UncloseUserShift(string ShiftID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@shift_id",ShiftID);
                return DAL.InsUpdDel("UncloseUserShift", para);
            }
        }

        public string GetMaxUserWorkShiftID()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@check", "m");
                return DAL.GetValue("ManageUserWorkShift", para);
            }
        }

        public string DeleteUserWorkShift(string ShiftID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "d");
                para[1] = new SqlParameter("@shift_id", ShiftID);
                

                return DAL.InsUpdDel("ManageUserWorkShift", para);
            }
        }

        public DataTable GetUserWorkShiftByID(string ShiftID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "g");
                para[1] = new SqlParameter("@shift_id",ShiftID );
                return DAL.GetData("ManageUserWorkShift", para);
            }
        }

        public DataTable GetUserWorkShiftIDByUserNameAndDate(string userName, DateTime CurrentDate)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@user_name", userName);
                para[1] = new SqlParameter("@current_date", CurrentDate);
                return DAL.GetData("GetUserWorkShiftIDByUserNameAndDate", para);
            }
        }

        public DataTable SearchUserWorkShift(string UserName,bool? ShiftStatus, DateTime? DateFrom, DateTime? DateTo)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[5];
                para[0] = new SqlParameter("@check", "s");
                para[1] = new SqlParameter("@user_name", UserName);
                para[2] = new SqlParameter("@shift_status", ShiftStatus);
                para[3] = new SqlParameter("@date_from", DateFrom);
                para[4] = new SqlParameter("@date_to", DateTo);
                return DAL.GetData("ManageUserWorkShift", para);
            }
        }

        public DataTable CalculateUserShiftCash(string UserName,string TreasuryID, DateTime StartDate, DateTime EndDate)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[4];
                para[0] = new SqlParameter("@treasury_id", TreasuryID);
                para[1] = new SqlParameter("@user_name", UserName);
                para[2] = new SqlParameter("@start_date", StartDate);
                para[3] = new SqlParameter("@end_date", EndDate);
                return DAL.GetData("CalculateUserShiftCash", para);
            }
        }

        public DataTable CheckIfThereIsWorkShiftExistAndOpenedAndGetResult(string UserName, DateTime MyDate)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@user_name", UserName);
                para[1] = new SqlParameter("@date", MyDate);
                return DAL.GetData("CheckIfThereIsWorkShiftExistAndOpenedAndGetResult", para);
            }
        }

        public DataTable PrintUserWorkShift(string ShiftID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@shift_id", ShiftID);
               
                return DAL.GetData("RPTUserWorkShiftPrint", para);
            }
        }
    }
}
