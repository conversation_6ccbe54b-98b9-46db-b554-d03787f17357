﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="label1.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="label1.Location" type="System.Drawing.Point, System.Drawing">
    <value>409, 4</value>
  </data>
  <data name="label1.Size" type="System.Drawing.Size, System.Drawing">
    <value>102, 28</value>
  </data>
  <data name="label1.Text" xml:space="preserve">
    <value>Treasuries</value>
  </data>
  <data name="TxtSearch.Location" type="System.Drawing.Point, System.Drawing">
    <value>101, 14</value>
  </data>
  <data name="TxtSearch.Size" type="System.Drawing.Size, System.Drawing">
    <value>224, 24</value>
  </data>
  <data name="label3.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label3.Location" type="System.Drawing.Point, System.Drawing">
    <value>47, 17</value>
  </data>
  <data name="label3.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 17</value>
  </data>
  <data name="label3.Text" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="toolStrip1.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Inherit</value>
  </data>
  <data name="BtnNew.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 9.5pt, style=Bold</value>
  </data>
  <data name="BtnNew.Size" type="System.Drawing.Size, System.Drawing">
    <value>103, 48</value>
  </data>
  <data name="BtnNew.Text" xml:space="preserve">
    <value>New  F1</value>
  </data>
  <data name="BtnEdit.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 9.5pt, style=Bold</value>
  </data>
  <data name="BtnEdit.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAG8SURBVFhHtZfbkYIwGIUpwRL2DfcC2IHKNmAJvu3MhhlL
        ENkCLIESLIFh4Z0SLIES3JxMwDgGct0zcx4U8n8nf5gQApXWx3aVFm1PfTN0vz01OS9jJwf46O2pvfBy
        ZnqG193nT7PRcVr87nD/fWy752X1JJs5nUnFL2tpfawWadFc2di8Kfnfak213TQAhDFGY+fW3D5A3aEb
        +B0mh5cwIRuY3SBK9cDZBNgU7XmEx9l5GWe30e/k+pZ8r9iNKjhsE2DQMiblA3xwlFVacNg2wCScO6DF
        lXDYZlNRwWEE4JC6wyxlxlrymtrSgbMlGAJgA+FjnaUDD6OsS5LDwnsAIzjkM4AxHPIVwAoO+QhgDYdc
        AzjBIZcAYURyGVD0LBxyChB+9TLoYCUccgkggw4W4TigsAEy2QQYX6sTHRDhOIxgN2UDZbIKgFcr3UYp
        aD8HxxaO2l4DoPh95qR8/ch2CMMC0WDimgPsPcDTk08PFlgSfvlB/xIAQNZq1gVSTsEh7wGw5gCjC2Kr
        p+Q9wHiG05T3AKYyCmBz6pmT9ocJTXcRQ6ATrjb6NNM9FdsaE+SoaSGE2AlP1vg8D4I/WBjF5ZYnto0A
        AAAASUVORK5CYII=
</value>
  </data>
  <data name="BtnEdit.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 48</value>
  </data>
  <data name="BtnEdit.Text" xml:space="preserve">
    <value>Edit  F2</value>
  </data>
  <data name="BtnDelete.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 9.5pt, style=Bold</value>
  </data>
  <data name="BtnDelete.Size" type="System.Drawing.Size, System.Drawing">
    <value>118, 48</value>
  </data>
  <data name="BtnDelete.Text" xml:space="preserve">
    <value>Delete  F3</value>
  </data>
  <data name="BtnClose.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 9.5pt, style=Bold</value>
  </data>
  <data name="BtnClose.Size" type="System.Drawing.Size, System.Drawing">
    <value>117, 48</value>
  </data>
  <data name="BtnClose.Text" xml:space="preserve">
    <value>Close  Esc</value>
  </data>
  <data name="$this.Font" type="System.Drawing.Font, System.Drawing">
    <value>Calibri, 10pt, style=Bold</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAEBAAAAEAIABoBAAAFgAAACgAAAAQAAAAIAAAAAEAIAAAAAAAAAQAACMuAAAjLgAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAB1+bUIh/nFgJH50eyM+dXVe/nIMgAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAH/5vABq9qxIbvSs227zr/9q87T/ZvO8/2L1we5T9LQzAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAGP2px1e8p2sWe+W/1Ptkv9N65H/R+uS/0HrlP8865X/Oe6d7jLw
        nDMAAAAAAAAAAAAAAAAAAAAAafetBVrynHRU75LzTOuI/0Tpg/8953//OOeA+UfngqB9012MS+B0kB3m
        fe4a53/uGeuHMwAAAAAAAAAAVvOeNVLvk9NL64j/Q+iB/zvme/8z5Xf/NOd9wa+0LozbjALq2IQA/9uO
        BPmExj96B95k/gjha+4N6HozAAAAAEjujt5B6ID/OeZ6/zLkdf8u5XjnbtJbityPA8TWgAD/1X0A/9V9
        AP/XggH/xq8cbQDbW/oA2FX/Bd5j7g3ndDM57YZ6MOV2+yrkcvk64nGhzZwSndeCAfrUegD/1HoA/9R6
        AP/YhQLzsKsgjgLbWcEA1U//ANVQ/wDWUf8H32TbAAAAAC3sghDKnxdh2IEB49J1AP/SdAD/0nMA/9N3
        AP/YjQi5NdFMkAHWUPMA0kr/ANJK/wDSSv8B1U7+CuBjjN6mAgDZggKl0GwA/85nAP/OZwD/zmgA/9R3
        AuuWqySKA9VMyQDPQ/8AzkL/AM5C/wDOQ/8E1k3ZDN9gRQDqcwDenQAF0GkA8cdTAP/HUwD/yFUA/813
        B7IiyzmZAMs5+gDHNf8AxzX/AMc1/wHMOvYpz0WU1KEsWemmKycAAAAAAAAAANNvAEfERgD3vjkA/8hR
        A+cHzS2IAL8l/wC+JP8AviT/AMEm/wnLMLqskSGX2W4X89lqG//geyD76aEpRQAAAAAAAAAAzFoASLwv
        APe/NAL2NLMZiAC7Ff4AuBX/AcAa4WCnHY/UXg3T0VIN/9VXD//aYBL/4HAX/+qjMV8AAAAAAAAAAAAA
        AADIUAFIvCcB98EyAuWWcwqVfo0NksZZCK/NRAX+zkQG/9NNB//YVwj/4XQU5+mZLVrdrjcAAAAAAAAA
        AAAAAAAAAAAAAM1ZAknCMAL3wCUB/8UvAf/JNgH/zkAC/9NKAv/bXwf85IIak+mwPxAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAA1nEQSctDBvfKOAD/z0EA/9ZPAf/fcQ7J5pgnMwAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADeghhG2WQN39xnCuTjhhpo2qgyAgAAAAAAAAAAAAAAAAAA
        AAAAAAAA/B8AAPgPAADgBwAAgAMAAAABAAAAAAAAAAAAAIAAAACAAQAAAAEAAIAAAADAAAAA4AEAAPAD
        AAD4DwAA/B8AAA==
</value>
  </data>
  <data name="$this.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="$this.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Inherit</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="$this.RightToLeftLayout" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>Treasuries</value>
  </data>
</root>