using System.ComponentModel.DataAnnotations;

namespace ServeIT.Models.Entities;

public class Invoice : BaseEntity<string>
{
    [Required]
    [StringLength(50)]
    public override string Id { get; set; } = string.Empty;

    [StringLength(50)]
    public string? Number { get; set; }

    [StringLength(50)]
    public string? ReferenceSaleInvoiceId { get; set; }

    public int? CostCenterId { get; set; }

    public DateTime Date { get; set; }

    public DateTime InvoiceDate { get; set; }

    public DateTime TransactionDate { get; set; }

    public int? StockId { get; set; }

    public int CustomerId { get; set; }

    [StringLength(200)]
    public string? CustomerName { get; set; }

    public InvoiceKind Kind { get; set; }

    public InvoiceKind InvoiceKind { get; set; }

    public PaymentKind PaymentKind { get; set; }

    public OrderType? OrderType { get; set; }

    public OrderStatus? OrderStatus { get; set; }

    public int? DeliveryId { get; set; }

    public int? TableId { get; set; }

    public int? TableMembersCount { get; set; }

    [StringLength(50)]
    public string? PriceSystem { get; set; }

    public int? TreasuryId { get; set; }

    public int? SalesRepresentativeId { get; set; }

    public decimal Paid { get; set; }

    public decimal CustomerRecipient { get; set; }

    public int InstallmentCount { get; set; }

    public decimal InstallmentPaidValue { get; set; }

    public InstallmentMethod InstallmentMethod { get; set; }

    public DiscountKind DiscountKind { get; set; }

    public decimal Discount { get; set; }

    public decimal Discount2 { get; set; }

    public decimal SalesTax { get; set; }

    public decimal Addition { get; set; }

    public decimal Service { get; set; }

    public decimal NetTotal { get; set; }

    public decimal SubTotal { get; set; }

    public decimal TotalVAT { get; set; }

    public decimal DiscountAmount { get; set; }

    public decimal TotalAmount { get; set; }

    [StringLength(1000)]
    public string? Notes { get; set; }

    public InvoiceCostingSystem InvoiceCostingSystem { get; set; }

    public decimal TurnOverRate { get; set; }

    // Navigation Properties
    public virtual CostCenter? CostCenter { get; set; }
    public virtual Customer Customer { get; set; } = null!;
    public virtual Stock? Stock { get; set; }
    public virtual Treasury? Treasury { get; set; }
    public virtual SalesRepresentative? SalesRepresentative { get; set; }
    public virtual ICollection<InvoiceDetail> InvoiceDetails { get; set; } = new List<InvoiceDetail>();
}

public enum InvoiceKind
{
    Sale = 1,
    Purchase = 2,
    SaleReturn = 3,
    PurchaseReturn = 4,
    Return = 5,
    Quote = 6
}

public enum PaymentKind
{
    Cash = 1,
    Credit = 2,
    Installment = 3
}

public enum OrderType
{
    DineIn = 1,
    Takeaway = 2,
    Delivery = 3
}

public enum OrderStatus
{
    Pending = 1,
    InProgress = 2,
    Ready = 3,
    Delivered = 4,
    Cancelled = 5
}

public enum InstallmentMethod
{
    Monthly = 1,
    Weekly = 2,
    Daily = 3
}

public enum DiscountKind
{
    Percentage = 1,
    Amount = 2
}

public enum InvoiceCostingSystem
{
    FIFO = 1,
    LIFO = 2,
    Average = 3
}
