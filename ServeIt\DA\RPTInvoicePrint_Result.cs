//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace ServeIt.DA
{
    using System;
    
    public partial class RPTInvoicePrint_Result
    {
        public Nullable<int> inv_id { get; set; }
        public Nullable<int> inv_no { get; set; }
        public string inv_kind { get; set; }
        public string inv_kind_name { get; set; }
        public string order_type { get; set; }
        public string table_name { get; set; }
        public string delivery_name { get; set; }
        public Nullable<int> kitchen_id { get; set; }
        public string kitchen_name { get; set; }
        public string printer_name { get; set; }
        public Nullable<System.DateTime> inv_date { get; set; }
        public string cust_name { get; set; }
        public string cust_mobile { get; set; }
        public string cust_address { get; set; }
        public string treas_name { get; set; }
        public string sale_rep { get; set; }
        public string pay_type { get; set; }
        public Nullable<decimal> paid { get; set; }
        public Nullable<decimal> cust_receipient { get; set; }
        public Nullable<decimal> service { get; set; }
        public Nullable<decimal> discount1 { get; set; }
        public Nullable<decimal> discount2 { get; set; }
        public Nullable<decimal> addition { get; set; }
        public Nullable<decimal> sale_tax { get; set; }
        public string notes { get; set; }
        public Nullable<long> pro_id { get; set; }
        public string pro_name { get; set; }
        public string unit_name { get; set; }
        public string stock_name { get; set; }
        public Nullable<decimal> quantity { get; set; }
        public Nullable<decimal> price { get; set; }
        public string full_quantity { get; set; }
        public Nullable<decimal> pro_discount { get; set; }
        public string pro_notes { get; set; }
        public Nullable<decimal> nettotal { get; set; }
        public Nullable<decimal> previouse_balance { get; set; }
        public string user_nam { get; set; }
        public int company_id { get; set; }
        public string company_name { get; set; }
        public string activity { get; set; }
        public Nullable<System.DateTime> first_period_date { get; set; }
        public Nullable<System.DateTime> last_period_date { get; set; }
        public string commercial_registration { get; set; }
        public string tax_card { get; set; }
        public string address { get; set; }
        public string phone { get; set; }
        public string mobile { get; set; }
        public string Email { get; set; }
        public byte[] Logo { get; set; }
        public byte[] background { get; set; }
        public string inv_print_footer_txt { get; set; }
    }
}
