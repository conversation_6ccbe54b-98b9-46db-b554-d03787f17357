﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;

namespace ServeIt.BL
{
    class ManagementBL
    {
        public string ManagementID { get; set; }
        public string ManagementName { get; set; }
        public string Notes { get; set; }
        public string UserName { get; set; }

        public string AddOrUpdateManagement(ManagementBL mang_bl)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[5];
                para[0] = new SqlParameter("@check", "a");
                para[1] = new SqlParameter("@management_id", mang_bl.ManagementID);
                para[2] = new SqlParameter("@management_name", mang_bl.ManagementName);
                para[3] = new SqlParameter("@notes", mang_bl.Notes);
                para[4] = new SqlParameter("@user_name", mang_bl.UserName);
                return DAL.InsUpdDel("ManageManagement", para);
            }
        }

        public string DeleteManagement(string ManagementID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "d");
                para[1] = new SqlParameter("@management_id", ManagementID);
                return DAL.InsUpdDel("ManageManagement", para);
            }
        }

        public string GetMaxManagementID()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@check", "m");
                return DAL.GetValue("ManageManagement", para);
            }
        }

        public DataTable GetManagementByID(string ManagementID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "g");
                para[1] = new SqlParameter("@management_id", ManagementID);
                return DAL.GetData("ManageManagement", para);
            }
        }

        public DataTable SearchManagement(string ManagementName)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "s");
                para[1] = new SqlParameter("@management_name", ManagementName);
                return DAL.GetData("ManageManagement", para);
            }
        }
    }
}
