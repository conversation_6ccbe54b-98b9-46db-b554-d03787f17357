﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ServeIt.BE
{
    class InstallmentBE
    {
        public string InstallmentTotalValue { get; set; }
        public string InstallmentDeposit { get; set; }
        public string InstallmentCount { get; set; }
        public string Value { get; set; }
        public string InstallmentMethod { get; set; }
        public string PaidID { get; set; }
        public string InvID { get; set; }
        public string CustID { get; set; }
        public DateTime? RequiredDate { get; set; }
        public DateTime? CollectingDate { get; set; }
        public decimal Paid { get; set; }
        public bool PaidStatus { get; set; }
        public string TreasuryID { get; set; }
        public string BankAccountID { get; set; }
        public string UserName { get; set; }
    }
}
