﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ServeIt.BL
{
    class Unit
    {

        //    methods manages product units 

        #region Manage Unit

        public string AddOrUpdateUnit(BE.Unit myunit)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[4];
                para[0] = new SqlParameter("@check", "a");
                para[1] = new SqlParameter("@unit_id", myunit.Unit_Id);
                para[2] = new SqlParameter("@unit_name", myunit.Unit_Name);
                para[3] = new SqlParameter("@user_name", myunit.User_Name);
                return DAL.InsUpdDel("ManageUnit", para);
            }
        }


        public string DeleteUnit(BE.Unit myunit)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "d");
                para[1] = new SqlParameter("@unit_id", myunit.Unit_Id);
                return DAL.InsUpdDel("ManageUnit", para);
            }
        }


        public DataTable GetAllUnit()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetData("getallunit", null);
            }
        }

        public DataTable GetPreviousUnitID(string CurrentID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@current_id", CurrentID);
                return DAL.GetData("GetPreviousUnitID", para);
            }
        }

        public DataTable GetNextUnitID(string CurrentID)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@current_id", CurrentID);
                return DAL.GetData("GetNextUnitID", para);
            }
        }

        public string GetMaxUnitId()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetValue("getmaxunitid");
            }
        }

        public DataTable GetUnitDetailsByUnitID(string UnitID) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@unit_id", UnitID);
                return DAL.GetData("GetUnitDetailsByUnitID", para);
            }
        }

        public DataTable GetProUnitDetailsByUnitIDAndProID(string ProID, string UnitID) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@pro_id", ProID);
                para[1] = new SqlParameter("@unit_id", UnitID);
                return DAL.GetData("GetProUnitDetailsByUnitIDAndProID", para);
            }
        }

        #endregion
    }
}
