﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ServeIt.BL
{
    class BankBranchBL
    {
        
        public string AddOrUpdateBankBranch(BE.BankBranchBE bankbrchbe)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[9];
                para[0] = new SqlParameter("@check", "a");
                para[1] = new SqlParameter("@id", bankbrchbe.Id);
                para[2] = new SqlParameter("@name", bankbrchbe.BranchName);
                para[3] = new SqlParameter("@bank_id", bankbrchbe.BankId);
                para[4] = new SqlParameter("@phone1", bankbrchbe.Phone1);
                para[5] = new SqlParameter("@phone2", bankbrchbe.phone2);
                para[6] = new SqlParameter("@fax", bankbrchbe.fax);
                para[7] = new SqlParameter("@address", bankbrchbe.Address);
                para[8] = new SqlParameter("@note", bankbrchbe.Note);
                return DAL.InsUpdDel("managebankbranch", para);
            }
        }


        public string DeleteBankBranch(BE.BankBranchBE bankbrchbe)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "d");
                para[1] = new SqlParameter("@id", bankbrchbe.Id);
                return DAL.InsUpdDel("managebankbranch", para);
            }
        }


        public string GetMaxBankBranchId() 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetValue("getmaxbankbranchid");
            }
        }


        public DataTable GetAllBankBranches() 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetData("GetAllBankBranch", null);
            }
        }


        public DataTable GetBankBranchByBankId(BE.BankBranchBE branchbe)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("bank_id", branchbe.BankId);
                return DAL.GetData("getbankbranchbybankid", para);
            }
        }
    }
}
