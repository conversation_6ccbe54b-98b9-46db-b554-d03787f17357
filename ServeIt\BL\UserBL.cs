﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ServeIt.BL
{
    class UserBL
    {

        public DataTable GetAllUser() 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetData("GetAllUser", null);
            }
        }

        public DataTable GetAllUserNames()
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                return DAL.GetData("GetAllUserNames", null);
            }
        }

        public DataTable SearchUser(String UserName) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@user_name", UserName);
                return DAL.GetData("SearchUser", para);
            }
        }

        public DataTable GetUserDetailsByUserName(string UserName) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@user_name", UserName);
                return DAL.GetData("GetUserDetailsByUserName", para);
            }
        }

        public string Login(BE.UserBE userbe)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@user_name",userbe.Name);
                para[1] = new SqlParameter("@password",userbe.Password);
                return DAL.GetValue("Login", para);
            }
        }

        public string CheckALterPassword(BE.UserBE userbe)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@user_name", userbe.Name);
                para[1] = new SqlParameter("@alter_password", userbe.AlterPassword);
                return DAL.GetValue("CheckAlterPassword", para);
            }
        }

        public string CheckDeletePassword(BE.UserBE userbe)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@user_name", userbe.Name);
                para[1] = new SqlParameter("@delete_password", userbe.DeletePassword);
                return DAL.GetValue("CheckDeletePassword", para);
            }
        }

        public string AddOrUpdateUser (BE.UserBE userbe)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[10];
                para[0] = new SqlParameter("@check","a");
                para[1] = new SqlParameter("@name", userbe.Name);
                para[2] = new SqlParameter("@password", userbe.Password);
                para[3] = new SqlParameter("@alter_password", userbe.AlterPassword);
                para[4] = new SqlParameter("@delete_password", userbe.DeletePassword);
                para[5] = new SqlParameter("@isactive", userbe.IsActive);
                para[6] = new SqlParameter("@job", userbe.Job);
                para[7] = new SqlParameter("@cost_center_id", userbe.CostCenterID);
                para[8] = new SqlParameter("@treasury_id", userbe.TreasuryID);
                para[9] = new SqlParameter("@cashier_invoice_name", userbe.CashierInvoiceName);
                return DAL.InsUpdDel("ManageUser", para);
            }
        }

        public DataTable GetUserInvoiceCashierName(string UserName)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@user_name", UserName);
                return DAL.GetData("GetUserInvoiceCashierName", para);
            }
        }

        public string DeleteUser(String UserName) 
        {
            using (DA.DataAccess DAl = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[2];
                para[0] = new SqlParameter("@check", "d");
                para[1] = new SqlParameter("@name", UserName);
                return DAl.InsUpdDel("ManageUser", para);
            }
        }

        public string AddUserPermission(BE.UserBE userbe) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[3];
                para[0] = new SqlParameter("@check", "a");
                para[1] = new SqlParameter("@user_name", userbe.Name);
                para[2] = new SqlParameter("@permission_name", userbe.PermissionName);
                return DAL.InsUpdDel("ManageUserPermissions", para);
            }
        }

        public string DeleteUserPermission(BE.UserBE userbe) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[3];
                para[0] = new SqlParameter("@check", "d");
                para[1] = new SqlParameter("@user_name", userbe.Name);
                para[2] = new SqlParameter("@permission_name", userbe.PermissionName);
                return DAL.InsUpdDel("ManageUserPermissions", para);
            }
        }

        public DataTable GetUserPermissions(string UserName) 
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@user_name", UserName);
                return DAL.GetData("GetUserPermissions", para);
            }
        }

        public DataTable CheckIfUserHaveSalePriceVisible(string UserName)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@user_name", UserName);
                return DAL.GetData("CheckIfUserHaveSalePriceVisible", para);
            }
        }

        public DataTable CheckIfUserHavePurchasePriceVisible(string UserName)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@user_name", UserName);
                return DAL.GetData("CheckIfUserHavePurchasePriceVisible", para);
            }
        }

        public DataTable CheckIfUserHaveChangeDiscountPermission(string UserName)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@user_name", UserName);
                return DAL.GetData("CheckIfUserHaveChangeDiscountPermission", para);
            }
        }

        public DataTable CheckIfUserHaveCreditInvociePermission(string UserName)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@user_name", UserName);
                return DAL.GetData("CheckIfUserHaveCreditInvociePermission", para);
            }
        }

        public DataTable CheckIfUserHaveChangePricePermission(string UserName)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@user_name", UserName);
                return DAL.GetData("CheckIfUserHaveChangePricePermission", para);
            }
        }

        public DataTable CheckIfUserHaveCostPricePermissionInReports(string UserName)
        {
            using (DA.DataAccess DAL = new DA.DataAccess())
            {
                SqlParameter[] para = new SqlParameter[1];
                para[0] = new SqlParameter("@user_name", UserName);
                return DAL.GetData("CheckIfUserHaveCostPricePermissionInReports", para);
            }
        }



    }
}
