using System.ComponentModel.DataAnnotations;

namespace ServeIT.Models.DTOs;

public class CustomerDto
{
    public int Id { get; set; }

    [Required(ErrorMessage = "Customer name is required")]
    [StringLength(100, ErrorMessage = "Customer name cannot exceed 100 characters")]
    public string Name { get; set; } = string.Empty;

    [StringLength(50)]
    public string? CustomerKind { get; set; }

    [Range(0, double.MaxValue, ErrorMessage = "Initial balance must be positive")]
    public decimal InitialBalance { get; set; }

    [StringLength(50)]
    public string? PriceSystem { get; set; }

    [StringLength(100)]
    public string? City { get; set; }

    [StringLength(100)]
    public string? Area { get; set; }

    [StringLength(500)]
    public string? Address { get; set; }

    [Phone(ErrorMessage = "Invalid mobile number format")]
    [StringLength(20)]
    public string? Mobile { get; set; }

    [Phone(ErrorMessage = "Invalid phone number format")]
    [StringLength(20)]
    public string? Phone { get; set; }

    public int? SalesRepresentativeId { get; set; }
    public string? SalesRepresentativeName { get; set; }

    public bool IsDefaultCustomer { get; set; }

    [StringLength(1000)]
    public string? Notes { get; set; }

    public bool IsActive { get; set; } = true;

    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string? CreatedBy { get; set; }
    public string? UpdatedBy { get; set; }
}
