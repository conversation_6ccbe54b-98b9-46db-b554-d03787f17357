using ServeIT.Models.DTOs;
using ServeIT.Models.Common;

namespace ServeIT.BlazorServer.Services;

public interface ICustomerApiService
{
    Task<ServiceResult<IEnumerable<CustomerDto>>> GetAllCustomersAsync();
    Task<ServiceResult<CustomerDto>> GetCustomerByIdAsync(int id);
    Task<ServiceResult<PagedResult<CustomerDto>>> GetCustomersPagedAsync(PagingParameters parameters);
    Task<ServiceResult<PagedResult<CustomerDto>>> SearchCustomersAsync(string searchTerm, PagingParameters parameters);
    Task<ServiceResult<CustomerDto>> CreateCustomerAsync(CustomerDto customer);
    Task<ServiceResult<CustomerDto>> UpdateCustomerAsync(int id, CustomerDto customer);
    Task<ServiceResult> DeleteCustomerAsync(int id);
    Task<ServiceResult<bool>> CheckMobileUniqueAsync(string mobile, int? excludeId = null);
    Task<ServiceResult<IEnumerable<CustomerDto>>> GetActiveCustomersAsync();
}
